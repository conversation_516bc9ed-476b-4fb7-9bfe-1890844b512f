image: python:3.10-slim

definitions:
  lint: &lint
    name: Run linting
    caches:
      - pip
    script:
      - apt-get update && apt-get install -y make git
      - pipe: atlassian/poetry-cli-setup:0.2.0
      - ./setup-poetry.sh
      - make auth-vapar
      - poetry install --only lint --no-root
      - make lint

  test: &test
    name: Run tests
    image:
      name: vaparbitbucket.azurecr.io/backend-tasks/tests:latest
      username: $AZURE_CONTAINER_REGISTRY_USERNAME
      password: $AZURE_CONTAINER_REGISTRY_PASSWORD
    caches:
      - pip
    script:
      - chmod u+x static/bin/NasscoConsoleValidator-1.8.1-linux-x64
      - apt-get update && apt-get install -y make git
      - pipe: atlassian/poetry-cli-setup:0.2.0
      - ./setup-poetry.sh
      - make auth-vapar
      - poetry install --with dev --no-root
      - make test

pipelines:
  pull-requests:
    '**':
      - parallel:
        - step: *lint
        - step: *test

  branches:
    release/development:
      - parallel:
        - step: *lint
        - step: *test
      - step:
          name: Deploy to development
          deployment: development
          trigger: manual
          caches:
              - pip
          script:
            - apt-get update && apt-get install -y make git curl lsb-release
            - pipe: atlassian/poetry-cli-setup:0.2.0
            - ./setup-poetry.sh
            - poetry self add poetry-plugin-export
            - make auth-vapar
            - poetry install --with dev --no-root
            - make install-az
            - az login --service-principal -u $AZURE_APP_ID -p $AZURE_CLIENT_SECRET --tenant $AZURE_TENANT_ID
            - az account set --subscription $AZURE_DEV_SUBSCRIPTION_ID
            - make deploy-dev

    release/staging:
        - parallel:
            - step: *lint
            - step: *test
        - step:
            name: Deploy to staging
            deployment: staging
            trigger: manual
            caches:
                - pip
            script:
                - apt-get update && apt-get install -y make git curl lsb-release
                - pipe: atlassian/poetry-cli-setup:0.2.0
                - ./setup-poetry.sh
                - poetry self add poetry-plugin-export
                - make auth-vapar
                - poetry install --with dev --no-root
                - make install-az
                - az login --service-principal -u $AZURE_APP_ID -p $AZURE_CLIENT_SECRET --tenant $AZURE_TENANT_ID
                - az account set --subscription $AZURE_SUBSCRIPTION_ID
                - make deploy-staging

    release/production:
        - parallel:
            - step: *lint
            - step: *test
        - parallel:
          - step:
              name: Deploy to production AU (default region)
              deployment: production
              trigger: manual
              caches:
                  - pip
              script:
                  - apt-get update && apt-get install -y make git curl lsb-release
                  - pipe: atlassian/poetry-cli-setup:0.2.0
                  - ./setup-poetry.sh
                  - poetry self add poetry-plugin-export
                  - make auth-vapar
                  - poetry install --with dev --no-root
                  - make install-az
                  - az login --service-principal -u $AZURE_APP_ID -p $AZURE_CLIENT_SECRET --tenant $AZURE_TENANT_ID
                  - az account set --subscription $AZURE_SUBSCRIPTION_ID
                  - make deploy-prod-au

          - step:
              name: Deploy to production UK
              deployment: production-uk
              trigger: manual
              caches:
                  - pip
              script:
                  - apt-get update && apt-get install -y make git curl lsb-release
                  - pipe: atlassian/poetry-cli-setup:0.2.0
                  - ./setup-poetry.sh
                  - poetry self add poetry-plugin-export
                  - make auth-vapar
                  - poetry install --with dev --no-root
                  - make install-az
                  - az login --service-principal -u $AZURE_APP_ID -p $AZURE_CLIENT_SECRET --tenant $AZURE_TENANT_ID
                  - az account set --subscription $AZURE_SUBSCRIPTION_ID
                  - make deploy-prod-uk
