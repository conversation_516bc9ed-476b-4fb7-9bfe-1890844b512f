import logging

import adlfs
import azure.durable_functions as df
import azure.functions as func
from azure.functions.blob import InputStream
from azure.storage.queue import BinaryBase64DecodePolicy, BinaryBase64EncodePolicy, QueueClient
from pydantic_settings import BaseSettings
from vapar.clients import api
from vapar.constants.exports import ExportFormat, ExportStatus, ExportStatusReason, ExportType
from vapar.constants.imports import (
    ImportStatusEnum,
    ImportStatusReasonEnum,
    ImportTypeEnum,
)
from vapar.core.exports import ExportRequestQueueMessage
from vapar.core.imports import ImportRequestQueueMessage
from vapar.lib.logging import rollbar_instrument, setup_azure_functions_logging

from common.models import ExportOutputFile, ExportStatusException

setup_azure_functions_logging()
log = logging.getLogger(__name__)

app = df.DFApp()


class ImportStorageSettings(BaseSettings):
    storage_account_conn_str: str
    imports_queue_name: str = "imports"


class ExportStorageSettings(BaseSettings):
    storage_account_conn_str: str


def unwrap_output(result: ExportOutputFile | ExportStatusReason) -> ExportOutputFile:
    """Raise 'ExportStatusException' if the value is a failure status reason, else return it"""
    if isinstance(result, ExportStatusReason):
        raise ExportStatusException(result)
    return result


@app.timer_trigger(schedule="%EXPORT_TIMEOUT_CLEANUP_NCRON_SCHEDULE%", arg_name="timer")
@rollbar_instrument
async def export_timeout_cleanup_timer_trigger(timer: func.TimerRequest):
    """
    A function that triggers a status change on exports that have timed out.
    """
    from activities import cleanup_export_timeout

    settings = cleanup_export_timeout.Settings()
    api_client = api.new_client()

    await cleanup_export_timeout.handler(api_client, settings)


@app.queue_trigger(arg_name="msg", queue_name="exports", connection="STORAGE_ACCOUNT_CONN_STR")
@app.durable_client_input(client_name="client", connection_name="STORAGE_ACCOUNT_CONN_STR")
@rollbar_instrument
async def export_queue_trigger(msg: func.QueueMessage, client: df.DurableOrchestrationClient):
    """
    The function that triggers an export being processed.
    """
    parsed_message = ExportRequestQueueMessage.model_validate_json(msg.get_body())
    instance_id = await client.start_new(
        orchestration_function_name="ExportOrchestrator",
        instance_id=None,
        client_input=parsed_message.model_dump(mode="json", by_alias=True),
    )
    log.info("Started export orchestration", extra={"instance_id": instance_id})


@app.orchestration_trigger(context_name="context", orchestration="ExportOrchestrator")
def export_orchestrator(context: df.DurableOrchestrationContext):
    """
    The orchestrator that encapsulates the workflow of an export being processed.
    """
    from activities import (
        asset_export,
        defect_export,
        fetch_export_details,
        fetch_export_payload,
        generate_bulk_inspection_pdf,
        info360_asset_export,
        pacp7_mdb_export,
        pacp8_sqlite_export,
        predictor_export,
        repair_recommendation_export,
        send_export_output_details,
        update_export_status,
        zipped_pdf_export,
    )

    input_raw = context.get_input()
    input_data = ExportRequestQueueMessage.model_validate(input_raw)
    log.info(
        "Starting ExportOrchestrator",
        extra={"export_id": input_data.export_id, "target_org_id": input_data.target_org_id},
    )

    raw_res = yield context.call_activity(
        "FetchExportDetails",
        fetch_export_details.Input(
            export_id=input_data.export_id,
            target_org_id=input_data.target_org_id,
            instance_id=context.instance_id,
        ).model_dump(mode="json", by_alias=True),
    )
    export_details = fetch_export_details.Output.model_validate(raw_res).details
    if export_details is None:
        log.error(
            "Export not found, exiting",
            extra={"extra_data": {"export_id": input_data.export_id, "target_org_id": input_data.target_org_id}},
        )
        return
    if export_details.status != ExportStatus.PENDING:
        log.info(
            "Export is not in PENDING state, exiting",
            extra={"export_id": input_data.export_id, "status": export_details.status.value},
        )
        return

    error_reason: ExportStatusReason | None = None
    try:
        yield context.call_activity(
            "UpdateExportStatus",
            update_export_status.Input(
                export_id=input_data.export_id,
                status=ExportStatus.PROCESSING,
                target_org_id=input_data.target_org_id,
                instance_id=context.instance_id,
            ).model_dump(mode="json", by_alias=True),
        )

        raw_res = yield context.call_activity(
            "FetchExportPayload",
            fetch_export_payload.Input(
                export_id=input_data.export_id,
                target_org_id=input_data.target_org_id,
                instance_id=context.instance_id,
            ).model_dump(mode="json", by_alias=True),
        )

        full_payload = fetch_export_payload.Output.model_validate(raw_res).full_payload.root
        # Dispatch the export payload to the appropriate activity based on type

        output_files = []
        if full_payload.type == ExportType.PREDICTOR:
            raw_res = yield context.call_activity(
                "DefectPredictorExport",
                predictor_export.Input(
                    export_id=input_data.export_id,
                    target_org_id=input_data.target_org_id,
                    payload=full_payload,
                    instance_id=context.instance_id,
                ).model_dump(mode="json", by_alias=True),
            )
            predictor_res = predictor_export.Output.model_validate(raw_res)
            output_files.append(unwrap_output(predictor_res.output_file))

        elif full_payload.type == ExportType.ASSET:
            raw_res = yield context.call_activity(
                "AssetExport",
                asset_export.Input(
                    export_id=input_data.export_id,
                    target_org_id=input_data.target_org_id,
                    payload=full_payload,
                    instance_id=context.instance_id,
                ).model_dump(mode="json", by_alias=True),
            )

            asset_res = asset_export.Output.model_validate(raw_res)
            output_files.append(unwrap_output(asset_res.output_file))

        elif full_payload.type == ExportType.DEFECT:
            raw_res = yield context.call_activity(
                "DefectExport",
                defect_export.Input(
                    export_id=input_data.export_id,
                    target_org_id=input_data.target_org_id,
                    payload=full_payload,
                    instance_id=context.instance_id,
                ).model_dump(mode="json", by_alias=True),
            )

            defect_res = defect_export.Output.model_validate(raw_res)
            output_files.append(unwrap_output(defect_res.output_file))

        elif full_payload.type == ExportType.PACP7_MDB:
            raw_res = yield context.call_activity(
                "PACP7MDBExport",
                pacp7_mdb_export.Input(
                    export_id=input_data.export_id,
                    target_org_id=input_data.target_org_id,
                    payload=full_payload,
                    instance_id=context.instance_id,
                ).model_dump(mode="json", by_alias=True),
            )

            mdb_res = pacp7_mdb_export.Output.model_validate(raw_res)
            output_files.append(unwrap_output(mdb_res.output_file))

        elif full_payload.type == ExportType.BULK_INSPECTION_PDF and full_payload.format == ExportFormat.PDF:
            raw_res = yield context.call_activity(
                "GenerateBulkInspectionPDF",
                generate_bulk_inspection_pdf.Input(
                    export_id=input_data.export_id,
                    target_org_id=input_data.target_org_id,
                    payload=full_payload,
                    instance_id=context.instance_id,
                ).model_dump(mode="json", by_alias=True),
            )
            pdf_res = generate_bulk_inspection_pdf.Output.model_validate(raw_res)
            output_files.append(pdf_res.output_file)

        elif full_payload.type == ExportType.BULK_INSPECTION_PDF and full_payload.format == ExportFormat.ZIP:
            raw_res = yield context.call_activity(
                "ZippedPdfExport",
                zipped_pdf_export.Input(
                    export_id=input_data.export_id,
                    target_org_id=input_data.target_org_id,
                    payload=full_payload,
                    instance_id=context.instance_id,
                ).model_dump(mode="json", by_alias=True),
            )
            zip_res = zipped_pdf_export.Output.model_validate(raw_res)
            output_files.append(unwrap_output(zip_res.output_file))

        elif full_payload.type == ExportType.INFO_ASSET:
            raw_res = yield context.call_activity(
                "InfoAssetExport",
                info360_asset_export.Input(
                    export_id=input_data.export_id,
                    target_org_id=input_data.target_org_id,
                    payload=full_payload,
                    instance_id=context.instance_id,
                ).model_dump(mode="json", by_alias=True),
            )
            info_asset_res = info360_asset_export.Output.model_validate(raw_res)
            output_files.append(unwrap_output(info_asset_res.output_file))

        elif full_payload.type == ExportType.PACP8_SQLITE:
            raw_res = yield context.call_activity(
                "PACP8SQLiteExport",
                pacp8_sqlite_export.Input(
                    export_id=input_data.export_id,
                    target_org_id=input_data.target_org_id,
                    payload=full_payload,
                    instance_id=context.instance_id,
                ).model_dump(mode="json", by_alias=True),
            )
            sqlite_res = pacp8_sqlite_export.Output.model_validate(raw_res)
            output_files.append(unwrap_output(sqlite_res.output_file))

        elif full_payload.type == ExportType.REPAIR_RECOMMENDATION:
            raw_res = yield context.call_activity(
                "RepairRecommendationExport",
                repair_recommendation_export.Input(
                    export_id=input_data.export_id,
                    target_org_id=input_data.target_org_id,
                    payload=full_payload,
                    instance_id=context.instance_id,
                ).model_dump(mode="json", by_alias=True),
            )
            rr_res = repair_recommendation_export.Output.model_validate(raw_res)
            output_files.append(unwrap_output(rr_res.output_file))

        yield context.call_activity(
            "SendExportOutputDetails",
            send_export_output_details.Input(
                export_id=input_data.export_id,
                target_org_id=input_data.target_org_id,
                files=output_files,
                instance_id=context.instance_id,
            ).model_dump(mode="json", by_alias=True),
        )

        yield context.call_activity(
            "UpdateExportStatus",
            update_export_status.Input(
                export_id=input_data.export_id,
                status=ExportStatus.COMPLETED,
                status_reason=None,
                target_org_id=input_data.target_org_id,
            ).model_dump(mode="json", by_alias=True),
        )

    except ExportStatusException as e:
        log.error(f"Error occurred: {e}", exc_info=True)
        error_reason = e.status_reason

    except Exception as e:
        log.error(f"Error occurred: {e}", exc_info=True)
        error_reason = ExportStatusReason.GENERIC_ERROR

    if error_reason:
        yield context.call_activity(
            "UpdateExportStatus",
            update_export_status.Input(
                export_id=input_data.export_id,
                status=ExportStatus.FAILED,
                status_reason=error_reason,
                target_org_id=input_data.target_org_id,
            ).model_dump(mode="json", by_alias=True),
        )


@app.activity_trigger(input_name="inputs", activity="FetchExportDetails")
@rollbar_instrument
async def fetch_export_details_activity(inputs: dict):
    from activities import fetch_export_details

    input_dto = fetch_export_details.Input.model_validate(inputs)
    client = api.new_client()
    output_dto = await fetch_export_details.handler(input_dto, client)

    return output_dto.model_dump(mode="json", by_alias=True)


@app.activity_trigger(input_name="inputs", activity="UpdateExportStatus")
@rollbar_instrument
def update_export_status_activity(inputs: dict):
    from activities import update_export_status

    input_dto = update_export_status.Input.model_validate(inputs)
    client = api.new_client()
    update_export_status.handler(input_dto, client)


@app.activity_trigger(input_name="inputs", activity="FetchExportPayload")
@rollbar_instrument
def fetch_export_payload_activity(inputs: dict):
    from activities import fetch_export_payload

    input_dto = fetch_export_payload.Input.model_validate(inputs)
    client = api.new_client()
    output_dto = fetch_export_payload.handler(input_dto, client)

    return output_dto.model_dump(mode="json", by_alias=True)


@app.activity_trigger(input_name="inputs", activity="SendExportOutputDetails")
@rollbar_instrument
def send_output_details_activity(inputs: dict):
    from activities import send_export_output_details

    input_dto = send_export_output_details.Input.model_validate(inputs)
    client = api.new_client()
    send_export_output_details.handler(input_dto, client)


@app.activity_trigger(input_name="inputs", activity="GenerateBulkInspectionPDF")
@rollbar_instrument
def generate_bulk_inspection_pdf_activity(inputs: dict) -> dict:
    from activities import generate_bulk_inspection_pdf

    input_dto = generate_bulk_inspection_pdf.Input.model_validate(inputs)
    storage_settings = ExportStorageSettings()
    settings = generate_bulk_inspection_pdf.Settings()
    client = api.new_client()
    output_fs = adlfs.AzureBlobFileSystem(connection_string=storage_settings.storage_account_conn_str)
    output_dto = generate_bulk_inspection_pdf.handler(input_dto, client, settings, output_fs)

    return output_dto.model_dump(mode="json", by_alias=True)


@app.activity_trigger(input_name="inputs", activity="ZippedPdfExport")
@rollbar_instrument
def zipped_pdf_export_activity(inputs: dict) -> dict:
    from activities import zipped_pdf_export

    input_dto = zipped_pdf_export.Input.model_validate(inputs)
    storage_settings = ExportStorageSettings()
    settings = zipped_pdf_export.Settings()
    client = api.new_client()
    output_fs = adlfs.AzureBlobFileSystem(connection_string=storage_settings.storage_account_conn_str)
    output_dto = zipped_pdf_export.handler(input_dto, client, settings, output_fs)

    return output_dto.model_dump(mode="json", by_alias=True)


@app.activity_trigger(input_name="inputs", activity="DefectPredictorExport")
@rollbar_instrument
def defect_predictor_export_activity(inputs: dict):
    from activities import predictor_export

    input_dto = predictor_export.Input.model_validate(inputs)
    storage_settings = ExportStorageSettings()
    settings = predictor_export.Settings()
    client = api.new_client()
    output_fs = adlfs.AzureBlobFileSystem(connection_string=storage_settings.storage_account_conn_str)

    output_dto = predictor_export.handler(input_dto, settings, client, output_fs)

    return output_dto.model_dump(mode="json", by_alias=True)


@app.activity_trigger(input_name="inputs", activity="AssetExport")
@rollbar_instrument
def asset_export_activity(inputs: dict):
    from activities import asset_export

    input_dto = asset_export.Input.model_validate(inputs)
    storage_settings = ExportStorageSettings()
    settings = asset_export.Settings()
    client = api.new_client()
    output_fs = adlfs.AzureBlobFileSystem(connection_string=storage_settings.storage_account_conn_str)

    output_dto = asset_export.handler(input_dto, settings, client, output_fs)

    return output_dto.model_dump(mode="json", by_alias=True)


@app.activity_trigger(input_name="inputs", activity="DefectExport")
@rollbar_instrument
def defect_export_activity(inputs: dict):
    from activities import defect_export

    input_dto = defect_export.Input.model_validate(inputs)
    storage_settings = ExportStorageSettings()
    settings = defect_export.Settings()
    client = api.new_client()
    output_fs = adlfs.AzureBlobFileSystem(connection_string=storage_settings.storage_account_conn_str)

    output_dto = defect_export.handler(input_dto, settings, client, output_fs)

    return output_dto.model_dump(mode="json", by_alias=True)


@app.activity_trigger(input_name="inputs", activity="PACP7MDBExport")
@rollbar_instrument
def pacp7_mdb_export_activity(inputs: dict):
    from activities import pacp7_mdb_export

    input_dto = pacp7_mdb_export.Input.model_validate(inputs)
    storage_settings = ExportStorageSettings()
    output_fs = adlfs.AzureBlobFileSystem(connection_string=storage_settings.storage_account_conn_str)
    settings = pacp7_mdb_export.Settings()
    client = api.new_client()

    output_dto = pacp7_mdb_export.handler(input_dto, output_fs, settings, client)

    return output_dto.model_dump(mode="json", by_alias=True)


@app.activity_trigger(input_name="inputs", activity="InfoAssetExport")
@rollbar_instrument
def info_asset_export_activity(inputs: dict):
    from activities import info360_asset_export

    input_dto = info360_asset_export.Input.model_validate(inputs)
    storage_settings = ExportStorageSettings()
    settings = info360_asset_export.Settings()
    client = api.new_client()
    output_fs = adlfs.AzureBlobFileSystem(connection_string=storage_settings.storage_account_conn_str)

    output_dto = info360_asset_export.handler(input_dto, settings, client, output_fs)

    return output_dto.model_dump(mode="json", by_alias=True)


@app.activity_trigger(input_name="inputs", activity="PACP8SQLiteExport")
@rollbar_instrument
def pacp8_sqlite_export_activity(inputs: dict):
    from activities import pacp8_sqlite_export

    input_dto = pacp8_sqlite_export.Input.model_validate(inputs)
    storage_settings = ExportStorageSettings()
    output_fs = adlfs.AzureBlobFileSystem(connection_string=storage_settings.storage_account_conn_str)
    settings = pacp8_sqlite_export.Settings()
    client = api.new_client()

    output_dto = pacp8_sqlite_export.handler(input_dto, output_fs, settings, client)

    return output_dto.model_dump(mode="json", by_alias=True)


@app.activity_trigger(input_name="inputs", activity="RepairRecommendationExport")
def repair_recommendation_export_activity(inputs: dict):
    from activities import repair_recommendation_export

    input_dto = repair_recommendation_export.Input.model_validate(inputs)
    storage_settings = ExportStorageSettings()
    output_fs = adlfs.AzureBlobFileSystem(connection_string=storage_settings.storage_account_conn_str)
    settings = repair_recommendation_export.Settings()
    client = api.new_client()

    output_dto = repair_recommendation_export.handler(input_dto, settings, client, output_fs)

    return output_dto.model_dump(mode="json", by_alias=True)


@app.blob_trigger(arg_name="blob", path="import-files/{name}", connection="STORAGE_ACCOUNT_CONN_STR")
@rollbar_instrument
async def imports_blob_trigger(blob: InputStream):
    """
    Trigger that listens for new import files being uploaded, and enqueues their associated import for processing.
    """
    props = blob.blob_properties
    import_id = props["Metadata"].get("import_id")
    target_org_id = props["Metadata"].get("target_org_id")
    created_at_timestamp = props["CreatedOn"]

    log.info(
        "Received blob upload for import %s, org %s",
        import_id,
        target_org_id,
        extra={"import_id": import_id, "target_org_id": target_org_id},
    )

    storage_settings = ImportStorageSettings()
    queue_client = QueueClient.from_connection_string(
        conn_str=storage_settings.storage_account_conn_str,
        queue_name=storage_settings.imports_queue_name,
        message_encode_policy=BinaryBase64EncodePolicy(),
        message_decode_policy=BinaryBase64DecodePolicy(),
    )

    msg = ImportRequestQueueMessage(import_id=import_id, target_org_id=target_org_id, created_at=created_at_timestamp)
    queue_client.send_message(msg.model_dump_json(by_alias=True).encode())
    log.info("Import message enqueued", extra={"import_id": import_id})


@app.queue_trigger(arg_name="msg", queue_name="imports", connection="STORAGE_ACCOUNT_CONN_STR")
@app.durable_client_input(client_name="client", connection_name="STORAGE_ACCOUNT_CONN_STR")
@rollbar_instrument
async def imports_queue_trigger(msg: func.QueueMessage, client: df.DurableOrchestrationClient):
    """
    Trigger that listens for new import requests being enqueued, and starts the import orchestration.
    """

    msg = ImportRequestQueueMessage.model_validate_json(msg.get_body())
    log.info(
        "Received message for import %s, org  %d",
        msg.import_id,
        msg.target_org_id,
        extra={"import_id": msg.import_id, "target_org_id": msg.target_org_id},
    )
    instance_id = await client.start_new(
        orchestration_function_name="ImportOrchestrator",
        instance_id=None,
        client_input=msg.model_dump(mode="json", by_alias=True),
    )
    log.info("Started import orchestration", extra={"instance_id": instance_id})


@app.orchestration_trigger(context_name="context", orchestration="ImportOrchestrator")
def import_orchestrator(context: df.DurableOrchestrationContext):
    """
    The orchestrator that encapsulates the workflow of an import being processed.
    """
    from activities import (
        asset_import,
        fetch_import_details,
        inspection_import,
        inspection_video_media_import,
        update_import_status,
    )

    input_raw = context.get_input()
    input_data = {"import_id": input_raw.get("import_id"), "target_org_id": input_raw.get("target_org_id")}

    log.info(
        "Starting ImportOrchestrator",
        extra={"import_id": input_data["import_id"], "target_org_id": input_data["target_org_id"]},
    )

    raw_res = yield context.call_activity(
        "FetchImportDetails",
        fetch_import_details.Input(
            import_id=input_data["import_id"],
            target_org_id=input_data["target_org_id"],
            instance_id=context.instance_id,
        ).model_dump(mode="json", by_alias=True),
    )
    import_details = fetch_import_details.Output.model_validate(raw_res).details
    if import_details is None:
        log.error(
            "Import not found, exiting",
            extra={"extra_data": {"import_id": input_data["import_id"], "target_org_id": input_data["target_org_id"]}},
        )
        return
    if import_details.status != ImportStatusEnum.PENDING:
        log.info(
            "Import is not in PENDING state, exiting",
            extra={"import_id": input_data["import_id"], "status": import_details.status.value},
        )
        return

    try:
        yield context.call_activity(
            "UpdateImportStatus",
            update_import_status.Input(
                import_id=input_data["import_id"],
                status=ImportStatusEnum.PROCESSING,
                target_org_id=input_data["target_org_id"],
                instance_id=context.instance_id,
            ).model_dump(mode="json", by_alias=True),
        )

        if import_details.type == ImportTypeEnum.ASSETS:
            yield context.call_activity(
                "AssetImport",
                asset_import.Input(
                    import_id=input_data["import_id"],
                    target_org_id=input_data["target_org_id"],
                    instance_id=context.instance_id,
                ).model_dump(mode="json", by_alias=True),
            )

        elif import_details.type == ImportTypeEnum.INSPECTIONS:
            yield context.call_activity(
                "InspectionImport",
                inspection_import.Input(
                    import_details=import_details,
                    import_id=input_data["import_id"],
                    target_org_id=input_data["target_org_id"],
                    instance_id=context.instance_id,
                ).model_dump(mode="json", by_alias=True),
            )

        elif import_details.type == ImportTypeEnum.INSPECTION_VIDEO_MEDIA:
            yield context.call_activity(
                "InspectionVideoMediaImport",
                inspection_video_media_import.Input(
                    import_details=import_details,
                    target_org_id=input_data["target_org_id"],
                    instance_id=context.instance_id,
                ).model_dump(mode="json", by_alias=True),
            )

        yield context.call_activity(
            "UpdateImportStatus",
            update_import_status.Input(
                import_id=input_data["import_id"],
                status=ImportStatusEnum.COMPLETED,
                status_reason=None,
                target_org_id=input_data["target_org_id"],
            ).model_dump(mode="json", by_alias=True),
        )

    except Exception as e:
        log.error(f"Error occurred: {e}", exc_info=True)
        yield context.call_activity(
            "UpdateImportStatus",
            update_import_status.Input(
                import_id=input_data["import_id"],
                status=ImportStatusEnum.FAILED,
                status_reason=ImportStatusReasonEnum.GENERIC_ERROR,
                target_org_id=input_data["target_org_id"],
            ).model_dump(mode="json", by_alias=True),
        )


@app.activity_trigger(input_name="inputs", activity="UpdateImportStatus")
@rollbar_instrument
def update_import_status_activity(inputs: dict):
    from activities import update_import_status

    input_dto = update_import_status.Input.model_validate(inputs)
    client = api.new_client()
    update_import_status.handler(input_dto, client)


@app.activity_trigger(input_name="inputs", activity="FetchImportDetails")
@rollbar_instrument
async def fetch_import_details_activity(inputs: dict):
    from activities import fetch_import_details

    input_dto = fetch_import_details.Input.model_validate(inputs)
    client = api.new_client()
    output_dto = await fetch_import_details.handler(input_dto, client)

    return output_dto.model_dump(mode="json", by_alias=True)


@app.activity_trigger(input_name="inputs", activity="AssetImport")
@rollbar_instrument
async def asset_import_activity(inputs: dict):
    from activities import asset_import

    storage_settings = ImportStorageSettings()

    input_dto = asset_import.Input.model_validate(inputs)
    client = api.new_client()

    input_fs = adlfs.AzureBlobFileSystem(connection_string=storage_settings.storage_account_conn_str, asynchronous=True)
    await asset_import.handler(input_dto, client, input_fs)


@app.activity_trigger(input_name="inputs", activity="InspectionImport")
@rollbar_instrument
def inspection_import_activity(inputs: dict):
    from activities import inspection_import

    input_dto = inspection_import.Input.model_validate(inputs)
    client = api.new_client()
    storage_settings = ImportStorageSettings()
    input_fs = adlfs.AzureBlobFileSystem(connection_string=storage_settings.storage_account_conn_str)
    inspection_import.handler(input_dto, client, input_fs)


@app.activity_trigger(input_name="inputs", activity="InspectionVideoMediaImport")
@rollbar_instrument
def inspection_import_video_media_activity(inputs: dict):
    from activities import inspection_video_media_import

    input_dto = inspection_video_media_import.Input.model_validate(inputs)
    client = api.new_client()
    storage_settings = ImportStorageSettings()
    vid_source_fs = adlfs.AzureBlobFileSystem(connection_string=storage_settings.storage_account_conn_str)
    dest_fs = adlfs.AzureBlobFileSystem(connection_string=storage_settings.storage_account_conn_str)
    settings = inspection_video_media_import.Settings()
    inspection_video_media_import.handler(input_dto, settings, client, vid_source_fs, dest_fs)
