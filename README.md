### VAPAR API Tasks
Function app for running background tasks for the [backend application](https://bitbucket.org/teamvapar/backend_v3/src).


## Local Development
### Install prerequisites:
* Python 3.10
* [Poetry](https://python-poetry.org/) - Used for package management
* [Azure functions core tools](https://learn.microsoft.com/en-us/azure/azure-functions/create-first-function-cli-python?tabs=macos%2Cbash%2Cazure-cli%2Cbrowser#install-the-azure-functions-core-tools) - Optional, used for running functions locally
* [Azurite](https://learn.microsoft.com/en-us/azure/storage/common/storage-use-azurite?tabs=npm%2Cblob-storage#install-azurite) - Optional, used for emulating Azure storage locally

### Install dev dependencies:
```bash
# Authenticate poetry with the vaparlib repository
make auth-vapar # Requires BITBUCKET_VAPAR_REPO_TOKEN to be defined in the environment

poetry install --with=dev --no-root
```

### Run linting:
```bash
make lint
```

### Fix linting issues:
```bash
make fix
```

### Run tests:
```bash
make test
```

### Running functions locally:
First, make sure the following env vars are defined:

* `STORAGE_ACCOUNT_CONN_STR`: Connection string for the Azure storage account that contains the queue or "UseDevelopmentStorage=true" for running with Azurite
* `VAPAR_API_BASE_URL`: Base URL for the API backend
* `VAPAR_API_SECRET_KEY`: API key for the service user that will be used to interact with the API backend

For running asset exports, also ensure the following env vars are defined:

* `VIDEO_PLAY_DOMAIN`: The base URL for playing inspection videos
* `PDF_DOMAIN`: The base URL for downloading inspection PDFs
* `IMAGE_SECRET_KEY_B64ENCODED`: The secret key for image URL encryption (base64 encoded)
* `SALT_B64ENCODED`: The salt for image URL encryption (base64 encoded)

For the export_timeout_cleanup_timer_trigger to be loaded, you need to define the following:

* `EXPORT_TIMEOUT_CLEANUP_NCRON_SCHEDULE`: The schedule for the cleanup function in [NCRON format](https://learn.microsoft.com/en-us/azure/azure-functions/functions-bindings-timer?tabs=python-v2,isolated-process,nodejs-v4&pivots=programming-language-python#ncrontab-expressions) - eg. "0 */10 * * * *" to execute every 10 minutes

Then, run the following commands:
```shell
# Start Azurite emulator
azurite

# Activate poetry virtual environment
poetry shell

# Start the function emulator
func start

# Now, messages submitted to a queue named 'exports' (either through Azure Storage Explorer or the VSCode Azure
# extension) will be picked up by the queue trigger function.

```

### Deployment
Deployments are automated via bitbucket pipelines when pushing to release/* branches.
You can also run deployments manually:
```bash
make deploy-dev
# or deploy-staging, deploy-prod
```
These commands deploy to a "staging" slot, which must be swapped manually with the active slot in the Azure portal.
