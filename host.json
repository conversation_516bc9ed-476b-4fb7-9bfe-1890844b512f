{"version": "2.0", "logging": {"applicationInsights": {"samplingSettings": {"isEnabled": true, "excludedTypes": "Request"}}}, "extensionBundle": {"id": "Microsoft.Azure.Functions.ExtensionBundle", "version": "[4.*, 5.0.0)"}, "extensions": {"durableTask": {"storageProvider": {"type": "AzureStorage"}, "hubName": "TaskHubName"}, "queues": {"maxPollingInterval": "00:00:02"}}, "functionTimeout": "00:09:59"}