[project]
name = "backend-tasks"
description = "Function app for running background tasks for the backend application"
authors = [
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" }
]
readme = "README.md"

[tool.poetry]
package-mode = false

[tool.poetry.dependencies]
python = "^3.10"
vapar = { git = "https://bitbucket.org/teamvapar/vapar.git", tag = "v0.9.17" }
azure-functions = "^1.20.0"
azure-functions-durable = "^1.2.9"
jinja2 = "^3.1.4"
fsspec = "^2024.6.1"
httpx = "^0.27.0"
jaydebeapi = "^1"
adlfs = "^2024.7.0"
pandas = "^2.2.2"
cachetools = "^5.5.0"
xmltodict = "^0.13.0"
pyppeteer = "^2.0.0"
yarl = "^1.18.3"
azure-storage-queue = "^12.12.0"
cryptography="43.0.3" # Pinned because of deployment issue: https://github.com/Azure/azure-sdk-for-python/issues/38725

[tool.poetry.group.dev.dependencies]
pypdf = "^4.3.1"
pytest = "^8.3.1"
pytest-cov = "^5.0.0"
pytest-asyncio = "^0.23.8"
black = "^24.4.2"
ruff = "^0.5.4"

[tool.poetry.group.lint.dependencies]
ruff = "^0.5.4"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 120
