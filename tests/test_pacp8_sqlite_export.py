import sqlite3
from pathlib import Path
from uuid import UUID

import httpx
import pandas as pd
import pytest
from fsspec.implementations.dirfs import DirFileSystem
from vapar.clients import api
from vapar.clients.nassco_validator import (
    NASSCOValidatorClient,
    NASSCOValidatorPayload,
    PACPCondition,
    PACPInspection,
    PACPRating,
)
from vapar.core.exports import PACP8SQLiteExportPayload

from activities import pacp8_sqlite_export
from tests.conftest import get_mock_client_with_handler

_ORG_DATA_TEMPLATE = {
    "id": 123,
    "fullName": "Test Org",
    "country": "AU",
    "emailDomain": "test-org.com",
    "logoPath": None,
    "orgType": "Asset_Owner",
    "shortName": "TO",
    "sewerData": "Sewer",
    "standardKey": 6,
    "standardDisplayName": "Australia 2013",
    "linkedOrganisations": [],
}


_INSPECTION_DATA_TEMPLATE = {
    "uuid": "4d797e1a-3004-4338-897d-7c7d9fb6f052",
    "legacyId": "78761",
    "status": "Uploaded",
    "structuralGrade": 2,
    "serviceGrade": 4,
    "createdBy": None,
    "createdAt": "2024-08-09T00:27:02.397568Z",
    "Direction": "Downstream",
    "Date": "2019-08-20",
    "LengthSurveyed": 3983.0,
    "GeneralRemarks": "",
    "IsImperial": None,
    "Standard": "Australia 2020",
    "ReviewedBy": None,
    "Time": None,
    "AssetID": "asset id",
    "Material": "vitrifiedclay",
    "HeightDiameter": 8,
    "LocationStreet": "123 Main St",
    "LocationTown": "Roanoke",
    "UpstreamNode": "US Node",
    "DownstreamNode": "DS Node",
    "UseOfDrainSewer": "SS",
    "repairCompletedDate": None,
    "folder": {
        "id": 4776,
        "path": "Sub-model testing 09/08/24",
        "depth": 2,
        "numchild": 0,
        "primary_org": None,
        "secondary_org": None,
        "job_name": "Sub-model testing 09/08/24",
        "created_date": "2024-08-09T00:07:15.965074Z",
        "pipe_type_sewer": False,
        "standard_key": 6,
    },
    "asset": {
        "uuid": "4e252512-15f6-4563-aadc-e85aa6ef05ac",
        "type": "pipe",
        "organisation": 237,
        "standard": 0,
        "createdAt": "2024-08-09T00:26:50.162221Z",
        "AssetID": "",
        "UpstreamNode": "US Node",
        "DownstreamNode": "DS Node",
        "HeightDiameter": 8,
        "Material": "vitrifiedclay",
        "UseOfDrainSewer": "SS",
        "LocationStreet": "123 Main St",
        "LocationTown": "Roanoke",
        "extraFields": {},
    },
    "file": {
        "id": 87011,
        "url": "uploadedvideofiles/06_03_2024_22_42_34_899046-01b-3933.001b-3932.0_10018_08_20_2019.mp4",
        "hidden": False,
        "filename": "06_03_2024_22_42_34_899046-01b-3933.001b-3932.0_10018_08_20_2019.mp4",
        "file_size": "108.2MB",
        "file_type": "application/octet-stream",
        "upload_org": 123,
        "target_org": 123,
        "upload_user": "VAPAR Admin",
        "uploaded_by": 766,
        "created_time": "2024-08-09T00:07:40.501596Z",
        "job_id": None,
        "job_tree": 4776,
        "upload_completed_time": "2024-08-09T00:08:45.227556Z",
        "upload_completed": True,
        "upload_org_type": "Asset_Owner",
    },
    "extraFields": {},
    "toBeMatched": False,
    "ExpectedLength": "3983.0",
    "start_node": "US Node",
    "end_node": "DS Node",
    "location": {"LocationStreet": "123 Main St", "LocationTown": "Roanoke"},
    "name": "Stree 8 4725 Brookwood Drive, Roanoke",
    "filename": "06_03_2024_22_42_34_899046-01b-3933.001b-3932.0_10018_08_20_2019.mp4",
    "SetupLocation": "Upstream",
    "chainage_unit": "m",
}


_FRAME_DATA_TEMPLATE = {
    "id": 0,
    "inspectionId": 0,
    "imageLocation": "string",
    "imageUrl": "string",
    "frameId": 1,
    "classLabel": "Defect Description 1",
    "classCertainty": ".9",
    "chainage": "string",
    "chainageNumber": "50",
    "isHidden": False,
    "isAccepted": True,
    "allClassBreakdown": "string",
    "atJoint": False,
    "atClock": 2147483647,
    "toClock": 2147483647,
    "contDefectStart": False,
    "contDefectEnd": None,
    "quantity1Value": "0",
    "quantity1Units": "m",
    "quantity2Value": "",
    "quantity2Units": "",
    "remarks": "string",
    "isMatched": True,
    "parentVideo": 87011,
    "pipeTypeSewer": True,
    "material": "string",
    "defectId": 0,
    "defectClass": "Defect Description 1",
    "defectCode": "OBZ",
    "defectStrScore": "2",
    "defectSerScore": "3",
    "defectScoreSeverity": "string",
    "defectScoreIsShown": True,
    "timeReference": "string",
}


@pytest.fixture
def settings():
    return pacp8_sqlite_export.Settings()


@pytest.fixture
def validator(settings):
    return NASSCOValidatorClient(settings.nassco_cli_path)


@pytest.fixture
def multiple_frames():
    f1 = _FRAME_DATA_TEMPLATE.copy()
    f1["frameId"] = 1
    f1["classLabel"] = "Defect Description 1"
    f1["chainage"] = "string"
    f1["chainageNumber"] = "50"
    f1["atClock"] = 1
    f1["toClock"] = 3
    f1["quantity1Value"] = "50"
    f1["quantity1Units"] = "m"
    f1["timeReference"] = None

    f2 = _FRAME_DATA_TEMPLATE.copy()
    f2["frameId"] = 2
    f2["classLabel"] = "Defect Description 2"
    f2["chainage"] = "string"
    f2["chainageNumber"] = "60"
    f2["atClock"] = 2
    f2["toClock"] = 4
    f2["quantity1Value"] = "100"
    f2["quantity1Units"] = "%"
    f2["contDefectStart"] = True
    f2["contDefectEnd"] = "70"
    f2["timeReference"] = "00:12:34.5"
    f2["atJoint"] = True
    f2["IsImperial"] = True

    return [f1, f2]


@pytest.fixture
def minimal_validator_payload():
    payload = NASSCOValidatorPayload(
        pacp_inspections=[
            PACPInspection(
                inspection_id="41c1ecd2-53ac-4977-998f-7a955769cf6b",
            ),
            PACPInspection(
                inspection_id="a1c1ecd2-53ac-4977-998f-7a955769cf6b",
            ),
        ],
        pacp_conditions=[
            PACPCondition(
                inspection_id="41c1ecd2-53ac-4977-998f-7a955769cf6b",
                condition_id="41c1ecd2-53ac-4977-998f-7a955769cf6b",
                joint=1,
                pacp_code="OBZ",
                remarks="Uh oh",
            ),
            PACPCondition(
                inspection_id="41c1ecd2-53ac-4977-998f-7a955769cf6b",
                condition_id="41c1ecd2-53ac-4977-998f-7a955769cf6a",
                joint=0,
                value_percent="50",
                pacp_code="OBZ",
            ),
            PACPCondition(
                inspection_id="41c1ecd2-53ac-4977-998f-7a955769cf6b",
                condition_id="41c1ecd2-53ac-4977-998f-7a955769cf6c",
                joint=0,
                value_percent="100",
                pacp_code="OBZ",
            ),
            PACPCondition(
                inspection_id="a1c1ecd2-53ac-4977-998f-7a955769cf6b",
                condition_id="a1c1ecd2-53ac-4977-998f-7a955769cf6b",
                joint=0,
                value_percent="100",
                pacp_code="OBZ",
            ),
        ],
        pacp_ratings=[
            PACPRating(
                inspection_id="41c1ecd2-53ac-4977-998f-7a955769cf6b",
                rating_id="41c1ecd2-53ac-4977-998f-7a955769cf6b",
            ),
            PACPRating(
                inspection_id="a1c1ecd2-53ac-4977-998f-7a955769cf6b",
                rating_id="a1c1ecd2-53ac-4977-998f-7a955769cf6b",
            ),
        ],
    )

    return payload


@pytest.fixture
def prefilled_validator_payload():
    payload = NASSCOValidatorPayload(
        pacp_inspections=[
            PACPInspection(
                inspection_id="41c1ecd2-53ac-4977-998f-7a955769cf6b",
                inspection_technology_used_cctv=1,
                inspection_technology_used_laser=0,
                inspection_technology_used_zoom=0,
                inspection_technology_used_other=0,
                inspection_technology_used_sonar=0,
                inspection_technology_used_sidewall=0,
                surveyed_by="John Smith",
                certificate_number="123456",
                project="Test Project",
                inspection_date="20210101",
                inspection_time="12:00",
                pipe_segment_reference="1",
                pre_cleaning="H",
                direction="D",
                inspection_status="CI",
                shape="C",
                street="Main St",
                city="ABC",
                upstream_mh_number="1",
                downstream_mh_number="2",
            ),
        ],
        pacp_conditions=[
            PACPCondition(
                inspection_id="41c1ecd2-53ac-4977-998f-7a955769cf6b",
                condition_id="41c1ecd2-53ac-4977-998f-7a955769cf6b",
                joint=1,
                pacp_code="OBZ",
                remarks="Uh oh",
                clock_at_from=1,
                clock_to=3,
                value_percent="50",
            ),
        ],
        pacp_ratings=[
            PACPRating(
                inspection_id="41c1ecd2-53ac-4977-998f-7a955769cf6b",
                rating_id="41c1ecd2-53ac-4977-998f-7a955769cf6b",
            ),
        ],
    )
    return payload


@pytest.fixture
def single_insp_with_custom_fields():
    insp = api.models.InspectionModel.from_dict(_INSPECTION_DATA_TEMPLATE)
    insp.extra_fields["ClientDefined1"] = "Custom 1"
    insp.extra_fields["ClientDefined2"] = "Custom 2"
    insp.extra_fields["ClientDefined3"] = "Custom 3"
    insp.extra_fields["ClientDefined10"] = "Custom 10"

    return insp


def _read_sqlite_to_dfs(output_fs: DirFileSystem, db_path: str) -> dict[str, pd.DataFrame]:
    full_path = str(Path(output_fs.path, db_path))
    with sqlite3.connect(full_path) as conn:
        tables = {
            "PACP_Inspections": pd.read_sql("select * from PACP_Inspections", conn),
            "PACP_Conditions": pd.read_sql("select * from PACP_Conditions", conn),
            "PACP_Ratings": pd.read_sql("select * from PACP_Ratings", conn),
            "PACP_Media_Conditions": pd.read_sql("select * from PACP_Media_Conditions", conn),
            "PACP_Media_Inspections": pd.read_sql("select * from PACP_Media_Inspections", conn),
            "PACP_Custom_Fields": pd.read_sql("select * from PACP_Custom_Fields", conn),
        }
    return tables


def test_extract_ratings_from_minimal_data(validator, minimal_validator_payload):
    res = pacp8_sqlite_export.extract_calculated_ratings(minimal_validator_payload, validator)
    assert len(res) == 2, "Expected ratings for 2 inspection"
    assert minimal_validator_payload.pacp_ratings[0].inspection_id in res, "Expected rating for inspection 1"
    assert minimal_validator_payload.pacp_ratings[1].inspection_id in res, "Expected rating for inspection 2"


def test_extract_ratings_with_some_prefilled_data(validator, prefilled_validator_payload):
    res = pacp8_sqlite_export.extract_calculated_ratings(prefilled_validator_payload, validator)
    assert len(res) == 1, "Expected ratings for 1 inspection"
    assert prefilled_validator_payload.pacp_ratings[0].inspection_id in res


def test_report_single_inspection_no_frames(settings, output_fs):
    """Check the basic required inspection properties are mapped"""

    def mock_handler(req):
        if req.url.path == "/api/v3/organisations/123":
            return httpx.Response(
                200,
                json=_ORG_DATA_TEMPLATE,
            )
        elif req.url.path == "/api/v3/inspections2":
            return httpx.Response(
                200,
                json={"results": [_INSPECTION_DATA_TEMPLATE], "count": 1, "next": None, "previous": None},
            )
        elif req.url.path == "/api/v3/inspections/frames":
            return httpx.Response(
                200,
                json={"results": [], "count": 0, "next": None, "previous": None},
            )
        return httpx.Response(404)

    api_client = get_mock_client_with_handler(mock_handler)

    input_data = pacp8_sqlite_export.Input(
        export_id=UUID("00000000-0000-0000-0000-000000000000"),
        target_org_id=123,
        payload=PACP8SQLiteExportPayload(inspection_ids=[UUID("4d797e1a-3004-4338-897d-7c7d9fb6f052")]),
    )

    res = pacp8_sqlite_export.handler(input_data, output_fs, settings, api_client, 0)

    tables = _read_sqlite_to_dfs(output_fs, res.output_file.file_path)
    inspections_df = tables["PACP_Inspections"]
    conditions_df = tables["PACP_Conditions"]
    media_inspections_df = tables["PACP_Media_Inspections"]
    media_conditions_df = tables["PACP_Media_Conditions"]
    custom_fields_df = tables["PACP_Custom_Fields"]

    assert len(conditions_df) == 0, "No defect frames provided"
    assert len(inspections_df) == 1, "Expected 1 inspection"
    assert len(media_conditions_df) == 0, "No defect frames provided"
    assert len(media_inspections_df) == 1, "One video per inspection"
    assert len(custom_fields_df) == 1, "One custom field row"

    insp_row = inspections_df.iloc[0]

    assert insp_row["InspectionID"] == "{4d797e1a-3004-4338-897d-7c7d9fb6f052}", "Should be enclosed in {}"

    assert insp_row["Inspection_Technology_Used_CCTV"] == 1, "Default to CCTV when not provided"
    assert insp_row["Inspection_Technology_Used_Laser"] == 0
    assert insp_row["Inspection_Technology_Used_Zoom"] == 0
    assert insp_row["Inspection_Technology_Used_Other"] == 0
    assert insp_row["Inspection_Technology_Used_Sonar"] == 0
    assert insp_row["Inspection_Technology_Used_Sidewall"] == 0

    assert insp_row["Inspection_Date"] == "20190820", "Should be in YYYYMMDD format"
    assert insp_row["PreCleaning"] == "X", "Default to X for unknown"
    assert insp_row["Direction"] == "D", "Downstream"
    assert insp_row["Inspection_Status"] == "CI", "'Completed Inspection' if not provided"
    assert insp_row["Street"] == "123 Main St"
    assert insp_row["City"] == "Roanoke"
    assert insp_row["Pipe_Use"] == "XX", "Default to XX for unknown"
    assert insp_row["Height"] == 8
    assert insp_row["Shape"] == "C", "Default to C for circular"
    assert insp_row["Material"] == "VCP", "Mapped to code"
    assert insp_row["Upstream_MH_Number"] == "US Node"
    assert insp_row["Downstream_MH_Number"] == "DS Node"
    assert insp_row["IsImperial"] == 0, "Default to metric"
    assert insp_row["Length_Surveyed"] == "3983.00", "2dp when its metric units"

    media_row = media_inspections_df.iloc[0]
    assert media_row["InspectionID"] == "{4d797e1a-3004-4338-897d-7c7d9fb6f052}"
    assert media_row["File_Name"] == "06_03_2024_22_42_34_899046-01b-3933.001b-3932.0_10018_08_20_2019.mp4"
    assert media_row["File_Location"].startswith("./videos/")

    custom_row = custom_fields_df.iloc[0]
    assert custom_row["InspectionID"] == "{4d797e1a-3004-4338-897d-7c7d9fb6f052}"
    assert custom_row["Custom_Field_One"] == ""
    assert custom_row["Custom_Field_Two"] == ""
    assert custom_row["Custom_Field_Three"] == ""
    # etc... No custom fields provided


def test_report_single_inspection_with_frames(settings, output_fs, multiple_frames):
    """Check the basic required inspection properties are mapped"""

    def mock_handler(req):
        if req.url.path == "/api/v3/organisations/123":
            return httpx.Response(
                200,
                json=_ORG_DATA_TEMPLATE,
            )
        elif req.url.path == "/api/v3/inspections2":
            return httpx.Response(
                200,
                json={"results": [_INSPECTION_DATA_TEMPLATE], "count": 1, "next": None, "previous": None},
            )
        elif req.url.path == "/api/v3/inspections/frames":
            return httpx.Response(
                200,
                json={"results": multiple_frames, "count": 2, "next": None, "previous": None},
            )
        return httpx.Response(404)

    api_client = get_mock_client_with_handler(mock_handler)

    input_data = pacp8_sqlite_export.Input(
        export_id=UUID("00000000-0000-0000-0000-000000000000"),
        target_org_id=123,
        payload=PACP8SQLiteExportPayload(inspection_ids=[UUID("4d797e1a-3004-4338-897d-7c7d9fb6f052")]),
    )

    res = pacp8_sqlite_export.handler(input_data, output_fs, settings, api_client, 0)

    tables = _read_sqlite_to_dfs(output_fs, res.output_file.file_path)
    inspections_df = tables["PACP_Inspections"]
    conditions_df = tables["PACP_Conditions"]
    media_inspections_df = tables["PACP_Media_Inspections"]
    media_conditions_df = tables["PACP_Media_Conditions"]

    assert len(conditions_df) == 3, "Expected 3 conditions: 2 frames, one of which is a continuous defect"
    assert len(inspections_df) == 1, "Expected 1 inspection"
    assert len(media_conditions_df) == 2, "2 defect frames"
    assert len(media_inspections_df) == 1, "One video per inspection"

    # Fetch the non-continuous defect
    cond_row = conditions_df[conditions_df["Continuous"] == ""].iloc[0]

    assert cond_row["InspectionID"] == "{4d797e1a-3004-4338-897d-7c7d9fb6f052}"
    assert cond_row["Distance"] == "50.00", "2dp when its metric units"
    assert cond_row["Clock_At_From"] == 1
    assert cond_row["Clock_To"] == 3
    assert cond_row["PACP_Code"] == "OBZ"
    assert cond_row["Remarks"] == "string"
    assert int(cond_row["Joint"]) == 0
    assert cond_row["VCR_Time"] == ""

    # Fetch the continuous defect start row
    cont_start_row = conditions_df[conditions_df["Continuous"].str.startswith("S")].iloc[0]

    assert cont_start_row["InspectionID"] == "{4d797e1a-3004-4338-897d-7c7d9fb6f052}"
    assert cont_start_row["Distance"] == "60.00", "2dp when its metric units"
    assert cont_start_row["Clock_At_From"] == 2
    assert cont_start_row["Clock_To"] == 4
    assert cont_start_row["PACP_Code"] == "OBZ"
    assert cont_start_row["Remarks"] == "string"
    assert int(cont_start_row["Joint"]) == 1, "Bools are represented as ints"
    assert cont_start_row["VCR_Time"] == "001234", "HHMMSS format"

    # Fetch the continuous defect end row
    cont_end_row = conditions_df[conditions_df["Continuous"].str.startswith("F")].iloc[0]

    assert cont_end_row["InspectionID"] == "{4d797e1a-3004-4338-897d-7c7d9fb6f052}"
    assert cont_end_row["Distance"] == "70.00", "2dp when its metric units"
    # Other fields should be the same as the start row
    assert cont_end_row["Clock_At_From"] == 2
    assert cont_end_row["Clock_To"] == 4
    assert cont_end_row["PACP_Code"] == "OBZ"
    assert cont_end_row["Remarks"] == "string"
    assert cont_end_row["Joint"] == 1
    assert cont_end_row["VCR_Time"] == "001234"


def test_report_custom_fields(settings, output_fs, single_insp_with_custom_fields):
    """Check the custom fields are mapped"""

    def mock_handler(req):
        if req.url.path == "/api/v3/organisations/123":
            return httpx.Response(
                200,
                json=_ORG_DATA_TEMPLATE,
            )
        elif req.url.path == "/api/v3/inspections2":
            return httpx.Response(
                200,
                json={
                    "results": [single_insp_with_custom_fields.to_dict()],
                    "count": 1,
                    "next": None,
                    "previous": None,
                },
            )
        elif req.url.path == "/api/v3/inspections/frames":
            return httpx.Response(
                200,
                json={"results": [], "count": 0, "next": None, "previous": None},
            )
        return httpx.Response(404)

    api_client = get_mock_client_with_handler(mock_handler)

    input_data = pacp8_sqlite_export.Input(
        export_id=UUID("00000000-0000-0000-0000-000000000000"),
        target_org_id=123,
        payload=PACP8SQLiteExportPayload(inspection_ids=[UUID("4d797e1a-3004-4338-897d-7c7d9fb6f052")]),
    )

    res = pacp8_sqlite_export.handler(input_data, output_fs, settings, api_client, 0)

    tables = _read_sqlite_to_dfs(output_fs, res.output_file.file_path)
    custom_fields_df = tables["PACP_Custom_Fields"]

    assert len(custom_fields_df) == 1, "One custom field row"

    custom_row = custom_fields_df.iloc[0]
    assert custom_row["InspectionID"] == "{4d797e1a-3004-4338-897d-7c7d9fb6f052}"
    assert custom_row["Custom_Field_One"] == "Custom 1"
    assert custom_row["Custom_Field_Two"] == "Custom 2"
    assert custom_row["Custom_Field_Three"] == "Custom 3"
    assert custom_row["Custom_Field_Four"] == ""
    assert custom_row["Custom_Field_Five"] == ""
    assert custom_row["Custom_Field_Six"] == ""
    assert custom_row["Custom_Field_Seven"] == ""
    assert custom_row["Custom_Field_Eight"] == ""
    assert custom_row["Custom_Field_Nine"] == ""
    assert custom_row["Custom_Field_Ten"] == "Custom 10"
