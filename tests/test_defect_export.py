import datetime
from uuid import UUID

import httpx
import pandas as pd
import pytest
import xmltodict
from vapar.clients import api
from vapar.constants.exports import ExportFormat
from vapar.core.exports import DefectExportPayload

from activities import defect_export
from tests.conftest import get_mock_client_with_handler

_INSPECTION_MODEL_DATA = {
    "uuid": "4d797e1a-3004-4338-897d-7c7d9fb6f052",
    "legacyId": "78761",
    "status": "Uploaded",
    "structuralGrade": 2,
    "serviceGrade": 4,
    "createdBy": None,
    "createdAt": "2024-08-09T00:27:02.397568Z",
    "Direction": "Downstream",
    "Date": "2019-08-20",
    "LengthSurveyed": 3983.0,
    "GeneralRemarks": "some inspection notes",
    "IsImperial": None,
    "Standard": "Australia 2020",
    "ReviewedBy": None,
    "Time": None,
    "AssetID": "asset id",
    "Material": "Vitrified Clay",
    "HeightDiameter": 8,
    "LocationStreet": "Stree 8 4725 Brookwood Drive",
    "LocationTown": "Roanoke",
    "UpstreamNode": "",
    "DownstreamNode": "01B-3932.0",
    "UseOfDrainSewer": "SS",
    "repairCompletedDate": None,
    "folder": {
        "id": 4776,
        "path": "Sub-model testing 09/08/24",
        "depth": 2,
        "numchild": 0,
        "primary_org": None,
        "secondary_org": None,
        "job_name": "Sub-model testing 09/08/24",
        "created_date": "2024-08-09T00:07:15.965074Z",
        "pipe_type_sewer": False,
        "standard_key": 6,
    },
    "asset": {
        "uuid": "4e252512-15f6-4563-aadc-e85aa6ef05ac",
        "type": "pipe",
        "organisation": 123,
        "standard": 0,
        "createdAt": "2024-08-09T00:26:50.162221Z",
        "AssetID": "asset id",
        "UpstreamNode": "",
        "DownstreamNode": "01B-3932.0",
        "HeightDiameter": 8,
        "Material": "Vitrified Clay",
        "UseOfDrainSewer": "SS",
        "LocationStreet": "Stree 8 4725 Brookwood Drive",
        "LocationTown": "Roanoke",
    },
    "file": {
        "id": 87011,
        "url": "uploadedvideofiles/06_03_2024_22_42_34_899046-01b-3933.001b-3932.0_10018_08_20_2019.mp4",
        "hidden": False,
        "filename": "06_03_2024_22_42_34_899046-01b-3933.001b-3932.0_10018_08_20_2019.mp4",
        "file_size": "108.2MB",
        "file_type": "application/octet-stream",
        "upload_org": 237,
        "target_org": 237,
        "upload_user": "VAPAR Admin",
        "uploaded_by": 766,
        "created_time": "2024-08-09T00:07:40.501596Z",
        "job_id": None,
        "job_tree": 4776,
        "upload_completed_type": "2024-08-09T00:08:45.227556Z",
        "upload_completed": True,
        "upload_org_type": "Asset_Owner",
    },
    "extraFields": {
        "ExpectedLength": "3983.0",
        "NameOfSurveyor": "Amanda",
        "CertificateNumber": "AB123",
        "Precleaned": "N",
        "InspectionStage": "CI",
        "NameOfReviewer": "Other Aguilar",
        "ReviewerCertificateNumber": "AB124",
        "Client": "American customer",
        "PONumber": "PO1234",
        "MediaLabel": "Something.mp4",
        "Project": "American Project",
        "SheetNumber": "1",
        "Weather": "1",
        "DateCleaned": "20221101",
        "FlowControlMeasures": "N",
        "PurposeOfInspection": "A",
        "MethodOfInspection": "CC",
        "PressureValue": "104",
        "PipeJointLength": "3",
        "GPS Accuracy": "Good",
        "ReverseSetup": "0",
        "ClientDefined1": "Custom Field One",
        "ClientDefined2": "Custom Field Two",
        "ClientDefined3": "Custom Field Three",
        "ClientDefined4": "Custom Field Four",
        "ClientDefined5": "Custom Field Five",
        "ClientDefined6": "Custom Field Six",
        "ClientDefined7": "Custom Field Seven",
        "ClientDefined8": "Custom Field Eight",
        "ClientDefined9": "Custom Field Nine",
        "ClientDefined10": "Custom Field Ten",
    },
    "toBeMatched": False,
    "ExpectedLength": "3983.0",
    "start_node": "",
    "end_node": "01B-3932.0",
    "location": {"LocationStreet": "Stree 8 4725 Brookwood Drive", "LocationTown": "Roanoke", "country": "AU"},
    "name": "Stree 8 4725 Brookwood Drive, Roanoke",
    "filename": "06_03_2024_22_42_34_899046-01b-3933.001b-3932.0_10018_08_20_2019.mp4",
    "setupLocation": "Upstream",
    "chainage_unit": "m",
    "WorkOrder": "ABC",
}

_FRAME_DATA_TEMPLATE = {
    "id": 0,
    "inspectionId": 0,
    "imageLocation": "an/image/location.jpg",
    "imageUrl": "an/image/url.jpg",
    "frameId": 1,
    "classLabel": "Defect Description 1",
    "classCertainty": ".9",
    "chainage": "150",
    "chainageNumber": "50",
    "isHidden": False,
    "isAccepted": True,
    "allClassBreakdown": "string",
    "atJoint": False,
    "atClock": 1,
    "toClock": 3,
    "contDefectStart": True,
    "contDefectEnd": "-94205.0",
    "quantity1Value": "0",
    "quantity1Units": "m",
    "quantity2Value": "0.5",
    "quantity2Units": "0",
    "remarks": "some frame notes",
    "isMatched": True,
    "parentVideo": 87011,
    "pipeTypeSewer": True,
    "material": "VC",
    "defectId": 1,
    "defectClass": "Defect Description 1",
    "defectCode": "CODE",
    "defectStrScore": "2",
    "defectSerScore": "3",
    "defectScoreSeverity": "3",
    "defectScoreIsShown": True,
    "timeReference": "0:01:23",
}

_ORG_DATA_TEMPLATE = {
    "id": 123,
    "fullName": "Test Org",
    "country": "US",
    "emailDomain": "testorg.com",
    "logoPath": None,
    "orgType": "Contractor",
    "shortName": "testorg",
    "sewerData": "Sewer",
    "standardKey": 3,
    "standardDisplayName": "US V7",
    "linkedOrganisations": [
        {
            "id": 240,
            "fullName": "Linked Test Org",
            "shortName": "linkedorg",
            "orgType": "Asset_Owner",
            "country": "US",
            "standardKey": 3,
            "standardDisplayName": "US V7",
            "emailDomain": "vapar.linkedtestorg.co",
            "logoPath": None,
            "sewerData": "Sewer",
            "subscriptionType": 3,
            "frameSampleRate": 1500,
        }
    ],
}


@pytest.fixture
def inspection() -> api.models.InspectionModel:
    return api.models.InspectionModel.from_dict(_INSPECTION_MODEL_DATA)


@pytest.fixture
def multiple_inspections() -> list[api.models.InspectionModel]:
    i1 = api.models.InspectionModel.from_dict(_INSPECTION_MODEL_DATA)

    # Has some missing data
    i2 = api.models.InspectionModel.from_dict(_INSPECTION_MODEL_DATA)
    i2.uuid = "1a1a1a1a-3004-4338-897d-7c7d9fb6f052"
    i2.legacy_id = "78762"
    i2.structural_grade = 2
    i2.service_grade = 4
    i2.asset.upstream_node = None
    i2.asset.downstream_node = "Unknown"
    i2.length_surveyed = 500
    i2.asset.height_diameter = None
    i2.date = datetime.datetime(2024, 1, 2)
    i2.general_remarks = None

    return [i1, i2]


@pytest.fixture
def frame() -> api.models.FramesList:
    return api.models.FramesList.from_dict(_FRAME_DATA_TEMPLATE)


@pytest.fixture
def settings() -> defect_export.Settings:
    return defect_export.Settings()


@pytest.fixture
def single_inspection_input() -> defect_export.Input:
    return defect_export.Input(
        export_id=UUID("00000000-0000-0000-0000-000000000000"),
        target_org_id=123,
        payload=DefectExportPayload(
            inspection_ids=[UUID("4d797e1a-3004-4338-897d-7c7d9fb6f052")],
            format=ExportFormat.CSV,
        ),
    )


@pytest.fixture
def multiple_inspections_input() -> defect_export.Input:
    return defect_export.Input(
        export_id=UUID("00000000-0000-0000-0000-000000000000"),
        target_org_id=123,
        payload=DefectExportPayload(
            inspection_ids=[
                UUID("4d797e1a-3004-4338-897d-7c7d9fb6f052"),
                UUID("1a1a1a1a-3004-4338-897d-7c7d9fb6f052"),
            ],
            format=ExportFormat.CSV,
        ),
    )


def test_report_for_single_inspection(output_fs, inspection, frame, single_inspection_input, settings):
    def mock_handler(req: httpx.Request):
        if req.url.path == "/api/v3/inspections2":
            return httpx.Response(
                status_code=200,
                json={
                    "count": 1,
                    "results": [inspection.to_dict()],
                    "next": None,
                    "previous": None,
                },
            )

        if req.url.path == "/api/v3/inspections/frames":
            return httpx.Response(
                status_code=200,
                json={
                    "results": [frame.to_dict()],
                    "count": 1,
                    "next": None,
                    "previous": None,
                },
            )

        return httpx.Response(404)

    client = get_mock_client_with_handler(mock_handler)
    resp_data = defect_export.handler(
        input_data=single_inspection_input,
        settings=settings,
        api_client=client,
        max_retry_secs=0,
        output_fs=output_fs,
    )

    out_file_path = resp_data.output_file.file_path
    with output_fs.open(out_file_path, "r") as f:
        df_res = pd.read_csv(f)

    assert len(df_res) == 1, "One inspection == one row"
    row = df_res.iloc[0]

    assert str(row["VAPAR ID"]) == inspection.legacy_id
    assert row["Asset No"] == inspection.asset.asset_id
    assert row["Length"] == inspection.length_surveyed
    assert row["Structural Grade"] == inspection.structural_grade
    assert row["Service Grade"] == inspection.service_grade
    assert row["Diameter"] == inspection.asset.height_diameter
    assert row["Material"] == inspection.asset.material
    assert row["Downstream Node"] == inspection.asset.downstream_node
    assert row["Dir of Travel"] == inspection.direction
    assert row["Frame ID"] == f"Frame {frame.frame_id}"
    assert row["Defect Class"] == frame.defect_class
    assert row["Defect Length"] == int(frame.chainage_number)
    assert row["Defect Code"] == frame.defect_code
    assert row["Remarks"] == frame.remarks
    assert row["Structural Score"] == int(frame.defect_str_score)
    assert row["Service Score"] == int(frame.defect_ser_score)
    assert row["Job Name"] == inspection.folder.additional_properties["job_name"]
    assert row["Work Order"] == inspection.work_order


def test_multiple_inspections(output_fs, multiple_inspections, frame, multiple_inspections_input, settings):
    def mock_handler(req: httpx.Request):
        if req.url.path == "/api/v3/inspections2":
            return httpx.Response(
                status_code=200,
                json={
                    "count": 2,
                    "results": [inspection.to_dict() for inspection in multiple_inspections],
                    "next": None,
                    "previous": None,
                },
            )

        if req.url.path == "/api/v3/inspections/frames":
            return httpx.Response(
                status_code=200,
                json={
                    "results": [frame.to_dict()],
                    "count": 1,
                    "next": None,
                    "previous": None,
                },
            )

        return httpx.Response(404)

    client = get_mock_client_with_handler(mock_handler)
    resp_data = defect_export.handler(
        input_data=multiple_inspections_input,
        settings=settings,
        api_client=client,
        max_retry_secs=0,
        output_fs=output_fs,
    )

    out_file_path = resp_data.output_file.file_path
    with output_fs.open(out_file_path, "r") as f:
        df_res = pd.read_csv(f).fillna("")

    assert len(df_res) == 2, "Two inspections == two rows"

    row_1 = df_res[df_res["VAPAR ID"].astype(str) == multiple_inspections[0].legacy_id].iloc[0]
    row_2 = df_res[df_res["VAPAR ID"].astype(str) == multiple_inspections[1].legacy_id].iloc[0]

    assert str(row_1["VAPAR ID"]) == multiple_inspections[0].legacy_id
    assert row_1["Asset No"] == multiple_inspections[0].asset.asset_id
    assert row_1["Length"] == multiple_inspections[0].length_surveyed
    assert row_1["Structural Grade"] == multiple_inspections[0].structural_grade
    assert row_1["Service Grade"] == multiple_inspections[0].service_grade
    assert row_1["Diameter"] == multiple_inspections[0].asset.height_diameter
    assert row_1["Material"] == multiple_inspections[0].asset.material
    assert row_1["Downstream Node"] == multiple_inspections[0].asset.downstream_node
    assert row_1["Dir of Travel"] == multiple_inspections[0].direction
    assert row_1["Frame ID"] == f"Frame {frame.frame_id}"
    assert row_1["Defect Class"] == frame.defect_class
    assert row_1["Defect Length"] == int(frame.chainage_number)
    assert row_1["Defect Code"] == frame.defect_code
    assert row_1["Remarks"] == frame.remarks
    assert row_1["Structural Score"] == int(frame.defect_str_score)
    assert row_1["Service Score"] == int(frame.defect_ser_score)
    assert row_1["Job Name"] == multiple_inspections[0].folder.additional_properties["job_name"]
    assert row_1["Work Order"] == multiple_inspections[0].work_order

    assert str(row_2["VAPAR ID"]) == multiple_inspections[1].legacy_id
    assert row_2["Asset No"] == multiple_inspections[1].asset.asset_id
    assert row_2["Length"] == multiple_inspections[1].length_surveyed
    assert row_2["Structural Grade"] == multiple_inspections[1].structural_grade
    assert row_2["Service Grade"] == multiple_inspections[1].service_grade
    assert row_2["Material"] == multiple_inspections[1].asset.material
    assert row_2["Downstream Node"] == multiple_inspections[1].asset.downstream_node
    assert row_2["Dir of Travel"] == multiple_inspections[1].direction
    assert row_2["Frame ID"] == f"Frame {frame.frame_id}"
    assert row_2["Defect Class"] == frame.defect_class
    assert row_2["Defect Length"] == int(frame.chainage_number)
    assert row_2["Defect Code"] == frame.defect_code
    assert row_2["Remarks"] == frame.remarks
    assert row_2["Structural Score"] == int(frame.defect_str_score)
    assert row_2["Service Score"] == int(frame.defect_ser_score)
    assert row_2["Job Name"] == multiple_inspections[1].folder.additional_properties["job_name"]
    assert row_2["Work Order"] == multiple_inspections[1].work_order


def test_xml_single_inspection(output_fs, settings, inspection, frame, single_inspection_input):
    def mock_handler(req: httpx.Request):
        if req.url.path == "/api/v3/inspections2":
            return httpx.Response(
                status_code=200,
                json={
                    "count": 1,
                    "results": [inspection.to_dict()],
                    "next": None,
                    "previous": None,
                },
            )

        if req.url.path == "/api/v3/inspections/frames":
            return httpx.Response(
                status_code=200,
                json={
                    "results": [frame.to_dict()],
                    "count": 1,
                    "next": None,
                    "previous": None,
                },
            )

        if req.url.path == "/api/v3/organisations/123":
            return httpx.Response(status_code=200, json=_ORG_DATA_TEMPLATE)

        return httpx.Response(404)

    client = get_mock_client_with_handler(mock_handler)
    single_inspection_input.payload.format = ExportFormat.XML
    resp = defect_export.handler(
        input_data=single_inspection_input,
        settings=settings,
        api_client=client,
        max_retry_secs=0,
        output_fs=output_fs,
    )

    out_file_path = resp.output_file.file_path
    with output_fs.open(out_file_path, "r") as f:
        xml_str = f.read()

    parsed_xml = xmltodict.parse(xml_str)
    survey_data = parsed_xml["SurveyGroup"]["Survey"]
    header = survey_data["Header"]

    assert header["PipelineLengthRef"] == inspection.asset.asset_id
    assert header["Date"] == inspection.date.strftime("%Y-%m-%d")
    assert header["LocationStreet"] == inspection.asset.location_street
    assert header["LocationTown"] == inspection.asset.location_town
    assert header["Direction"] == "D"
    assert header["HeightDiameter"] == str(inspection.asset.height_diameter)
    assert header["GeneralRemarks"] == "VAPAR ID 78761, some inspection notes", "remarks should have legacy id added"
    assert header["ExpectedLength"] == str(inspection.length_surveyed)
    assert header["LengthSurveyed"] == str(inspection.length_surveyed)
    assert header["UseOfDrainSewer"] == "SS"
    assert header["UpstreamNode"] is None
    assert header["DownstreamNode"] == inspection.asset.downstream_node

    observations = survey_data["Observations"]["Observation"]
    assert len(observations) == 2, "reference frame and 1 defect frame"

    ref_frame = observations[0]
    assert ref_frame["VideoRef"] == "0:00:00"
    assert ref_frame["Distance"] == "0.0"
    assert ref_frame["Code"] == "VVR"

    defect_frame = observations[1]
    assert defect_frame["VideoRef"] == frame.time_reference
    assert defect_frame["Distance"] == str(frame.chainage_number)
    assert defect_frame["Code"] == frame.defect_code
    assert defect_frame["Remarks"] == "CODE, some frame notes", "remarks should have defect code added"
    photo_ref = defect_frame["PhotographRefs"]["PhotographRef"]
    assert (
        photo_ref == "Images/asset id__01B-3932.0_50_1.jpg"
    ), "should contain asset id, chainage, start and end nodes and frame id"


def test_xml_multiple_inspections(output_fs, settings, multiple_inspections, frame, multiple_inspections_input):
    def mock_handler(req: httpx.Request):
        if req.url.path == "/api/v3/inspections2":
            return httpx.Response(
                status_code=200,
                json={
                    "count": 2,
                    "results": [inspection.to_dict() for inspection in multiple_inspections],
                    "next": None,
                    "previous": None,
                },
            )

        if req.url.path == "/api/v3/inspections/frames":
            return httpx.Response(
                status_code=200,
                json={
                    "results": [frame.to_dict()],
                    "count": 1,
                    "next": None,
                    "previous": None,
                },
            )

        if req.url.path == "/api/v3/organisations/123":
            return httpx.Response(status_code=200, json=_ORG_DATA_TEMPLATE)

        return httpx.Response(404)

    client = get_mock_client_with_handler(mock_handler)
    multiple_inspections_input.payload.format = ExportFormat.XML
    resp = defect_export.handler(
        input_data=multiple_inspections_input,
        settings=settings,
        api_client=client,
        max_retry_secs=0,
        output_fs=output_fs,
    )

    out_file_path = resp.output_file.file_path
    with output_fs.open(out_file_path, "r") as f:
        xml_str = f.read()

    parsed_xml = xmltodict.parse(xml_str)
    survey_data = parsed_xml["SurveyGroup"]["Survey"]
    assert len(survey_data) == 2, "one survey per inspection"

    assert len(survey_data[0]["Observations"]["Observation"]) == 2, "reference frame and 1 defect frame"
    assert len(survey_data[1]["Observations"]["Observation"]) == 2, "reference frame and 1 defect frame"

    assert survey_data[1]["Header"]["GeneralRemarks"] == "VAPAR ID 78762", "No trailing comma for missing remarks"
