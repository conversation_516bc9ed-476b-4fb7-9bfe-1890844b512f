from uuid import UUID

import httpx
import pytest
from vapar.clients import api

from activities import update_export_status
from tests.conftest import get_mock_client_with_handler


def test_success():
    def api_handler_callback(req):
        assert req.url.path == "/api/v3/exports/00000000-0000-0000-0000-000000000000"
        assert req.method == "PATCH"
        assert req.headers[api.TARGET_ORG_ID_HEADER] == "123"
        return httpx.Response(
            200,
            json={
                "id": "00000000-0000-0000-0000-000000000000",
                "status": "PR",
                "statusReason": None,
                "targetOrg": 123,
                "type": "IP",
                "format": "CSV",
                "updatedAt": "2022-01-01T00:00:00Z",
                "createdAt": "2022-01-01T00:00:00Z",
                "createdBy": 0,
                "completedAt": None,
            },
        )

    input_data = update_export_status.Input(
        export_id=UUID("00000000-0000-0000-0000-000000000000"),
        status=update_export_status.ExportStatus.PROCESSING,
        target_org_id=123,
    )
    client = get_mock_client_with_handler(api_handler_callback)

    update_export_status.handler(input_data, client, max_retry_secs=0)


def test_nonexistent_export():
    def api_handler_callback(req):
        assert req.url.path == "/api/v3/exports/00000000-0000-0000-0000-000000000000"
        assert req.method == "PATCH"
        assert req.headers[api.TARGET_ORG_ID_HEADER] == "123"
        return httpx.Response(404)

    input_data = update_export_status.Input(
        export_id=UUID("00000000-0000-0000-0000-000000000000"),
        status=update_export_status.ExportStatus.PROCESSING,
        target_org_id=123,
    )
    client = get_mock_client_with_handler(api_handler_callback)

    with pytest.raises(api.APIStatusException):
        update_export_status.handler(input_data, client, max_retry_secs=0)
