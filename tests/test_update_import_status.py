from uuid import UUID

import httpx
import pytest
from vapar.clients import api
from vapar.constants.imports import ImportStatusEnum

from activities import update_import_status
from tests.conftest import get_mock_client_with_handler


def test_success():
    def api_handler_callback(req):
        assert req.url.path == "/api/v3/imports/00000000-0000-0000-0000-000000000000"
        assert req.method == "PATCH"
        assert req.headers[api.TARGET_ORG_ID_HEADER] == "123"
        return httpx.Response(
            200,
            json={
                "id": "00000000-0000-0000-0000-000000000000",
                "type": "AS",
                "targetOrg": 130,
                "createdBy": 805,
                "createdAt": "2025-03-11T00:20:20.099774Z",
                "updatedAt": "2025-03-11T00:20:20.396330Z",
                "completedAt": None,
                "validatedAt": "2025-03-11T00:20:20.393495Z",
                "validationStatus": "PA",
                "isHidden": False,
                "status": "PR",
                "statusReason": None,
                "payload": {},
            },
        )

    input_data = update_import_status.Input(
        import_id=UUID("00000000-0000-0000-0000-000000000000"),
        status=ImportStatusEnum.PROCESSING,
        target_org_id=123,
    )
    client = get_mock_client_with_handler(api_handler_callback)

    update_import_status.handler(input_data, client, max_retry_secs=0)


def test_nonexistent_import():
    def api_handler_callback(req):
        assert req.url.path == "/api/v3/imports/00000000-0000-0000-0000-000000000000"
        assert req.method == "PATCH"
        assert req.headers[api.TARGET_ORG_ID_HEADER] == "123"
        return httpx.Response(404)

    input_data = update_import_status.Input(
        import_id=UUID("00000000-0000-0000-0000-000000000000"),
        status=ImportStatusEnum.PROCESSING,
        target_org_id=123,
    )
    client = get_mock_client_with_handler(api_handler_callback)

    with pytest.raises(api.APIStatusException):
        update_import_status.handler(input_data, client, max_retry_secs=0)
