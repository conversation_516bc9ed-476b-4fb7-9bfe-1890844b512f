from uuid import UUID

import httpx
import pandas as pd
import pytest
from dateutil.parser import isoparse
from vapar.clients import api
from vapar.constants.exports import ExportFormat
from vapar.core.exports import RepairRecommendationExportPayload

from activities import repair_recommendation_export
from tests.conftest import get_mock_client_with_handler

_ORGANISATION_DATA = {
    "id": 123,
    "country": "AU",
    "standardKey": 6,
    "standardDisplayName": "Australia 2020",
    "logoPath": None,
    "sewerData": "True",
    "linkedOrganisations": [],
}

_CUSTOM_REPAIR_TYPE_DATA = {"id": 1, "name": "Test Name", "type": "Test Type"}

_INSPECTION_DETAIL_DATA = {
    "name": "Test Name",
    "folder": None,
    "folderName": None,
    "id": 12345,
    "targetOrgId": 123,
    "uploadOrgId": 123,
    "inspection": "11111111-1111-1111-1111-111111111111",
}

_REPAIR_RECOMMENDATION_DATA = {
    "createdAt": "2024-08-09T00:27:02.397568Z",
    "updatedAt": "2025-06-13T02:49:05.672406+00:00",
    "consequence": None,
    "consequenceComment": None,
    "frequency": "6 months",
    "generalNotes": "",
    "id": "13634a5d-47be-4958-a122-36cee6c70643",
    "inspectionId": "11111111-1111-1111-1111-111111111111",
    "junctions": 0,
    "likelihood": None,
    "likelihoodComment": None,
    "vaparPlan": {
        "actionSummary": "Clear pipe at 0.0, ",
        "actor": "vapar",
        "generalNotes": "",
        "items": [
            {
                "actor": "vapar",
                "customDataType": None,
                "customTypeId": None,
                "id": "7d34711f-2bda-4635-a84b-8caca97c05d4",
                "lastUpdated": "2025-06-13T02:49:05.672406+00:00",
                "metadata": [{"key": "Count", "value": 2}],
                "repairCostEstimate": 2108.0,
                "repairType": "Patch",
            },
        ],
    },
    "contractorPlan": {
        "actionSummary": None,
        "actor": "contractor",
        "generalNotes": "",
        "items": [
            {
                "actor": "contractor",
                "customDataType": None,
                "customTypeId": None,
                "id": "7d34711f-2bda-4635-a84b-8caca97c05d4",
                "lastUpdated": "2025-06-13T02:49:05.672406+00:00",
                "metadata": [],
                "repairCostEstimate": 1000.0,
                "repairType": "Lining",
            }
        ],
    },
    "ownerPlan": {
        "actionSummary": None,
        "actor": "owner",
        "generalNotes": "",
        "items": [
            {
                "actor": "owner",
                "customDataType": None,
                "customTypeId": None,
                "id": "7d34711f-2bda-4635-a84b-8caca97c05d4",
                "lastUpdated": "2025-06-13T02:49:05.672406+00:00",
                "metadata": [],
                "repairCostEstimate": 1000.0,
                "repairType": "Lining",
            },
        ],
    },
}

_CUSTOM_REPAIR_VALUE_DATA = {
    "id": 1,
    "cValueText": None,
    "cValueNumber": None,
    "cValueBool": None,
    "oValueText": None,
    "oValueNumber": None,
    "oValueBool": True,
    "custom_Repair_Type": 1,
}

_FILE_INFO_DATA = {
    "id": 1,
    "url": "www.testurl.com/file.mp4",
    "hidden": False,
    "filename": "file.mp4",
    "fileSize": "5MB",
    "fileType": "mp4",
    "uploadOrg": 123,
    "targetOrg": 123,
    "uploadUser": "Test User",
    "uploadedBy": 1,
    "createdTime": "2024-08-08T00:27:02.397568Z",
}

_INSPECTION_MODEL_DATA = {
    "id": 12345,
    "uuid": "11111111-1111-1111-1111-111111111111",
    "legacy_id": 12345,
    "status": "Reviewed",
    "direction": "Upstream",
    "structural_grade": "1",
    "service_grade": "1",
    "date": "2024-01-01",
    "length_surveyed": 10.5,
    "work_order": "WO123",
    "general_remarks": "Test remarks",
}

_ASSET_MODEL_DATA = {
    "uuid": "*************-3333-3333-************",
    "type": "Pipe",
    "organisation": 123,
    "createdAt": "2024-01-01T00:27:02.397568Z",
    "assetId": "ASSET123",
    "heightDiameter": "250",
    "material": "PVC",
    "locationStreet": "Test Street",
    "locationTown": "Test Town",
    "upstreamNode": "US123",
    "downstreamNode": "DS123",
}


@pytest.fixture
def organisation() -> api.models.Organisation:
    return api.models.Organisation.from_dict(_ORGANISATION_DATA)


@pytest.fixture
def custom_repair_types() -> list[api.models.CustomRepairType]:
    return [api.models.CustomRepairType.from_dict(_CUSTOM_REPAIR_TYPE_DATA)]


@pytest.fixture
def mappointlists() -> list[api.models.InspectionDetail]:
    m1 = api.models.InspectionDetail.from_dict(_INSPECTION_DETAIL_DATA)

    m2 = api.models.InspectionDetail.from_dict(_INSPECTION_DETAIL_DATA)
    m2.id = 54321
    m2.name = "Test Name 2"
    m2.inspection = "*************-2222-2222-************"

    return [m1, m2]


@pytest.fixture
def repair_recommendations() -> list[api.models.RepairRecommendation]:
    r1 = api.models.RepairRecommendation.from_dict(_REPAIR_RECOMMENDATION_DATA)

    r2 = api.models.RepairRecommendation.from_dict(_REPAIR_RECOMMENDATION_DATA)
    r2.id = 2
    r2.created_at = isoparse("2024-01-01T00:27:02.397568Z")
    r2.target = 54321

    return [r1, r2]


@pytest.fixture
def custom_repair_values() -> list[api.models.CustomRepairValue]:
    return [api.models.CustomRepairValue.from_dict(_CUSTOM_REPAIR_VALUE_DATA)]


@pytest.fixture
def inspections() -> api.models.InspectionModel:
    file = api.models.FileInfo.from_dict(_FILE_INFO_DATA)
    asset = api.models.AssetModel.from_dict(_ASSET_MODEL_DATA)

    i1 = api.models.InspectionModel.from_dict(_INSPECTION_MODEL_DATA)
    i1.file = file
    i1.asset = asset
    i1.legacy_id = 12345

    i2 = api.models.InspectionModel.from_dict(_INSPECTION_MODEL_DATA)
    i2.file = file
    i2.asset = asset
    i2.legacy_id = 54321
    i2.uuid = "*************-2222-2222-************"

    return [i1, i2]


@pytest.fixture
def settings() -> repair_recommendation_export.Settings:
    return repair_recommendation_export.Settings()


@pytest.fixture
def single_inspection_input() -> repair_recommendation_export.Input:
    return repair_recommendation_export.Input(
        export_id=UUID("00000000-0000-0000-0000-000000000000"),
        target_org_id=123,
        payload=RepairRecommendationExportPayload(
            inspection_ids=["11111111-1111-1111-1111-111111111111"],
            format=ExportFormat.CSV,
        ),
    )


@pytest.fixture
def multiple_inspections_input() -> repair_recommendation_export.Input:
    return repair_recommendation_export.Input(
        export_id=UUID("00000000-0000-0000-0000-000000000000"),
        target_org_id=123,
        payload=RepairRecommendationExportPayload(
            inspection_ids=["11111111-1111-1111-1111-111111111111", "*************-2222-2222-************"],
            format=ExportFormat.CSV,
        ),
    )


def test_single_inspection(
    output_fs,
    single_inspection_input,
    settings,
    organisation,
    custom_repair_types,
    mappointlists,
    repair_recommendations,
    custom_repair_values,
    inspections,
):
    def mock_handler(req: httpx.Request):
        if req.url.path == "/api/v3/organisations/123":
            return httpx.Response(status_code=200, json=organisation.to_dict())

        if req.url.path == "/api/v3/repairs/12345/custom-types":
            return httpx.Response(status_code=200, json=[custom_repair_types[0].to_dict()])

        if req.url.path == "/api/v3/inspections/12345":
            return httpx.Response(status_code=200, json=mappointlists[0].to_dict())

        if req.url.path == "/api/v3/inspections/11111111-1111-1111-1111-111111111111/repairplan":
            return httpx.Response(status_code=200, json=repair_recommendations[0].to_dict())

        if req.url.path == "/api/v3/repairs/12345/custom-value":
            return httpx.Response(status_code=200, json=[custom_repair_values[0].to_dict()])

        if req.url.path == "/api/v3/inspections2/11111111-1111-1111-1111-111111111111":
            return httpx.Response(status_code=200, json=inspections[0].to_dict())

        return httpx.Response(404)

    client = get_mock_client_with_handler(mock_handler)
    resp_data = repair_recommendation_export.handler(
        input_data=single_inspection_input,
        settings=settings,
        api_client=client,
        max_retry_secs=0,
        output_fs=output_fs,
    )

    out_file_path = resp_data.output_file.file_path
    with output_fs.open(out_file_path, "r") as f:
        df_res = pd.read_csv(f)

    assert len(df_res) == 2, "One automated suggestion and one contractor recommendation"
    row = df_res.iloc[0]

    assert row["ID"] == mappointlists[0].id
    assert row["Patching Required"]
    assert row["Patches"] == 2
    assert row["Repair Cost Estimate"] == 2108.0


def test_multiple_inspections(
    output_fs,
    multiple_inspections_input,
    settings,
    organisation,
    custom_repair_types,
    mappointlists,
    repair_recommendations,
    custom_repair_values,
    inspections,
):
    def mock_handler(req: httpx.Request):
        if req.url.path == "/api/v3/organisations/123":
            return httpx.Response(status_code=200, json=organisation.to_dict())

        if req.url.path in [
            "/api/v3/repairs/12345/custom-types",
            "/api/v3/repairs/54321/custom-types",
        ]:
            return httpx.Response(status_code=200, json=[custom_repair_types[0].to_dict()])

        if req.url.path == "/api/v3/inspections/12345":
            return httpx.Response(status_code=200, json=mappointlists[0].to_dict())

        if req.url.path == "/api/v3/inspections/54321":
            return httpx.Response(status_code=200, json=mappointlists[1].to_dict())

        if req.url.path == "/api/v3/inspections/11111111-1111-1111-1111-111111111111/repairplan":
            return httpx.Response(status_code=200, json=repair_recommendations[0].to_dict())

        if req.url.path == "/api/v3/inspections/*************-2222-2222-************/repairplan":
            return httpx.Response(status_code=200, json=repair_recommendations[1].to_dict())

        if req.url.path in [
            "/api/v3/repairs/12345/custom-value",
            "/api/v3/repairs/54321/custom-value",
        ]:
            return httpx.Response(status_code=200, json=[custom_repair_values[0].to_dict()])

        if req.url.path == "/api/v3/inspections2/11111111-1111-1111-1111-111111111111":
            return httpx.Response(status_code=200, json=inspections[0].to_dict())

        if req.url.path == "/api/v3/inspections2/*************-2222-2222-************":
            return httpx.Response(status_code=200, json=inspections[1].to_dict())

        return httpx.Response(404)

    client = get_mock_client_with_handler(mock_handler)
    resp_data = repair_recommendation_export.handler(
        input_data=multiple_inspections_input,
        settings=settings,
        api_client=client,
        max_retry_secs=0,
        output_fs=output_fs,
    )

    out_file_path = resp_data.output_file.file_path
    with output_fs.open(out_file_path, "r") as f:
        df_res = pd.read_csv(f).fillna("")

    assert len(df_res) == 4, "Two automated suggestions and two contractor recommendations"

    row_1 = df_res[df_res["ID"] == mappointlists[0].id].iloc[0]
    row_2 = df_res[df_res["ID"] == mappointlists[1].id].iloc[0]
    row_3 = df_res[df_res["ID"] == mappointlists[1].id].iloc[1]

    assert row_1["ID"] == mappointlists[0].id
    assert row_2["ID"] == mappointlists[1].id
    assert row_3["Lining Required"]
    assert row_3["Repair Cost Estimate"] == 1000.0
