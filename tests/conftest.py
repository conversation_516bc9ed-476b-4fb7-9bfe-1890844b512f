from collections.abc import Callable
from pathlib import Path

import httpx
import pytest
from fsspec.implementations.dirfs import DirFileSystem
from fsspec.implementations.local import LocalFileSystem
from vapar.clients import api

from common.constants import PROJECT_ROOT_DIR

_TEST_OUTPUTS = PROJECT_ROOT_DIR / "test_outputs"


@pytest.fixture
def outputs_path() -> Path:
    return _TEST_OUTPUTS


@pytest.fixture
def resources_path() -> Path:
    return PROJECT_ROOT_DIR / "tests" / "resources"


@pytest.fixture
def resources_fs(resources_path) -> DirFileSystem:
    return DirFileSystem(str(resources_path), LocalFileSystem(auto_mkdir=True))


@pytest.fixture
def videos_path(resources_path):
    return resources_path / "videos"


@pytest.fixture
def xml_path(resources_path):
    return resources_path / "xml"


@pytest.fixture
def fixtures_path(resources_path):
    return resources_path / "fixtures"


@pytest.fixture
def output_fs(outputs_path):
    return DirFileSystem(str(outputs_path), LocalFileSystem(auto_mkdir=True))


def get_mock_client_with_handler(handler: Callable[[httpx.Request], httpx.Response]) -> api.AuthenticatedClient:
    return api.AuthenticatedClient(
        base_url="https://not-a-real-domain.com",
        token="not-a-real-token",
        prefix="",
        auth_header_name="X-API-KEY",
        httpx_args={"transport": httpx.MockTransport(handler)},
    )
