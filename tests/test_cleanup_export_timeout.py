import json

import httpx
import pytest
from vapar.constants.exports import ExportStatus, ExportStatusReason

from activities import cleanup_export_timeout
from tests.conftest import get_mock_client_with_handler


@pytest.fixture
def settings():
    return cleanup_export_timeout.Settings(
        export_pending_timeout_minutes=20,
        export_processing_timeout_minutes=30,
    )


@pytest.mark.asyncio
async def test_basic_cleanup(settings):
    def _handler(request: httpx.Request):
        if request.url.path == "/api/v3/exports/status" and request.method == "PATCH":
            query_str = request.url.query.decode()
            assert "status=PE" in query_str or "status=PR" in query_str, "Should be filtering by status"
            assert "updated_at__lte=" in query_str, "Should be filtering by last updated time"

            body = json.loads(request.read())
            assert body["status"] == ExportStatus.FAILED.value
            assert body["statusReason"] == ExportStatusReason.TIMEOUT.value

            return httpx.Response(status_code=204)
        return httpx.Response(status_code=404)

    mock_api_client = get_mock_client_with_handler(_handler)
    await cleanup_export_timeout.handler(mock_api_client, settings)
