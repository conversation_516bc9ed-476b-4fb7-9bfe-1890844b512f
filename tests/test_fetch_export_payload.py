from uuid import UUID

import httpx
import pytest
from vapar.clients import api
from vapar.core.exports import BulkInspectionPDFExportPayload

from activities import fetch_export_payload
from tests.conftest import get_mock_client_with_handler


@pytest.fixture
def mock_api_client() -> api.AuthenticatedClient:
    def _handler(request: httpx.Request):
        if request.url.path == "/api/v3/exports/bff08f36-19eb-4636-997a-a57e79e59db7/payload":
            return httpx.Response(
                status_code=200,
                json={
                    "type": "BI",
                    "format": "PDF",
                    "inspectionIds": ["2b3b3b3b-3b3b-3b3b-3b3b-3b3b3b3b3b3b"],
                    "runValidation": False,
                },
            )
        return httpx.Response(status_code=404)

    return get_mock_client_with_handler(_handler)


def test_successful_fetch(mock_api_client):
    input_data = fetch_export_payload.Input(export_id=UUID("bff08f36-19eb-4636-997a-a57e79e59db7"), target_org_id=123)
    resp = fetch_export_payload.handler(input_data, mock_api_client, max_retry_secs=0)
    assert resp.full_payload.root == BulkInspectionPDFExportPayload(
        type="BI", format="PDF", inspection_ids=["2b3b3b3b-3b3b-3b3b-3b3b-3b3b3b3b3b3b"]
    )


def test_nonexistent_export_id(mock_api_client):
    nonexistent_uuid = UUID("00000000-0000-0000-0000-000000000000")
    input_data = fetch_export_payload.Input(export_id=nonexistent_uuid, target_org_id=123)
    with pytest.raises(api.APIStatusException):
        fetch_export_payload.handler(input_data, mock_api_client, max_retry_secs=0)
