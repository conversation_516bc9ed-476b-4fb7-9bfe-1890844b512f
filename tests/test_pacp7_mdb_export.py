from copy import deepcopy
from datetime import datetime
from pathlib import Path
from uuid import UUID

import httpx
import jayde<PERSON>api
import pandas as pd
import pytest
from fsspec.implementations.dirfs import DirFileSystem
from vapar.core.exports import PACP7MDBExportPayload

from activities import pacp7_mdb_export
from common.defects import APISubStandardAllDefectsProvider
from common.standards import APISubstandardProvider
from tests.conftest import get_mock_client_with_handler

pytestmark = pytest.mark.filterwarnings("ignore:.*jpype._core.isThreadAttachedToJVM is deprecated.*")

_OUTPUTS_FOLDER = "export-outputs"


_PACP_INSPECTIONS_COLS = [
    "InspectionID",
    "Surveyed_BY",
    "CERTIFICATE_NUMBER",
    "Direction",
    "Inspection_Status",
    "Inspection_Date",
    "PreCleaning",
    "Owner",
    "Customer",
    "Project",
    "Height",
    "Total_Length",
    "Length_Surveyed",
    "Inspection_Technology_Used_CCTV",
    "Pipe_Segment_Reference",
    "Street",
    "City",
    "Pipe_Use",
    "Material",
    "Downstream_MH",
    "Upstream_MH",
    "Additional_Info",
]

_PACP_CONDITIONS_COLS = [
    "ConditionID",
    "InspectionID",
    "Distance",
    "PACP_Code",
    "Value_1st_Dimension",
    "Value_2nd_Dimension",
    "Value_Percent",
    "Joint",
    "Clock_At_From",
    "Clock_To",
    "Remarks",
    "Continuous",
]

_ORG_DATA_TEMPLATE = {
    "id": 123,
    "fullName": "Test Org",
    "country": "AU",
    "emailDomain": "test-org.com",
    "logoPath": None,
    "orgType": "Asset_Owner",
    "shortName": "TO",
    "sewerData": "Sewer",
    "standardKey": 6,
    "standardDisplayName": "Australia 2013",
    "linkedOrganisations": [],
}


_INSPECTION_DATA_TEMPLATE = {
    "uuid": "4d797e1a-3004-4338-897d-7c7d9fb6f052",
    "legacyId": "78761",
    "status": "Uploaded",
    "structuralGrade": 2,
    "serviceGrade": 4,
    "createdBy": None,
    "createdAt": "2024-08-09T00:27:02.397568Z",
    "Direction": "Downstream",
    "Date": "2019-08-20",
    "LengthSurveyed": 3983.0,
    "GeneralRemarks": "",
    "IsImperial": None,
    "Standard": "Australia 2020",
    "ReviewedBy": None,
    "Time": None,
    "AssetID": "asset id",
    "Material": "vitrifiedclay",
    "HeightDiameter": 8,
    "LocationStreet": "Stree 8 4725 Brookwood Drive",
    "LocationTown": "Roanoke",
    "UpstreamNode": "US Node",
    "DownstreamNode": "DS Node",
    "UseOfDrainSewer": "SS",
    "repairCompletedDate": None,
    "folder": {
        "id": 4776,
        "path": "Sub-model testing 09/08/24",
        "depth": 2,
        "numchild": 0,
        "primary_org": None,
        "secondary_org": None,
        "job_name": "Sub-model testing 09/08/24",
        "created_date": "2024-08-09T00:07:15.965074Z",
        "pipe_type_sewer": False,
        "standard_key": 6,
    },
    "asset": {
        "uuid": "4e252512-15f6-4563-aadc-e85aa6ef05ac",
        "type": "pipe",
        "organisation": 237,
        "standard": 0,
        "createdAt": "2024-08-09T00:26:50.162221Z",
        "AssetID": "",
        "UpstreamNode": None,
        "DownstreamNode": "01B-3932.0",
        "HeightDiameter": 8,
        "Material": "vitrifiedclay",
        "UseOfDrainSewer": "SS",
        "LocationStreet": "Stree 8 4725 Brookwood Drive",
        "LocationTown": "Roanoke",
    },
    "file": {
        "id": 87011,
        "url": "uploadedvideofiles/06_03_2024_22_42_34_899046-01b-3933.001b-3932.0_10018_08_20_2019.mp4",
        "hidden": False,
        "filename": "06_03_2024_22_42_34_899046-01b-3933.001b-3932.0_10018_08_20_2019.mp4",
        "file_size": "108.2MB",
        "file_type": "application/octet-stream",
        "upload_org": 123,
        "target_org": 123,
        "upload_user": "VAPAR Admin",
        "uploaded_by": 766,
        "created_time": "2024-08-09T00:07:40.501596Z",
        "job_id": None,
        "job_tree": 4776,
        "upload_completed_time": "2024-08-09T00:08:45.227556Z",
        "upload_completed": True,
        "upload_org_type": "Asset_Owner",
    },
    "extraFields": {"ExpectedLength": "3983.0"},
    "toBeMatched": False,
    "ExpectedLength": "3983.0",
    "start_node": "US Node",
    "end_node": "DS Node",
    "location": {"LocationStreet": "Stree 8 4725 Brookwood Drive", "LocationTown": "Roanoke", "country": "AU"},
    "name": "Stree 8 4725 Brookwood Drive, Roanoke",
    "filename": "06_03_2024_22_42_34_899046-01b-3933.001b-3932.0_10018_08_20_2019.mp4",
    "SetupLocation": "Upstream",
    "chainage_unit": "m",
}

_FRAME_DATA_TEMPLATE = {
    "id": 0,
    "inspectionId": 0,
    "imageLocation": "string",
    "imageUrl": "string",
    "frameId": 1,
    "classLabel": "Defect Description 1",
    "classCertainty": ".9",
    "chainage": "string",
    "chainageNumber": "50",
    "isHidden": False,
    "isAccepted": True,
    "allClassBreakdown": "string",
    "atJoint": False,
    "atClock": **********,
    "toClock": **********,
    "contDefectStart": False,
    "contDefectEnd": None,
    "quantity1Value": "0",
    "quantity1Units": "m",
    "quantity2Value": "0.",
    "quantity2Units": "0",
    "remarks": "string",
    "isMatched": True,
    "parentVideo": 87011,
    "pipeTypeSewer": True,
    "material": "string",
    "defectId": 0,
    "defectClass": "Defect Description 1",
    "defectCode": "OBZ",
    "defectStrScore": "2",
    "defectSerScore": "3",
    "defectScoreSeverity": "string",
    "defectScoreIsShown": True,
    "timeReference": "string",
}

_DEFECT_DATA_TEMPLATE = {
    "id": 1,
    "substandard": {
        "id": 0,
        "materialType": "Vitrified Clay",
        "pipeTypeSewer": True,
        "comment": "string",
        "standard": {"id": 0, "displayName": "Standard 1", "name": "Standard 1"},
        "region": "AU",
    },
    "defectDescription": "Defect Description 1",
    "defectModelName": "Defect Model Name 1",
    "defectModelId": 0,
    "serviceScore": "2",
    "structuralScore": "3",
    "defectType": "string",
    "quantity1DefaultVal": 0,
    "quantity1Units": "string",
    "quantity2DefaultVal": 0,
    "quantity2Units": "string",
    "defectCode": "string",
    "continuousScore": True,
    "materialApplied": "string",
    "characterisation1": "string",
    "characterisation2": "string",
    "clockPositionRequired": True,
    "clockSpreadPossible": True,
    "percentageRequired": True,
    "startSurvey": True,
    "endSurvey": True,
    "repairPriority": **********,
    "repairCategory": "Roots",
    "fastpassCode": True,
    "atJointRequired": True,
}

_SUBSTD_DATA_TEMPLATE = {
    "id": 0,
    "materialType": "string",
    "pipeTypeSewer": True,
    "comment": "string",
    "standard": {"id": 0, "displayName": "string", "name": "string"},
    "region": "UK",
}


@pytest.fixture(autouse=True)
def clear_cached():
    APISubstandardProvider._GLOBAL_SUBSTANDARD_CACHE.clear()
    APISubStandardAllDefectsProvider._GLOBAL_DEFECT_CACHE.clear()


@pytest.fixture
def multiple_frames_data():
    f1 = _FRAME_DATA_TEMPLATE.copy()
    f1["frameId"] = 1
    f1["classLabel"] = "Defect Description 1"
    f1["imageLocation"] = "image1.jpg"
    f1["defectId"] = 1

    f2 = _FRAME_DATA_TEMPLATE.copy()
    f2["frameId"] = 2
    f2["classLabel"] = "Defect Description 2"
    f2["imageLocation"] = "image2.jpg"
    f2["defectId"] = 2

    f3 = _FRAME_DATA_TEMPLATE.copy()
    f3["frameId"] = 3
    f3["classLabel"] = "Defect Description 3"
    f3["imageLocation"] = "image3.jpg"
    f3["defectCode"] = None  # This frame should be skipped
    f3["defectId"] = None

    return [f1, f2, f3]


@pytest.fixture
def multiple_defects_data():
    d1 = deepcopy(_DEFECT_DATA_TEMPLATE)
    d1["id"] = 1
    d1["defectDescription"] = "Defect Description 1"
    d1["defectModelName"] = "Defect Model Name 1"
    d1["defectModelId"] = 1

    d2 = deepcopy(_DEFECT_DATA_TEMPLATE)
    d2["id"] = 2
    d2["defectDescription"] = "Defect Description 2"
    d2["defectModelName"] = "Defect Model Name 2"
    d2["defectModelId"] = 2

    return [d1, d2]


@pytest.fixture
def multiple_inspections_data():
    i1 = deepcopy(_INSPECTION_DATA_TEMPLATE)
    i1["uuid"] = "00000000-0000-0000-0000-000000000000"
    i1["legacyId"] = "1"
    i1["asset"]["LocationStreet"] = "Street 1"
    i1["UpstreamNode"] = "US Node 1"
    i1["DownstreamNode"] = "DS Node 1"
    i1["Direction"] = "Upstream"
    i1["start_node"] = "DS Node 1"
    i1["end_node"] = "US Node 1"

    i2 = deepcopy(_INSPECTION_DATA_TEMPLATE)
    i2["uuid"] = "00000000-0000-0000-0000-000000000001"
    i2["legacyId"] = "2"
    i2["asset"]["LocationStreet"] = "Street 2"
    i2["UpstreamNode"] = "US Node 2"
    i2["DownstreamNode"] = "DS Node 2"
    i2["start_node"] = "US Node 2"
    i2["end_node"] = "DS Node 2"

    return [i1, i2]


@pytest.fixture
def settings():
    return pacp7_mdb_export.Settings(export_outputs_folder_name=_OUTPUTS_FOLDER)


def read_mdb_table_to_df(cursor: jaydebeapi.Cursor, table_name: str, columns: list[str]) -> pd.DataFrame:
    cursor.execute(f"SELECT {', '.join(columns)} FROM {table_name}")
    rows = cursor.fetchall()
    return pd.DataFrame(rows, columns=columns, dtype=str)  # All columns as string for simpler comparisons


def read_mdb_to_dfs(output_fs: DirFileSystem, outfile_path: str):
    full_path = Path(output_fs.path) / outfile_path
    with pacp7_mdb_export.connect_to_access_db(full_path) as conn, conn.cursor() as cursor:
        return {
            "PACP_Inspections": read_mdb_table_to_df(cursor, "PACP_Inspections", _PACP_INSPECTIONS_COLS),
            # "PACP_Ratings": read_mdb_table_to_df(cursor, "PACP_Ratings"),
            # "PACP_Media_Inspections": read_mdb_table_to_df(cursor, "PACP_Media_Inspections"),
            "PACP_Conditions": read_mdb_table_to_df(cursor, "PACP_Conditions", _PACP_CONDITIONS_COLS),
            # "PACP_Media_Conditions": read_mdb_table_to_df(cursor, "PACP_Media_Conditions"),
        }


def jdbc_date_to_str(date: str) -> str:
    # 2019-08-20 00:00:00 -> 2019-08-20
    return datetime.strptime(date, "%Y-%m-%d %H:%M:%S").strftime("%Y-%m-%d")


def test_no_inspections(output_fs, settings):
    def mock_handler(req):
        if req.url.path == "/api/v3/organisations/123":
            return httpx.Response(
                200,
                json=_ORG_DATA_TEMPLATE,
            )
        if req.url.path == "/api/v3/inspections2":
            return httpx.Response(
                200,
                json={"results": [], "count": 0, "next": None, "previous": None},
            )

        if req.url.path == "/api/v3/inspections/frames":
            return httpx.Response(
                200,
                json={"results": [], "count": 0, "next": None, "previous": None},
            )

        return httpx.Response(404)

    api_client = get_mock_client_with_handler(mock_handler)

    input_data = pacp7_mdb_export.Input(
        export_id=UUID("00000000-0000-0000-0000-000000000000"),
        target_org_id=123,
        payload=PACP7MDBExportPayload(inspection_ids=[]),
    )
    res = pacp7_mdb_export.handler(input_data, output_fs, settings, api_client)

    outfile_path = res.output_file.file_path
    dfs = read_mdb_to_dfs(output_fs, outfile_path)
    assert len(dfs["PACP_Inspections"]) == 0


def test_single_inspection_no_frames(output_fs, settings):
    def mock_handler(req):
        if req.url.path == "/api/v3/organisations/123":
            return httpx.Response(
                200,
                json=_ORG_DATA_TEMPLATE,
            )
        elif req.url.path == "/api/v3/inspections2":
            return httpx.Response(
                200,
                json={"results": [_INSPECTION_DATA_TEMPLATE], "count": 1, "next": None, "previous": None},
            )
        elif req.url.path == "/api/v3/inspections/frames":
            return httpx.Response(
                200,
                json={"results": [], "count": 0, "next": None, "previous": None},
            )
        elif req.url.path == "/api/v3/standards/subcategories":
            return httpx.Response(
                200,
                json={"results": [_SUBSTD_DATA_TEMPLATE], "count": 1, "next": None, "previous": None},
            )

        elif req.url.path == "/api/v3/standards/defects":
            return httpx.Response(
                200,
                json=[_DEFECT_DATA_TEMPLATE],
            )
        return httpx.Response(404)

    api_client = get_mock_client_with_handler(mock_handler)

    input_data = pacp7_mdb_export.Input(
        export_id=UUID("00000000-0000-0000-0000-000000000000"),
        target_org_id=123,
        payload=PACP7MDBExportPayload(inspection_ids=[UUID("00000000-0000-0000-0000-000000000000")]),
    )
    res = pacp7_mdb_export.handler(input_data, output_fs, settings, api_client, max_retry_secs=0)

    assert res.output_file.file_display_name.startswith("TO"), "Display name should start with org short name"
    assert res.output_file.file_display_name.endswith(".mdb"), "MS-Access file extension is .mdb"

    outfile_path = res.output_file.file_path
    dfs = read_mdb_to_dfs(output_fs, outfile_path)
    assert len(dfs["PACP_Inspections"]) == 1
    insp_row = dfs["PACP_Inspections"].iloc[0]

    assert insp_row["Direction"] == "D"
    assert insp_row["Inspection_Status"] == "CI"
    assert jdbc_date_to_str(insp_row["Inspection_Date"]) == "2019-08-20"
    assert insp_row["Upstream_MH"] == "US Node"
    assert insp_row["Downstream_MH"] == "DS Node"
    assert insp_row["Owner"] == "Test Org"
    assert insp_row["Customer"] == "Test Org"
    assert insp_row["Material"] == "VCP"
    assert insp_row["Pipe_Use"] == "SS"
    assert insp_row["Additional_Info"] == "78761", "Legacy ID"
    assert insp_row["Height"] == "8"


@pytest.fixture
def single_insp_with_frames_client(multiple_frames_data):
    def handler(req):
        if req.url.path == "/api/v3/organisations/123":
            return httpx.Response(
                200,
                json=_ORG_DATA_TEMPLATE,
            )
        elif req.url.path == "/api/v3/inspections2":
            return httpx.Response(
                200,
                json={"results": [_INSPECTION_DATA_TEMPLATE], "count": 1, "next": None, "previous": None},
            )
        elif req.url.path == "/api/v3/inspections/frames":
            return httpx.Response(
                200,
                json={"results": multiple_frames_data, "count": 3, "next": None, "previous": None},
            )
        elif req.url.path == "/api/v3/standards/subcategories":
            return httpx.Response(
                200,
                json={"results": [_SUBSTD_DATA_TEMPLATE], "count": 1, "next": None, "previous": None},
            )

        elif req.url.path == "/api/v3/standards/defects":
            return httpx.Response(
                200,
                json=[_DEFECT_DATA_TEMPLATE],
            )

        return httpx.Response(404)

    return get_mock_client_with_handler(handler)


@pytest.fixture
def multiple_insp_client(multiple_inspections_data, multiple_frames_data):
    def handler(req):
        if req.url.path == "/api/v3/organisations/123":
            return httpx.Response(
                200,
                json=_ORG_DATA_TEMPLATE,
            )
        elif req.url.path == "/api/v3/inspections2":
            return httpx.Response(
                200,
                json={"results": multiple_inspections_data, "count": 2, "next": None, "previous": None},
            )
        elif req.url.path == "/api/v3/inspections/frames":
            return httpx.Response(
                200,
                json={"results": multiple_frames_data, "count": 3, "next": None, "previous": None},
            )
        elif req.url.path == "/api/v3/standards/subcategories":
            return httpx.Response(
                200,
                json={"results": [_SUBSTD_DATA_TEMPLATE], "count": 1, "next": None, "previous": None},
            )

        elif req.url.path == "/api/v3/standards/defects":
            return httpx.Response(
                200,
                json=[_DEFECT_DATA_TEMPLATE],
            )

        return httpx.Response(404)

    return get_mock_client_with_handler(handler)


def test_singe_inspection_with_frames(output_fs, settings, single_insp_with_frames_client):
    input_data = pacp7_mdb_export.Input(
        export_id=UUID("00000000-0000-0000-0000-000000000000"),
        target_org_id=123,
        payload=PACP7MDBExportPayload(inspection_ids=[UUID("00000000-0000-0000-0000-000000000000")]),
    )
    res = pacp7_mdb_export.handler(input_data, output_fs, settings, single_insp_with_frames_client, max_retry_secs=0)

    outfile_path = res.output_file.file_path
    dfs = read_mdb_to_dfs(output_fs, outfile_path)
    assert len(dfs["PACP_Inspections"]) == 1
    insp_row = dfs["PACP_Inspections"].iloc[0]

    insp_row_id = insp_row["InspectionID"]

    conds_df = dfs["PACP_Conditions"].sort_values("ConditionID")
    assert len(conds_df) == 2, "One row per frame with defect"
    assert all(conds_df["InspectionID"] == insp_row_id), "All frames belong to the inspection"


def test_multiple_inspections(output_fs, settings, multiple_insp_client):
    input_data = pacp7_mdb_export.Input(
        export_id=UUID("00000000-0000-0000-0000-000000000000"),
        target_org_id=123,
        payload=PACP7MDBExportPayload(
            inspection_ids=[
                UUID("00000000-0000-0000-0000-000000000000"),
                UUID("00000000-0000-0000-0000-000000000001"),
            ]
        ),
    )
    res = pacp7_mdb_export.handler(input_data, output_fs, settings, multiple_insp_client, max_retry_secs=0)

    outfile_path = res.output_file.file_path
    dfs = read_mdb_to_dfs(output_fs, outfile_path)

    insps_df = dfs["PACP_Inspections"]
    assert len(insps_df) == 2, "One row per inspection"

    insp_1 = insps_df[insps_df["Additional_Info"] == "1"].iloc[0]
    assert insp_1["Street"] == "Street 1"
    assert insp_1["Upstream_MH"] == "US Node 1"
    assert insp_1["Downstream_MH"] == "DS Node 1"

    insp_2 = insps_df[insps_df["Additional_Info"] == "2"].iloc[0]
    assert insp_2["Street"] == "Street 2"
    assert insp_2["Upstream_MH"] == "US Node 2"
    assert insp_2["Downstream_MH"] == "DS Node 2"


@pytest.mark.skip("This test is for debugging and is very slow")
def test_intermittent_fk_bug(output_fs, settings, multiple_insp_client):
    """For testing foreign key errors that occur when specifying PKs - AD-2415"""
    for _ in range(1000):
        input_data = pacp7_mdb_export.Input(
            export_id=UUID("00000000-0000-0000-0000-000000000000"),
            target_org_id=123,
            payload=PACP7MDBExportPayload(
                inspection_ids=[
                    UUID("00000000-0000-0000-0000-000000000000"),
                    UUID("00000000-0000-0000-0000-000000000001"),
                ]
            ),
        )
        pacp7_mdb_export.handler(input_data, output_fs, settings, multiple_insp_client, max_retry_secs=0)
