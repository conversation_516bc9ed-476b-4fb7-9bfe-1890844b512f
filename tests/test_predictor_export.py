import datetime
from uuid import UUID

import httpx
import pandas as pd
import pytest
from vapar.clients import api
from vapar.constants.exports import ExportFormat
from vapar.core.exports import PredictorExportPayload

from activities import predictor_export
from common.defects import APISubStandardAllDefectsProvider
from common.standards import APISubstandardProvider
from tests.conftest import get_mock_client_with_handler

_INSPECTION_MODEL_DATA = {
    "uuid": "4d797e1a-3004-4338-897d-7c7d9fb6f052",
    "legacyId": "78761",
    "status": "Uploaded",
    "structuralGrade": 2,
    "serviceGrade": 4,
    "createdBy": None,
    "createdAt": "2024-08-09T00:27:02.397568Z",
    "Direction": "Downstream",
    "Date": "2019-08-20",
    "LengthSurveyed": 3983.0,
    "GeneralRemarks": "",
    "IsImperial": None,
    "Standard": "Australia 2020",
    "ReviewedBy": None,
    "Time": None,
    "AssetID": "asset id",
    "Material": "Vitrified Clay",
    "HeightDiameter": 8,
    "LocationStreet": "Stree 8 4725 Brookwood Drive",
    "LocationTown": "Roanoke",
    "UpstreamNode": "",
    "DownstreamNode": "01B-3932.0",
    "UseOfDrainSewer": "SS",
    "repairCompletedDate": None,
    "folder": {
        "id": 4776,
        "path": "Sub-model testing 09/08/24",
        "depth": 2,
        "numchild": 0,
        "primary_org": None,
        "secondary_org": None,
        "job_name": "Sub-model testing 09/08/24",
        "created_date": "2024-08-09T00:07:15.965074Z",
        "pipe_type_sewer": False,
        "standard_key": 6,
    },
    "asset": {
        "uuid": "4e252512-15f6-4563-aadc-e85aa6ef05ac",
        "type": "pipe",
        "organisation": 237,
        "standard": 0,
        "createdAt": "2024-08-09T00:26:50.162221Z",
        "AssetID": "",
        "UpstreamNode": None,
        "DownstreamNode": "01B-3932.0",
        "HeightDiameter": 8,
        "Material": "s concrete segments (unbo",
        "LocationStreet": "Stree 8 4725 Brookwood Drive",
        "LocationTown": "Roanoke",
    },
    "file": {
        "id": 0,
        "url": "uploadedvideofiles/06_03_2024_22_42_34_899046-01b-3933.001b-3932.0_10018_08_20_2019.mp4",
        "hidden": False,
        "filename": "06_03_2024_22_42_34_899046-01b-3933.001b-3932.0_10018_08_20_2019.mp4",
        "file_size": "108.2MB",
        "file_type": "application/octet-stream",
        "upload_org": 237,
        "target_org": 237,
        "upload_user": "VAPAR Admin",
        "uploaded_by": 766,
        "created_time": "2024-08-09T00:07:40.501596Z",
        "job_id": None,
        "job_tree": 4776,
        "upload_completed_time": "2024-08-09T00:08:45.227556Z",
        "upload_completed": True,
        "upload_org_type": "Asset_Owner",
    },
    "extraFields": {"ExpectedLength": "3983.0"},
    "toBeMatched": False,
    "ExpectedLength": "3983.0",
    "start_node": "",
    "end_node": "01B-3932.0",
    "location": {"street": "Stree 8 4725 Brookwood Drive", "town": "Roanoke", "country": "AU"},
    "name": "Stree 8 4725 Brookwood Drive, Roanoke",
    "filename": "06_03_2024_22_42_34_899046-01b-3933.001b-3932.0_10018_08_20_2019.mp4",
    "setupLocation": "Upstream",
    "chainageUnit": "m",
}

_FRAME_DATA_TEMPLATE = {
    "id": 0,
    "inspectionId": 0,
    "imageLocation": "string",
    "imageUrl": "string",
    "frameId": 1,
    "classLabel": "Defect Description 1",
    "classCertainty": ".9",
    "chainage": "string",
    "chainageNumber": "50",
    "isHidden": False,
    "isAccepted": True,
    "allClassBreakdown": "string",
    "atJoint": False,
    "atClock": **********,
    "toClock": **********,
    "contDefectStart": True,
    "contDefectEnd": "-94205.0",
    "quantity1Value": "0",
    "quantity1Units": "m",
    "quantity2Value": "0.",
    "quantity2Units": "0",
    "remarks": "string",
    "isMatched": True,
    "parentVideo": 0,
    "pipeTypeSewer": True,
    "material": "string",
    "defectId": 0,
    "defectClass": "Defect Description 1",
    "defectCode": "string",
    "defectStrScore": "2",
    "defectSerScore": "3",
    "defectScoreSeverity": "string",
    "defectScoreIsShown": True,
    "timeReference": "string",
}

_DEFECT_DATA_TEMPLATE = {
    "id": 0,
    "substandard": {
        "id": 0,
        "materialType": "Vitrified Clay",
        "pipeTypeSewer": True,
        "comment": "string",
        "standard": {"id": 0, "displayName": "Standard 1", "name": "Standard 1"},
        "region": "AU",
    },
    "defectDescription": "Defect Description 1",
    "defectModelName": "Defect Model Name 1",
    "defectModelId": 0,
    "serviceScore": "2",
    "structuralScore": "3",
    "defectType": "string",
    "quantity1DefaultVal": 0,
    "quantity1Units": "string",
    "quantity2DefaultVal": 0,
    "quantity2Units": "string",
    "defectCode": "string",
    "continuousScore": True,
    "materialApplied": "string",
    "characterisation1": "string",
    "characterisation2": "string",
    "clockPositionRequired": True,
    "clockSpreadPossible": True,
    "percentageRequired": True,
    "startSurvey": True,
    "endSurvey": True,
    "repairPriority": **********,
    "repairCategory": "Roots",
    "fastpassCode": True,
    "atJointRequired": True,
}

_SUBSTANDARD_DATA_TEMPLATE = {
    "id": 0,
    "materialType": "string",
    "pipeTypeSewer": True,
    "comment": "string",
    "standard": {"id": 0, "displayName": "Standard 1", "name": "Standard 1"},
    "region": "AU",
}


@pytest.fixture(autouse=True)
def clear_cached():
    APISubstandardProvider._GLOBAL_SUBSTANDARD_CACHE.clear()
    APISubStandardAllDefectsProvider._GLOBAL_DEFECT_CACHE.clear()


@pytest.fixture
def all_defects() -> list[api.models.AllDefects]:
    d1 = api.models.AllDefects.from_dict(_DEFECT_DATA_TEMPLATE)

    d2 = api.models.AllDefects.from_dict(_DEFECT_DATA_TEMPLATE)
    d2.id = 1
    d2.defect_description = "Defect Description 2"
    d2.defect_model_name = "Defect Model Name 2"
    d2.structural_score = 1
    d2.service_score = 1

    d3 = api.models.AllDefects.from_dict(_DEFECT_DATA_TEMPLATE)
    d3.id = 2
    d3.defect_description = "Defect Description 3"
    d3.defect_model_name = "Defect Model Name 3"
    d3.structural_score = 2
    d3.service_score = 5

    return [d1, d2, d3]


@pytest.fixture
def inspection() -> api.models.InspectionModel:
    return api.models.InspectionModel.from_dict(_INSPECTION_MODEL_DATA)


@pytest.fixture
def multiple_inspections() -> list[api.models.InspectionModel]:
    i1 = api.models.InspectionModel.from_dict(_INSPECTION_MODEL_DATA)

    # Has some missing data
    i2 = api.models.InspectionModel.from_dict(_INSPECTION_MODEL_DATA)
    i2.uuid = "1a1a1a1a-3004-4338-897d-7c7d9fb6f052"
    i2.legacy_id = "78762"
    i2.structural_grade = 1
    i2.service_grade = 4
    i2.asset.upstream_node = None
    i2.asset.downstream_node = "Unknown"
    i2.length_surveyed = 500
    i2.asset.height_diameter = None
    i2.date = datetime.datetime(2024, 1, 2)
    i2.file["id"] = 1  # different file id

    return [i1, i2]


@pytest.fixture
def frame() -> api.models.FramesList:
    return api.models.FramesList.from_dict(_FRAME_DATA_TEMPLATE)


@pytest.fixture
def sub_standard() -> api.models.StandardSubcategory:
    return api.models.StandardSubcategory.from_dict(_SUBSTANDARD_DATA_TEMPLATE)


@pytest.fixture
def multiple_frames() -> list[api.models.FramesList]:
    f1 = api.models.FramesList.from_dict(_FRAME_DATA_TEMPLATE)
    f1.id = 0
    f1.frame_id = 1
    f1.defect_class = "Defect Description 1"
    f1.class_label = "Defect Description 1"

    f2 = api.models.FramesList.from_dict(_FRAME_DATA_TEMPLATE)
    f2.id = 1
    f2.frame_id = 2
    f2.defect_class = "Defect Description 2"
    f2.class_label = "Defect Description 2"

    f3 = api.models.FramesList.from_dict(_FRAME_DATA_TEMPLATE)
    f3.id = 2
    f3.frame_id = 3
    f3.defect_class = "Defect Description 1"
    f3.class_label = "Defect Description 1"
    f3.parent_video = 1  # video id for the second inspection

    return [f1, f2, f3]


@pytest.fixture
def settings() -> predictor_export.Settings:
    return predictor_export.Settings()


@pytest.fixture
def single_inspection_input() -> predictor_export.Input:
    return predictor_export.Input(
        export_id=UUID("00000000-0000-0000-0000-000000000000"),
        target_org_id=123,
        payload=PredictorExportPayload(
            inspection_ids=[UUID("4d797e1a-3004-4338-897d-7c7d9fb6f052")],
            format=ExportFormat.CSV,
        ),
    )


@pytest.fixture
def multiple_inspections_input() -> predictor_export.Input:
    return predictor_export.Input(
        export_id=UUID("00000000-0000-0000-0000-000000000000"),
        target_org_id=123,
        payload=PredictorExportPayload(
            inspection_ids=[
                UUID("4d797e1a-3004-4338-897d-7c7d9fb6f052"),
                UUID("1a1a1a1a-3004-4338-897d-7c7d9fb6f052"),
            ],
            format=ExportFormat.CSV,
        ),
    )


def test_report_for_single_inspection(
    output_fs, inspection, frame, sub_standard, all_defects, single_inspection_input, settings
):
    def mock_handler(req: httpx.Request):
        if req.url.path == "/api/v3/inspections2":
            return httpx.Response(
                status_code=200,
                json={
                    "count": 1,
                    "results": [inspection.to_dict()],
                    "next": None,
                    "previous": None,
                },
            )

        if req.url.path == "/api/v3/inspections/frames":
            return httpx.Response(
                status_code=200,
                json={
                    "count": 1,
                    "results": [frame.to_dict()],
                    "next": None,
                    "previous": None,
                },
            )

        if req.url.path == "/api/v3/standards/defects":
            return httpx.Response(status_code=200, json=[d.to_dict() for d in all_defects])

        if req.url.path == "/api/v3/standards/subcategories":
            return httpx.Response(status_code=200, json={"count": 1, "results": [sub_standard.to_dict()]})

        return httpx.Response(404)

    client = get_mock_client_with_handler(mock_handler)
    resp_data = predictor_export.handler(
        input_data=single_inspection_input,
        settings=settings,
        api_client=client,
        max_retry_secs=0,
        output_fs=output_fs,
    )

    out_file_path = resp_data.output_file.file_path
    with output_fs.open(out_file_path, "r") as f:
        df_res = pd.read_csv(f).fillna("")

    assert len(df_res) == 1, "One inspection == one row"
    row = df_res.iloc[0]
    assert str(row["id"]) == inspection.legacy_id
    assert row["Name"] == inspection.asset.location_street
    assert row["Chainage"] == inspection.length_surveyed
    assert row["Structural Grade"] == inspection.structural_grade
    assert row["Asset No"] == inspection.asset.asset_id
    assert row["Date Captured"] == inspection.date.strftime("%Y-%m-%d")
    assert row["Start Node"] == inspection.additional_properties["start_node"]
    assert row["End Node"] == inspection.additional_properties["end_node"]

    assert row["Defect Description 1"] == 3, "Max of str and ser for defect on the one frame"
    assert row["Defect Description 2"] == 0, "No defect of this type"
    assert row["Defect Description 3"] == 0, "No defect of this type"


def test_single_inspection_multiple_frames(
    output_fs, inspection, multiple_frames, sub_standard, all_defects, single_inspection_input, settings
):
    for frame in multiple_frames:
        frame.parent_video = 0  # Attach all frames to the one inspection

    def mock_handler(req: httpx.Request):
        if req.url.path == "/api/v3/inspections2":
            return httpx.Response(
                status_code=200,
                json={
                    "count": 1,
                    "results": [inspection.to_dict()],
                    "next": None,
                    "previous": None,
                },
            )

        if req.url.path == "/api/v3/inspections/frames":
            return httpx.Response(
                status_code=200,
                json={
                    "count": len(multiple_frames),
                    "results": [f.to_dict() for f in multiple_frames],
                    "next": None,
                    "previous": None,
                },
            )

        if req.url.path == "/api/v3/standards/defects":
            return httpx.Response(status_code=200, json=[d.to_dict() for d in all_defects])

        if req.url.path == "/api/v3/standards/subcategories":
            return httpx.Response(status_code=200, json={"count": 1, "results": [sub_standard.to_dict()]})

        return httpx.Response(404)

    client = get_mock_client_with_handler(mock_handler)
    resp_data = predictor_export.handler(
        input_data=single_inspection_input,
        settings=settings,
        api_client=client,
        max_retry_secs=0,
        output_fs=output_fs,
    )

    out_file_path = resp_data.output_file.file_path
    with output_fs.open(out_file_path, "r") as f:
        df_res = pd.read_csv(f)

    assert len(df_res) == 1, "One inspection == one row"
    row = df_res.iloc[0]

    assert row["Defect Description 1"] == 6, "Sum of max(str, ser) for each frame with this defect"
    assert row["Defect Description 2"] == 3, "Sum of max(str, ser) for each frame with this defect"
    assert row["Defect Description 3"] == 0, "No frames with this defect"


def test_multiple_inspections(
    output_fs, multiple_inspections, multiple_frames, sub_standard, all_defects, multiple_inspections_input, settings
):
    def mock_handler(req: httpx.Request):
        if req.url.path == "/api/v3/inspections2":
            return httpx.Response(
                status_code=200,
                json={
                    "count": 2,
                    "results": [i.to_dict() for i in multiple_inspections],
                    "next": None,
                    "previous": None,
                },
            )

        if req.url.path == "/api/v3/inspections/frames":
            return httpx.Response(
                status_code=200,
                json={
                    "count": len(multiple_frames),
                    "results": [f.to_dict() for f in multiple_frames],
                    "next": None,
                    "previous": None,
                },
            )

        if req.url.path == "/api/v3/standards/defects":
            return httpx.Response(status_code=200, json=[d.to_dict() for d in all_defects])

        if req.url.path == "/api/v3/standards/subcategories":
            return httpx.Response(status_code=200, json={"count": 1, "results": [sub_standard.to_dict()]})

        return httpx.Response(404)

    client = get_mock_client_with_handler(mock_handler)
    resp_data = predictor_export.handler(
        input_data=multiple_inspections_input,
        settings=settings,
        api_client=client,
        max_retry_secs=0,
        output_fs=output_fs,
    )

    out_file_path = resp_data.output_file.file_path
    with output_fs.open(out_file_path, "r") as f:
        df_res = pd.read_csv(f).fillna("")

    assert len(df_res) == 2, "Two inspections == two rows"

    row_1 = df_res[df_res["id"].astype(str) == multiple_inspections[0].legacy_id].iloc[0]
    row_2 = df_res[df_res["id"].astype(str) == multiple_inspections[1].legacy_id].iloc[0]

    assert row_1["Defect Description 1"] == 3, "Sum of max(str, ser) for each frame with this defect"
    assert row_1["Defect Description 2"] == 0, "Sum of max(str, ser) for each frame with this defect"
    assert row_1["Defect Description 3"] == 0, "No frames with this defect"
    assert row_1["Name"] == multiple_inspections[0].asset.location_street
    assert row_1["Chainage"] == multiple_inspections[0].length_surveyed
    assert row_1["Structural Grade"] == multiple_inspections[0].structural_grade
    assert row_1["Service Grade"] == multiple_inspections[0].service_grade
    assert row_1["Asset No"] == multiple_inspections[0].asset.asset_id
    assert row_1["Date Captured"] == multiple_inspections[0].date.strftime("%Y-%m-%d")
    assert row_1["Start Node"] == multiple_inspections[0].additional_properties["start_node"]
    assert row_1["End Node"] == multiple_inspections[0].additional_properties["end_node"]

    assert row_2["Defect Description 1"] == 3, "Max of str and ser for defect on the one frame"
    assert row_2["Defect Description 2"] == 0, "No defect of this type"
    assert row_2["Defect Description 3"] == 0, "No defect of this type"
    assert row_2["Name"] == multiple_inspections[1].asset.location_street
    assert row_2["Chainage"] == multiple_inspections[1].length_surveyed
    assert row_2["Structural Grade"] == multiple_inspections[1].structural_grade
    assert row_2["Service Grade"] == multiple_inspections[1].service_grade
    assert row_2["Asset No"] == multiple_inspections[1].asset.asset_id
    assert row_2["Date Captured"] == multiple_inspections[1].date.strftime("%Y-%m-%d")
    assert row_2["Start Node"] == multiple_inspections[1].additional_properties["start_node"]
    assert row_2["End Node"] == multiple_inspections[1].additional_properties["end_node"]
