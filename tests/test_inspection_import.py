import itertools
import json
from copy import deepcopy
from datetime import datetime
from pathlib import Path
from uuid import UUID

import httpx
import pytest
import xmltodict
from vapar.clients import api
from vapar.constants.imports import ImportStatusEnum, ImportTypeEnum
from vapar.core.imports import (
    MSCC5DefectXMLInspectionImportPayload,
)

from activities import inspection_import
from activities.inspection_import import (
    get_standard_by_name,
    make_asset_create_or_match_request,
    parse_dict_to_models,
)
from common.models import ImportDetails
from tests.conftest import get_mock_client_with_handler

_STANDARD_MODEL_DATA = {
    "id": 2,
    "name": "MSCC5",
    "display_name": "UK V5",
}

_IMPORT_OBJ_DATA = {
    "id": "00000000-0000-0000-0000-000000000000",
    "importOperation": "00000000-0000-0000-0000-000000000000",
    "createdBy": 1,
    "createdAt": "2025-02-19T04:49:56.481006Z",
    "updatedAt": "2025-02-19T04:49:56.527982Z",
    "validatedAt": "2025-02-19T04:49:56.527850Z",
    "validationStatus": "PA",
    "fileSize": 8250,
    "originalFilename": "mscc5_small.xml",
    "blobUrl": "xml/mscc5_small.xml",
    "extension": ".xml",
    "mimeType": "application/xml",
}

_ASSET_DATA = {
    "uuid": "00000000-0000-0000-0000-000000000000",
    "type": "pipe",
    "organisation": 130,
    "standard": 2,
    "createdAt": "2024-09-25T07:31:58.245432Z",
    "AssetID": "**********",
    "UpstreamNode": "**********x",
    "DownstreamNode": "SJ51677902x",
    "HeightDiameter": 51677903,
    "Material": "VC",
    "LocationStreet": "Back Lane x",
    "LocationTown": "town xx",
    "UseOfDrainSewer": "SS",
    "YearRenewed": None,
    "inspectionsCount": 0,
    "extraFields": {},
}


_FILE_OBJ_DATA = {
    "id": 123,
    "jobId": 123,
    "filename": "JDS.mp4",
    "fileType": "video/mp4",
    "fileSize": "8.6MB",
    "url": "",
    "targetOrg": 1,
    "uploadOrg": 1,
    "uploadUser": "John Smith",
    "jobTree": 1,
    "uploadedBy": 1,
    "uploadCompletedTime": "2022-11-19T04:25:10.474441Z",
    "uploadCompleted": False,
    "createdTime": "2022-11-19T04:25:05.627999Z",
    "hidden": False,
    "updatedAt": "2024-12-09T02:30:38.472019Z",
    "standardKey": 2,
    "storageRegion": "AU",
    "sewerData": "Sewer",
    "downloadUrl": None,
    "playUrl": None,
}


@pytest.fixture
def standard_obj() -> api.models.Standard:
    return api.models.Standard.from_dict(_STANDARD_MODEL_DATA)


@pytest.fixture
def all_defects(fixtures_path: Path) -> list[api.models.AllDefects]:
    json_path = fixtures_path / "defectscores_prod.json"
    with open(json_path) as f:
        defectscores = json.load(f)

    return [
        api.models.AllDefects.from_dict(
            {
                "id": d["pk"],
                "substandard": {
                    "id": 2,
                    "standard": _STANDARD_MODEL_DATA,
                    "region": "UK",
                },
                "defectDescription": d["fields"]["defect_description"],
                "defectModelName": "Test model name",
                "defectModelId": d["fields"]["defect_key"],
                "serviceScore": d["fields"]["service_score"],
                "structuralScore": d["fields"]["structural_score"],
                "defectType": d["fields"].get("defect_type"),
                "quantity1DefaultVal": d["fields"].get("quantity_1_default_val"),
                "quantity1Units": d["fields"].get("quantity_1_units"),
                "quantity2DefaultVal": d["fields"].get("quantity_2_default_val"),
                "quantity2Units": d["fields"].get("quantity_2_units"),
                "defectCode": d["fields"].get("defect_code"),
                "atJointRequired": d["fields"].get("at_joint_required"),
                "materialApplied": d["fields"].get("material_applied"),
                "characterisation1": d["fields"].get("characterisation_1"),
                "characterisation2": d["fields"].get("characterisation_2"),
                "clockPositionRequired": d["fields"].get("clock_position_required"),
                "clockSpreadPossible": d["fields"].get("clock_spread_possible"),
                "percentageRequired": d["fields"].get("percentage_required"),
                "startSurvey": d["fields"].get("start_survey"),
                "endSurvey": d["fields"].get("end_survey"),
                "repairPriority": d["fields"].get("repair_priority"),
                "repairCategory": d["fields"].get("repair_category"),
                "fastpassCode": d["fields"].get("fastpass_code"),
                "continuousScore": d["fields"].get("continuous_score"),
                "isShown": d["fields"].get("is_shown"),
            }
        )
        for d in defectscores
        if d["fields"]["standard_key"] == 2 and d["fields"]["sub_standard"] == 2
    ]


@pytest.fixture
def xml_file_path(xml_path: Path) -> Path:
    return xml_path / "mscc5_small.xml"


def test_make_asset_create_or_match_request(output_fs, standard_obj, xml_file_path):
    def mock_handler(req: httpx.Request):
        if req.url.path == "/api/v3/standards":
            return httpx.Response(
                status_code=200,
                json={
                    "results": [standard_obj.to_dict()],
                    "count": 1,
                    "next": None,
                    "previous": None,
                },
            )

        return httpx.Response(404)

    client = get_mock_client_with_handler(mock_handler)

    with open(xml_file_path) as f:
        data = xmltodict.parse(f.read(), force_list={"Survey", "NodeSurvey", "Observation", "PhotographRef"})
    survey = parse_dict_to_models(data)[0]
    assert survey.file.video_name == "18021803150MMVCFW_18021803150MMVCFW.mp4"

    standard = get_standard_by_name(client, survey.asset.standard)
    request = make_asset_create_or_match_request(standard_id=standard.id, org_id=0, asset=survey.asset)

    assert request.standard_id == standard_obj.id
    assert request.org_id == 0
    assert request.downstream_node == "18021803150MMVCFW"
    assert request.height_diameter == 100
    assert request.material == "Z"
    assert request.location_street == "The Green"
    assert request.location_town == "United Kingdom"
    assert request.use_of_drain_sewer == "SS"


@pytest.fixture
def input_dto() -> inspection_import.Input:
    return inspection_import.Input(
        import_id=UUID("00000000-0000-0000-0000-000000000000"),
        target_org_id=0,
        instance_id="",
        import_details=ImportDetails(
            id=UUID("00000000-0000-0000-0000-000000000000"),
            type=ImportTypeEnum.INSPECTIONS,
            payload=MSCC5DefectXMLInspectionImportPayload(
                destination_folder_id=5,
            ),
            status=ImportStatusEnum.PROCESSING,
            created_at=datetime(2024, 1, 1),
            updated_at=datetime(2024, 1, 1),
        ),
    )


def test_activity_mscc5_xml(
    input_dto,
    resources_fs,
    standard_obj,
    all_defects,
):
    created_asset_refs = []
    created_insp_survey_lengths = []
    created_files_folder = []

    id_gen = itertools.count()

    def mock_handler(req: httpx.Request):
        if req.url.path == "/api/v3/imports/00000000-0000-0000-0000-000000000000/files":
            return httpx.Response(
                status_code=200,
                json={
                    "count": 1,
                    "previous": None,
                    "next": None,
                    "results": [deepcopy(_IMPORT_OBJ_DATA)],
                },
            )

        if req.url.path == "/api/v3/standards":
            return httpx.Response(
                status_code=200,
                json={
                    "results": [standard_obj.to_dict()],
                    "count": 1,
                    "next": None,
                    "previous": None,
                },
            )

        if req.url.path == "/api/v3/standards/defects":
            return httpx.Response(status_code=200, json=[d.to_dict() for d in all_defects])

        if (req.url.path, req.method) == ("/api/v3/assets/match-or-create", "POST"):
            created_asset_refs.append(json.loads(req.content)["AssetID"])
            return httpx.Response(
                201,
                json={
                    "isMatched": False,
                    "asset": deepcopy(_ASSET_DATA),
                },
            )

        if (req.url.path, req.method) == ("/api/v3/files", "POST"):
            created_files_folder.append(json.loads(req.content)["jobTree"])
            return httpx.Response(201, json=deepcopy(_FILE_OBJ_DATA))

        if (req.url.path, req.method) == ("/api/v3/files/123", "PATCH"):
            data = json.loads(req.content)
            assert "totalFrames" in data
            return httpx.Response(204)

        if (req.url.path, req.method) == ("/api/v3/inspections2", "POST"):
            created_insp_survey_lengths.append(json.loads(req.content)["LengthSurveyed"])
            return httpx.Response(
                201,
                json={"uuid": "00000000-0000-0000-0000-000000000000"},
            )

        if (req.url.path, req.method) == ("/api/v3/imports/00000000-0000-0000-0000-000000000000/inspections", "POST"):
            resp = deepcopy(json.loads(req.content))
            return httpx.Response(201, json=resp)

        if req.url.path == "/api/v3/inspections/videos/123/frames" and req.method == "POST":
            data = json.loads(req.content)
            assert isinstance(data, list)
            resp = [
                {
                    "id": next(id_gen),
                    **in_frame,
                }
                for i, in_frame in enumerate(data)
            ]
            return httpx.Response(201, json=resp)

        return httpx.Response(404)

    client = get_mock_client_with_handler(mock_handler)
    inspection_import.handler(input_dto, client, resources_fs, max_retry_secs=0)

    assert len(created_asset_refs) == 2, "Two assets should have been created"
    assert set(created_asset_refs) == {"ASSETID1", "ASSETID2"}, "These asset ids were provided (PipelineLengthRef)"

    assert len(created_insp_survey_lengths) == 2, "Two inspections should have been created"
    assert set(created_insp_survey_lengths) == {29.4, 69.7}, "These lengths were provided (LengthSurveyed)"

    target_folder = input_dto.import_details.payload.root.root.destination_folder_id
    assert len(created_files_folder) == 2, "Should have created one file per inspection"
    assert all(folder == target_folder for folder in created_files_folder), "All files should be in the provided folder"


def test_defect_matching(
    input_dto,
    resources_fs,
    standard_obj,
    all_defects,
):
    created_asset_refs = []
    created_insp_survey_lengths = []
    created_files_folder = []

    id_gen = itertools.count()

    def mock_handler(req: httpx.Request):
        if req.url.path == "/api/v3/imports/00000000-0000-0000-0000-000000000000/files":
            return httpx.Response(
                status_code=200,
                json={
                    "count": 1,
                    "previous": None,
                    "next": None,
                    "results": [deepcopy(_IMPORT_OBJ_DATA)],
                },
            )

        if req.url.path == "/api/v3/standards":
            return httpx.Response(
                status_code=200,
                json={
                    "results": [standard_obj.to_dict()],
                    "count": 1,
                    "next": None,
                    "previous": None,
                },
            )

        if req.url.path == "/api/v3/standards/defects":
            return httpx.Response(status_code=200, json=[d.to_dict() for d in all_defects])

        if (req.url.path, req.method) == ("/api/v3/assets/match-or-create", "POST"):
            created_asset_refs.append(json.loads(req.content)["AssetID"])
            return httpx.Response(
                201,
                json={
                    "isMatched": False,
                    "asset": deepcopy(_ASSET_DATA),
                },
            )

        if (req.url.path, req.method) == ("/api/v3/files", "POST"):
            created_files_folder.append(json.loads(req.content)["jobTree"])
            return httpx.Response(201, json=deepcopy(_FILE_OBJ_DATA))

        if (req.url.path, req.method) == ("/api/v3/files/123", "PATCH"):
            data = json.loads(req.content)
            assert "totalFrames" in data
            return httpx.Response(204)

        if (req.url.path, req.method) == ("/api/v3/inspections2", "POST"):
            created_insp_survey_lengths.append(json.loads(req.content)["LengthSurveyed"])
            return httpx.Response(
                201,
                json={"uuid": "00000000-0000-0000-0000-000000000000"},
            )

        if (req.url.path, req.method) == ("/api/v3/imports/00000000-0000-0000-0000-000000000000/inspections", "POST"):
            resp = deepcopy(json.loads(req.content))
            return httpx.Response(201, json=resp)

        if req.url.path == "/api/v3/inspections/videos/123/frames" and req.method == "POST":
            data = json.loads(req.content)
            assert isinstance(data, list)
            resp = [
                {
                    "id": next(id_gen),
                    **in_frame,
                }
                for i, in_frame in enumerate(data)
            ]
            return httpx.Response(201, json=resp)

        return httpx.Response(404)

    # Test general photograph fallback
    matched = inspection_import._match_defect(
        inspection_import.ParsedDefect(
            continuous_defect="",
            clock_ref_at_from=0,
            clock_ref_to=0,
        ),
        all_defects,
        quantity_1_value=None,
        code="",
        joint="",
    )
    assert matched == (120, 2020, None, None)

    # Test matching on percentage
    matched = inspection_import._match_defect(
        inspection_import.ParsedDefect(
            continuous_defect="",
            percentage="16",
            clock_ref_at_from=0,
            clock_ref_to=0,
        ),
        all_defects,
        quantity_1_value=16,
        code="DEE",
        joint="",
    )
    assert matched == (224, 1886, "%", "")

    # Test matching fallback on non-percentage defect
    matched = inspection_import._match_defect(
        inspection_import.ParsedDefect(
            continuous_defect="",
            clock_ref_at_from=4,
            clock_ref_to=7,
        ),
        all_defects,
        quantity_1_value=None,
        code="H",
        joint="",
    )
    assert matched == (84, 2029, "", "")

    # Test matching fallback on defect with potentially duplicated DB entries
    matched = inspection_import._match_defect(
        inspection_import.ParsedDefect(
            continuous_defect="",
            clock_ref_at_from=6,
            clock_ref_to=0,
        ),
        all_defects,
        quantity_1_value=None,
        code="B",
        joint="J",
    )
    assert matched == (1, 1901, "", "")
