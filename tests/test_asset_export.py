import datetime
from uuid import UUID

import httpx
import pandas as pd
import pytest
from vapar.clients import api
from vapar.constants.exports import ExportFormat
from vapar.core.exports import AssetExportPayload

from activities import asset_export
from tests.conftest import get_mock_client_with_handler

_INSPECTION_MODEL_DATA = {
    "uuid": "4d797e1a-3004-4338-897d-7c7d9fb6f052",
    "legacyId": "78761",
    "status": "Uploaded",
    "structuralGrade": 1,
    "serviceGrade": 4,
    "createdBy": None,
    "createdAt": "2024-08-09T00:27:02.397568Z",
    "Direction": "Downstream",
    "Date": "2019-08-20",
    "LengthSurveyed": 3983.0,
    "GeneralRemarks": "",
    "IsImperial": None,
    "Standard": "Australia 2020",
    "ReviewedBy": None,
    "Time": None,
    "AssetID": "asset id",
    "Material": "Vitrified Clay",
    "HeightDiameter": 8,
    "LocationStreet": "Stree 8 4725 Brookwood Drive",
    "LocationTown": "Roanoke",
    "UpstreamNode": "",
    "DownstreamNode": "01B-3932.0",
    "UseOfDrainSewer": "SS",
    "repairCompletedDate": None,
    "folder": {
        "id": 4776,
        "path": "Sub-model testing 09/08/24",
        "depth": 2,
        "numchild": 0,
        "primary_org": None,
        "secondary_org": None,
        "job_name": "Sub-model testing 09/08/24",
        "created_date": "2024-08-09T00:07:15.965074Z",
        "pipe_type_sewer": False,
        "standard_key": 6,
    },
    "asset": {
        "uuid": "4e252512-15f6-4563-aadc-e85aa6ef05ac",
        "type": "pipe",
        "organisation": 142,
        "standard": 0,
        "createdAt": "2024-08-09T00:26:50.162221Z",
        "AssetID": "asset id",
        "UpstreamNode": "01B-3932.1",
        "DownstreamNode": "01B-3932.0",
        "HeightDiameter": 8,
        "Material": "Vitrified Clay",
        "LocationStreet": "Stree 8 4725 Brookwood Drive",
        "LocationTown": "Roanoke",
    },
    "file": {
        "id": 87011,
        "url": "uploadedvideofiles/06_03_2024_22_42_34_899046-01b-3933.001b-3932.0_10018_08_20_2019.mp4",
        "hidden": False,
        "filename": "06_03_2024_22_42_34_899046-01b-3933.001b-3932.0_10018_08_20_2019.mp4",
        "file_size": "108.2MB",
        "file_type": "application/octet-stream",
        "upload_org": 237,
        "target_org": 237,
        "upload_user": "VAPAR Admin",
        "uploaded_by": 766,
        "created_time": "2024-08-09T00:07:40.501596Z",
        "job_id": None,
        "job_tree": 4776,
        "upload_completed_type": "2024-08-09T00:08:45.227556Z",
        "upload_completed": True,
        "upload_org_type": "Asset_Owner",
    },
    "extraFields": {"ExpectedLength": "3983.0"},
    "toBeMatched": False,
    "ExpectedLength": "3983.0",
    "start_node": "",
    "end_node": "01B-3932.0",
    "location": {"LocationStreet": "Stree 8 4725 Brookwood Drive", "LocationTown": "Roanoke", "country": "AU"},
    "name": "Stree 8 4725 Brookwood Drive, Roanoke",
    "filename": "06_03_2024_22_42_34_899046-01b-3933.001b-3932.0_10018_08_20_2019.mp4",
    "setupLocation": "Upstream",
    "chainage_unit": "m",
    "WorkOrder": "ABC",
}


@pytest.fixture
def inspection() -> api.models.InspectionModel:
    return api.models.InspectionModel.from_dict(_INSPECTION_MODEL_DATA)


@pytest.fixture
def multiple_inspections() -> list[api.models.InspectionModel]:
    i1 = api.models.InspectionModel.from_dict(_INSPECTION_MODEL_DATA)

    # Has some missing data
    i2 = api.models.InspectionModel.from_dict(_INSPECTION_MODEL_DATA)
    i2.uuid = "1a1a1a1a-3004-4338-897d-7c7d9fb6f052"
    i2.legacy_id = "78762"
    i2.structural_grade = 2
    i2.service_grade = 4
    i2.asset.upstream_node = None
    i2.asset.downstream_node = "Unknown"
    i2.length_surveyed = 500
    i2.asset.height_diameter = None
    i2.date = datetime.datetime(2024, 1, 2)

    return [i1, i2]


@pytest.fixture
def settings() -> asset_export.Settings:
    return asset_export.Settings()


@pytest.fixture
def single_inspection_input() -> asset_export.Input:
    return asset_export.Input(
        export_id=UUID("00000000-0000-0000-0000-000000000000"),
        target_org_id=123,
        payload=AssetExportPayload(
            inspection_ids=[UUID("4d797e1a-3004-4338-897d-7c7d9fb6f052")],
            format=ExportFormat.CSV,
        ),
    )


@pytest.fixture
def multiple_inspections_input() -> asset_export.Input:
    return asset_export.Input(
        export_id=UUID("00000000-0000-0000-0000-000000000000"),
        target_org_id=123,
        payload=AssetExportPayload(
            inspection_ids=[
                UUID("4d797e1a-3004-4338-897d-7c7d9fb6f052"),
                UUID("1a1a1a1a-3004-4338-897d-7c7d9fb6f052"),
            ],
            format=ExportFormat.CSV,
        ),
    )


def test_report_for_single_inspection(output_fs, inspection, single_inspection_input, settings):
    def mock_handler(req: httpx.Request):
        if req.url.path == "/api/v3/inspections2":
            return httpx.Response(
                status_code=200,
                json={
                    "results": [inspection.to_dict()],
                    "count": 1,
                    "next": None,
                    "previous": None,
                },
            )

        return httpx.Response(404)

    client = get_mock_client_with_handler(mock_handler)
    resp_data = asset_export.handler(
        input_data=single_inspection_input,
        settings=settings,
        api_client=client,
        max_retry_secs=0,
        output_fs=output_fs,
    )

    out_file_path = resp_data.output_file.file_path
    with output_fs.open(out_file_path, "r") as f:
        df_res = pd.read_csv(f)

    assert len(df_res) == 1, "One inspection == one row"
    row = df_res.iloc[0]

    assert str(row["VAPAR ID"]) == inspection.legacy_id
    assert row["Asset No"] == inspection.asset.asset_id
    assert row["Length"] == inspection.length_surveyed
    assert row["Structural Grade"] == inspection.structural_grade
    assert row["Service Grade"] == inspection.service_grade
    assert row["Diameter"] == inspection.asset.height_diameter
    assert row["Material"] == inspection.asset.material
    assert row["Downstream Node"] == inspection.asset.downstream_node
    assert row["Dir of Travel"] == inspection.direction
    assert row["Job Name"] == inspection.folder.additional_properties["job_name"]
    assert row["Video File Size"] == inspection.file["file_size"]
    assert row["Folder Path"] == inspection.folder.additional_properties["path"]
    assert row["Workflow Status"] == inspection.status
    assert row["Work Order"] == inspection.work_order


def test_multiple_inspections(output_fs, multiple_inspections, multiple_inspections_input, settings):
    def mock_handler(req: httpx.Request):
        # if req.url.path == "/api/v3/inspections2/4d797e1a-3004-4338-897d-7c7d9fb6f052":
        #     return httpx.Response(status_code=200, json=multiple_inspections[0].to_dict())
        #
        # if req.url.path == "/api/v3/inspections2/1a1a1a1a-3004-4338-897d-7c7d9fb6f052":
        #     return httpx.Response(status_code=200, json=multiple_inspections[1].to_dict())

        if req.url.path == "/api/v3/inspections2":
            return httpx.Response(
                status_code=200,
                json={
                    "results": [insp.to_dict() for insp in multiple_inspections],
                    "count": 2,
                    "next": None,
                    "previous": None,
                },
            )

        return httpx.Response(404)

    client = get_mock_client_with_handler(mock_handler)
    resp_data = asset_export.handler(
        input_data=multiple_inspections_input,
        settings=settings,
        api_client=client,
        max_retry_secs=0,
        output_fs=output_fs,
    )

    out_file_path = resp_data.output_file.file_path
    with output_fs.open(out_file_path, "r") as f:
        df_res = pd.read_csv(f).fillna("")

    assert len(df_res) == 2, "Two inspections == two rows"

    row_1 = df_res[df_res["VAPAR ID"].astype(str) == multiple_inspections[0].legacy_id].iloc[0]
    row_2 = df_res[df_res["VAPAR ID"].astype(str) == multiple_inspections[1].legacy_id].iloc[0]

    assert str(row_1["VAPAR ID"]) == multiple_inspections[0].legacy_id
    assert row_1["Asset No"] == multiple_inspections[0].asset.asset_id
    assert row_1["Length"] == multiple_inspections[0].length_surveyed
    assert row_1["Structural Grade"] == multiple_inspections[0].structural_grade
    assert row_1["Service Grade"] == multiple_inspections[0].service_grade
    assert row_1["Diameter"] == multiple_inspections[0].asset.height_diameter
    assert row_1["Material"] == multiple_inspections[0].asset.material
    assert row_1["Downstream Node"] == multiple_inspections[0].asset.downstream_node
    assert row_1["Dir of Travel"] == multiple_inspections[0].direction
    assert row_1["Job Name"] == multiple_inspections[0].folder.additional_properties["job_name"]
    assert row_1["Video File Size"] == multiple_inspections[0].file["file_size"]
    assert row_1["Folder Path"] == multiple_inspections[0].folder.additional_properties["path"]
    assert row_1["Workflow Status"] == multiple_inspections[0].status
    assert row_1["Work Order"] == multiple_inspections[0].work_order

    assert str(row_2["VAPAR ID"]) == multiple_inspections[1].legacy_id
    assert row_2["Asset No"] == multiple_inspections[1].asset.asset_id
    assert row_2["Length"] == multiple_inspections[1].length_surveyed
    assert row_2["Structural Grade"] == multiple_inspections[1].structural_grade
    assert row_2["Service Grade"] == multiple_inspections[1].service_grade
    assert row_2["Material"] == multiple_inspections[1].asset.material
    assert row_2["Downstream Node"] == multiple_inspections[1].asset.downstream_node
    assert row_2["Dir of Travel"] == multiple_inspections[1].direction
    assert row_2["Job Name"] == multiple_inspections[1].folder.additional_properties["job_name"]
    assert row_2["Video File Size"] == multiple_inspections[1].file["file_size"]
    assert row_2["Folder Path"] == multiple_inspections[1].folder.additional_properties["path"]
    assert row_2["Workflow Status"] == multiple_inspections[1].status
    assert row_2["Work Order"] == multiple_inspections[1].work_order
