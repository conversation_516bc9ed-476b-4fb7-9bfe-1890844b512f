import json
import math
import shutil
from datetime import datetime
from pathlib import Path
from uuid import UUID

import httpx
import pytest
from fsspec.implementations.dirfs import DirFileSystem
from fsspec.implementations.local import LocalFileSystem
from vapar.core.imports import InspectionVideoMediaImportPayload

from activities import inspection_video_media_import
from common.models import ImportDetails
from tests.conftest import get_mock_client_with_handler

_FILE_TEMPLATE = {
    "id": 123,
    "jobId": None,
    "filename": "test_video_file.mp4",
    "fileType": "application/octet-stream",
    "fileSize": "30.5MB",
    "url": "04_03_2021_12_24_52_764753-testvid.avi",
    "targetOrg": 1,
    "uploadOrg": 1,
    "uploadUser": "",
    "jobTree": 1,
    "uploadedBy": 1,
    "uploadCompletedTime": "2024-09-25T07:20:57.801616Z",
    "uploadCompleted": True,
    "createdTime": "2024-09-25T07:20:50.081333Z",
    "hidden": False,
    "updatedAt": "2024-12-09T02:30:38.472019Z",
    "standardKey": 2,
    "storageRegion": "AU",
    "sewerData": "Sewer",
    "downloadUrl": "",
    "playUrl": "",
}

_FRAME_TEMPLATE = {
    "id": 12972456,
    "inspectionId": 77743,
    "imageLocation": "",
    "imageUrl": "",
    "frameId": 1,
    "classLabel": "Node - Start node",
    "classCertainty": "0.94",
    "chainage": "0.0",
    "chainageNumber": "0.00",
    "isHidden": False,
    "isAccepted": False,
    "allClassBreakdown": "",
    "atJoint": False,
    "atClock": 4,
    "toClock": None,
    "contDefectStart": False,
    "contDefectEnd": None,
    "quantity1Value": None,
    "quantity1Units": "",
    "quantity2Value": None,
    "quantity2Units": "",
    "remarks": "",
    "isMatched": None,
    "parentVideo": 86053,
    "pipeTypeSewer": True,
    "material": "CO",
    "defectId": 1838,
    "defectClass": "MH - Start node type - Manhole",
    "defectCode": "MH",
    "defectStrScore": "0",
    "defectSerScore": "0",
    "defectScoreSeverity": "none",
    "defectScoreIsShown": True,
    "timeReference": "0:00:00.0",
}


@pytest.fixture
def vid_path(videos_path: Path) -> Path:
    return videos_path / "04_03_2021_12_24_52_764753-testvid.avi"


@pytest.fixture
def local_work_dir(outputs_path: Path):
    path = outputs_path / "local_work_dir"
    path.mkdir(parents=True, exist_ok=True)
    try:
        yield path
    finally:
        shutil.rmtree(str(path))


@pytest.fixture
def settings(local_work_dir) -> inspection_video_media_import.Settings:
    return inspection_video_media_import.Settings(
        videos_container_path="/", frames_container_path="videoframefiles/", local_work_dir_root=str(local_work_dir)
    )


@pytest.fixture
def input_dto() -> inspection_video_media_import.Input:
    return inspection_video_media_import.Input(
        import_details=ImportDetails(
            id=UUID("00000000-0000-0000-0000-000000000000"),
            payload=InspectionVideoMediaImportPayload(file_id=123),
            status="PR",
            type="IV",
            created_at=datetime.now(),
            updated_at=datetime.now(),
        ),
        target_org_id=1,
    )


@pytest.fixture
def vid_source_fs(videos_path: Path):
    return DirFileSystem(str(videos_path), LocalFileSystem(auto_mkdir=True))


@pytest.fixture
def dest_fs(outputs_path: Path):
    return DirFileSystem(str(outputs_path), LocalFileSystem(auto_mkdir=True))


def test_extract_video_codec(vid_path):
    codec = inspection_video_media_import.extract_video_codec(video_path=vid_path, ffprobe_path="ffprobe")
    assert codec == "mpeg4"


def test_extract_video_duration(vid_path):
    duration = inspection_video_media_import.extract_video_duration(video_path=vid_path, ffprobe_path="ffprobe")
    assert math.ceil(duration) == 43  # ~ 42.8 secs


@pytest.mark.parametrize(
    "times_and_filenames",
    [
        [],
        [("00:00:01", "frame-single.jpg")],
        [("00:00:01", "frame1.jpg"), ("00:00:02", "frame2.jpg"), ("00:00:03", "frame3.jpg")],
        [
            ("00:00:39", "frame4.jpg"),
            ("00:00:25", "frame5.jpg"),
            ("00:00:10", "frame6.jpg"),
            ("00:00:16", "frame7.jpg"),
        ],  # Not in chronological order
    ],
)
def test_extract_frames(output_fs, vid_path, times_and_filenames):
    inspection_video_media_import.extract_frames_at_timestamps(
        video_path=vid_path,
        times_and_filenames=times_and_filenames,
        output_dir=output_fs.path,
        ffmpeg_path="ffmpeg",
    )

    for _, filename in times_and_filenames:
        assert output_fs.exists(filename)


def test_handler_minimal(vid_path, settings, input_dto, vid_source_fs, dest_fs):
    frame_urls_generated = []

    def handler(req: httpx.Request) -> httpx.Response:
        if req.url.path == "/api/v3/files/123" and req.method == "GET":
            return httpx.Response(200, json=_FILE_TEMPLATE)

        if req.url.path == "/api/v3/files/123/frames":
            frames = [
                {
                    **_FRAME_TEMPLATE,
                    "id": i,
                    "frameId": i + 1,
                    "timeReference": f"00:00:0{i}.0",
                }
                for i in range(5)
            ]
            return httpx.Response(200, json=frames)

        if req.url.path == "/api/v3/inspections/videos/123/frames" and req.method == "PATCH":
            for obj in json.loads(req.content):
                frame_urls_generated.append(obj["imageLocation"])
            return httpx.Response(200, json=[])  # Just return empty since we don't use this

        if req.url.path == "/api/v3/files/123" and req.method == "PATCH":
            return httpx.Response(200)

        return httpx.Response(404)

    client = get_mock_client_with_handler(handler)

    inspection_video_media_import.handler(
        input_dto=input_dto,
        settings=settings,
        api_client=client,
        video_source_fs=vid_source_fs,
        destination_fs=dest_fs,
        max_retry_secs=0,
    )

    for url in frame_urls_generated:
        # Frames should be copied to the destination filesystem - here this is a local folder but in prod it would be
        # a blob storage container
        assert dest_fs.exists(url)
