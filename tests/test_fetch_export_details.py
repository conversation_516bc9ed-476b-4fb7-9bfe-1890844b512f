import datetime
from uuid import UUID

import httpx
import pytest
from vapar.clients import api
from vapar.constants.exports import ExportFormat, ExportStatus, ExportType

from activities import fetch_export_details
from tests.conftest import get_mock_client_with_handler


@pytest.fixture
def mock_api_client() -> api.AuthenticatedClient:
    def _handler(request: httpx.Request):
        if request.url.path == "/api/v3/exports/bff08f36-19eb-4636-997a-a57e79e59db7":
            return httpx.Response(
                status_code=200,
                json={
                    "id": "bff08f36-19eb-4636-997a-a57e79e59db7",
                    "targetOrg": 123,
                    "type": "BI",
                    "format": "PDF",
                    "status": "PE",
                    "statusReason": None,
                    "createdAt": "2024-10-15T22:50:17.410394Z",
                    "updatedAt": "2024-10-15T22:51:11.138199Z",
                    "completedAt": None,
                    "createdBy": 78,
                    "isHidden": None,
                    "isInitiatedByUser": None,
                    "fileDisplayNames": [],
                },
            )
        return httpx.Response(status_code=404)

    return get_mock_client_with_handler(_handler)


@pytest.mark.asyncio
async def test_successful_fetch(mock_api_client):
    input_data = fetch_export_details.Input(export_id=UUID("bff08f36-19eb-4636-997a-a57e79e59db7"), target_org_id=123)
    resp = await fetch_export_details.handler(input_data, mock_api_client, max_retry_secs=0)

    assert resp.details is not None
    assert resp.details.status == ExportStatus.PENDING
    assert resp.details.status_reason is None
    assert resp.details.type == ExportType.BULK_INSPECTION_PDF
    assert resp.details.format == ExportFormat.PDF
    assert resp.details.created_at == datetime.datetime(2024, 10, 15, 22, 50, 17, 410394, tzinfo=datetime.timezone.utc)


@pytest.mark.asyncio
async def test_nonexistent_export_id(mock_api_client):
    nonexistent_uuid = UUID("00000000-0000-0000-0000-000000000000")
    input_data = fetch_export_details.Input(export_id=nonexistent_uuid, target_org_id=123)
    resp = await fetch_export_details.handler(input_data, mock_api_client, max_retry_secs=0)
    assert resp.details is None
