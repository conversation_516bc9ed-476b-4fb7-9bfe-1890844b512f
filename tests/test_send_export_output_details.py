import json
import uuid
from uuid import UUID

import httpx

from activities import send_export_output_details
from common.models import ExportOutputFile
from tests.conftest import get_mock_client_with_handler


def test_multiple_files():
    calls = []

    def handler(req):
        if req.url.path == "/api/v3/exports/00000000-0000-0000-0000-000000000000/outputs" and req.method == "POST":
            calls.append(req)
            content = json.loads(req.content)
            return httpx.Response(
                201,
                json={
                    "id": str(uuid.uuid4()),
                    "filename": content["filename"],
                    "blobUrl": content["blobUrl"],
                    "fileSize": content["fileSize"],
                    "mimeType": content["mimeType"],
                    "extension": content["extension"],
                    "createdBy": 0,
                    "createdAt": "2022-01-01T00:00:00Z",
                    "updatedAt": "2022-01-01T00:00:00Z",
                    "export": "00000000-0000-0000-0000-000000000000",
                    "sasUrl": "https://not-a-real-domain.com",
                },
            )
        return httpx.Response(404)

    client = get_mock_client_with_handler(handler)

    input_data = send_export_output_details.Input(
        export_id=UUID("00000000-0000-0000-0000-000000000000"),
        target_org_id=123,
        files=[
            ExportOutputFile(
                file_path="export-outputs/file1.csv",
                size=123,
                mime_type="text/csv",
                extension=".csv",
            ),
            ExportOutputFile(
                file_path="export-outputs/file2.pdf",
                size=456,
                mime_type="application/pdf",
                extension=".pdf",
                file_display_name="File 2.pdf",
            ),
        ],
    )
    send_export_output_details.handler(input_data, client, max_retry_secs=0)

    assert len(calls) == 2

    sent1 = json.loads(calls[0].content)
    assert sent1 == {
        "filename": "file1.csv",
        "export": "00000000-0000-0000-0000-000000000000",
        "blobUrl": "export-outputs/file1.csv",
        "fileSize": 123,
        "mimeType": "text/csv",
        "extension": ".csv",
        "fileDisplayName": None,
    }

    sent2 = json.loads(calls[1].content)
    assert sent2 == {
        "filename": "file2.pdf",
        "export": "00000000-0000-0000-0000-000000000000",
        "blobUrl": "export-outputs/file2.pdf",
        "fileSize": 456,
        "mimeType": "application/pdf",
        "extension": ".pdf",
        "fileDisplayName": "File 2.pdf",
    }
