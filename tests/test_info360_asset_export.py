from copy import deepcopy
from uuid import UUID

import httpx
import pandas as pd
import pytest
from vapar.clients import api
from vapar.constants.exports import ExportFormat, InfoAssetExportType
from vapar.core.exports import InfoAssetExportPayload

from activities import info360_asset_export
from tests.conftest import get_mock_client_with_handler

_INSPECTION_MODEL_DATA = {
    "uuid": "4d797e1a-3004-4338-897d-7c7d9fb6f052",
    "legacyId": "78761",
    "status": "Uploaded",
    "structuralGrade": 2,
    "serviceGrade": 4,
    "createdBy": None,
    "createdAt": "2024-08-09T00:27:02.397568Z",
    "Direction": "Downstream",
    "Date": "2019-08-20",
    "LengthSurveyed": 3983.0,
    "GeneralRemarks": "",
    "IsImperial": None,
    "Standard": "UK V5",
    "ReviewedBy": None,
    "Time": None,
    "AssetID": "asset id",
    "Material": "Vitrified Clay",
    "HeightDiameter": 8,
    "LocationStreet": "123 Main Street",
    "LocationTown": "Roanoke",
    "UseOfDrainSewer": "SS",
    "repairCompletedDate": None,
    "folder": {
        "id": 4776,
        "path": "Sub-model testing 09/08/24",
        "depth": 2,
        "numchild": 0,
        "primary_org": None,
        "secondary_org": None,
        "job_name": "Sub-model testing 09/08/24",
        "created_date": "2024-08-09T00:07:15.965074Z",
        "pipe_type_sewer": False,
        "standard_key": 6,
    },
    "asset": {
        "uuid": "4e252512-15f6-4563-aadc-e85aa6ef05ac",
        "type": "pipe",
        "organisation": 237,
        "standard": 0,
        "createdAt": "2024-08-09T00:26:50.162221Z",
        "AssetID": "",
        "UpstreamNode": None,
        "DownstreamNode": "01B-3932.0",
        "HeightDiameter": 8,
        "Material": "Vitrified Clay",
        "LocationStreet": "123 Main Street",
        "LocationTown": "Roanoke",
    },
    "file": {
        "id": 0,
        "url": "uploadedvideofiles/06_03_2024_22_42_34_899046-01b-3933.001b-3932.0_10018_08_20_2019.mp4",
        "hidden": False,
        "filename": "06_03_2024_22_42_34_899046-01b-3933.001b-3932.0_10018_08_20_2019.mp4",
        "file_size": "108.2MB",
        "file_type": "application/octet-stream",
        "upload_org": 237,
        "target_org": 237,
        "upload_user": "VAPAR Admin",
        "uploaded_by": 766,
        "created_time": "2024-08-09T00:07:40.501596Z",
        "job_id": None,
        "job_tree": 4776,
        "upload_completed_time": "2024-08-09T00:08:45.227556Z",
        "upload_completed": True,
        "upload_org_type": "Asset_Owner",
    },
    "extraFields": {"ExpectedLength": "3983.0"},
    "toBeMatched": False,
    "ExpectedLength": "3983.0",
    "start_node": "",
    "end_node": "01B-3932.0",
    "location": {"street": "123 Main Street", "town": "Roanoke", "country": "AU"},
    "name": "123 Main Street, Roanoke",
    "filename": "06_03_2024_22_42_34_899046-01b-3933.001b-3932.0_10018_08_20_2019.mp4",
    "setupLocation": "Upstream",
    "chainageUnit": "m",
}

_FRAME_DATA_TEMPLATE = {
    "id": 0,
    "inspectionId": 0,
    "imageLocation": "string",
    "imageUrl": "string",
    "frameId": 1,
    "classLabel": "Defect Description 1",
    "classCertainty": ".9",
    "chainage": "string",
    "chainageNumber": "50",
    "isHidden": False,
    "isAccepted": True,
    "allClassBreakdown": "string",
    "atJoint": False,
    "atClock": 2147483647,
    "toClock": 2147483647,
    "contDefectStart": True,
    "contDefectEnd": "-94205.0",
    "quantity1Value": "0",
    "quantity1Units": "m",
    "quantity2Value": "0.",
    "quantity2Units": "0",
    "remarks": "string",
    "isMatched": True,
    "parentVideo": 0,
    "pipeTypeSewer": True,
    "material": "string",
    "defectId": 0,
    "defectClass": "Defect Description 1",
    "defectCode": "string",
    "defectStrScore": "2",
    "defectSerScore": "3",
    "defectScoreSeverity": "string",
    "defectScoreIsShown": True,
    "timeReference": "string",
}

_DEFECT_DATA_TEMPLATE = {
    "id": 0,
    "substandard": {
        "id": 0,
        "materialType": "Vitrified Clay",
        "pipeTypeSewer": True,
        "comment": "string",
        "standard": {"id": 0, "displayName": "UK V5", "name": "MSCC5"},
        "region": "UK",
    },
    "defectDescription": "Defect Description 1",
    "defectModelName": "Defect Model Name 1",
    "defectModelId": 0,
    "serviceScore": "2",
    "structuralScore": "3",
    "defectType": "string",
    "quantity1DefaultVal": 0,
    "quantity1Units": "string",
    "quantity2DefaultVal": 0,
    "quantity2Units": "string",
    "defectCode": "string",
}

_ORG_DATA_TEMPLATE = {
    "id": 123,
    "fullName": "Test Org",
    "country": "AU",
    "emailDomain": "test-org.com",
    "logoPath": None,
    "orgType": "Asset_Owner",
    "shortName": "TO",
    "sewerData": "Sewer",
    "standardKey": 6,
    "standardDisplayName": "Australia 2013",
    "linkedOrganisations": [],
}

_SUBSTANDARD_DATA_TEMPLATE = {
    "id": 0,
    "materialType": "string",
    "pipeTypeSewer": True,
    "comment": "string",
    "standard": {"id": 0, "displayName": "UK V5", "name": "MSCC5"},
    "region": "UK",
}


@pytest.fixture
def settings():
    return info360_asset_export.Settings()


@pytest.fixture
def multiple_inspections() -> list[api.models.InspectionModel]:
    i1 = api.models.InspectionModel.from_dict(_INSPECTION_MODEL_DATA)
    i1.uuid = "1a1a1a1a-1a1a-1a1a-1a1a-1a1a1a1a1a1a"

    i2 = api.models.InspectionModel.from_dict(deepcopy(_INSPECTION_MODEL_DATA))
    i2.legacy_id = "78762"
    i2.uuid = "2b2b2b2b-2b2b-2b2b-2b2b-2b2b2b2b2b2b"
    i2.file["id"] = 1

    return [i1, i2]


@pytest.fixture
def one_frame_per_inspection():
    f1 = api.models.FramesList.from_dict(_FRAME_DATA_TEMPLATE)
    f1.defect_code = "A"
    f1.remarks = "This is a remark"

    f2 = api.models.FramesList.from_dict(_FRAME_DATA_TEMPLATE)
    f2.id = 2
    f2.parent_video = 1
    f2.defect_code = "B"

    return [f1, f2]


@pytest.fixture
def multiple_frames() -> list[api.models.FramesList]:
    f1 = api.models.FramesList.from_dict(_FRAME_DATA_TEMPLATE)
    f1.defect_code = "A"
    f1.remarks = "This is a remark"

    f2 = api.models.FramesList.from_dict(_FRAME_DATA_TEMPLATE)
    f2.id = 2
    f2.defect_code = "B"

    f3 = api.models.FramesList.from_dict(_FRAME_DATA_TEMPLATE)
    f3.defect_code = None

    f4 = api.models.FramesList.from_dict(_FRAME_DATA_TEMPLATE)
    f4.parent_video = 1
    f4.defect_code = "A"

    return [f1, f2, f3, f4]


def test_report_assets(multiple_inspections, one_frame_per_inspection, settings, output_fs):
    def mock_handler(req: httpx.Request):
        if req.url.path == "/api/v3/inspections2":
            return httpx.Response(
                status_code=200,
                json={
                    "results": [insp.to_dict() for insp in multiple_inspections],
                    "count": 2,
                    "next": None,
                    "previous": None,
                },
            )

        if req.url.path == "/api/v3/inspections/frames":
            return httpx.Response(
                status_code=200,
                json={
                    "count": 2,
                    "results": [frame.to_dict() for frame in one_frame_per_inspection],
                    "next": None,
                    "previous": None,
                },
            )

        if req.url.path == "/api/v3/organisations/123":
            return httpx.Response(status_code=200, json=_ORG_DATA_TEMPLATE)

        if req.url.path == "/api/v3/standards/subcategories":
            return httpx.Response(status_code=200, json={"count": 1, "results": [_SUBSTANDARD_DATA_TEMPLATE]})

        return httpx.Response(404)

    api_client = get_mock_client_with_handler(mock_handler)

    input_data = info360_asset_export.Input(
        export_id=UUID("1a1a1a1a-1a1a-1a1a-1a1a-1a1a1a1a1a1a"),
        target_org_id=123,
        payload=InfoAssetExportPayload(
            inspection_ids=[UUID("1a1a1a1a-1a1a-1a1a-1a1a-1a1a1a1a1a1a"), UUID("2b2b2b2b-2b2b-2b2b-2b2b-2b2b2b2b2b2b")],
            format=ExportFormat.CSV,
            info_asset_export_type=InfoAssetExportType.ASSET,
        ),
    )

    output = info360_asset_export.handler(input_data, settings, api_client, output_fs, max_retry_secs=0)
    with output_fs.open(output.output_file.file_path) as f:
        df = pd.read_csv(f).fillna("")

    assert len(df) == 2, "One row per inspection"

    row_1 = df.iloc[0]

    assert row_1["id"] == "Sub-model testing 09/08/24-78761", "ID is the folder name and legacy ID"
    assert row_1["direction"] == "D"
    assert row_1["hard_wired_service_grade"] == 4
    assert row_1["hard_wired_structural_grade"] == 2
    assert row_1["when_surveyed"] == "2019-08-20"
    assert row_1["material"] == "VitrifiedClay", "Material name has only alphabet characters, no spaces"

    row_2 = df.iloc[1]
    assert row_2["id"] == "Sub-model testing 09/08/24-78762"


def test_report_defects(multiple_inspections, multiple_frames, settings, output_fs):
    def mock_handler(req: httpx.Request):
        if req.url.path == "/api/v3/inspections2":
            return httpx.Response(
                status_code=200,
                json={
                    "results": [insp.to_dict() for insp in multiple_inspections],
                    "count": 2,
                    "next": None,
                    "previous": None,
                },
            )

        if req.url.path == "/api/v3/inspections/frames":
            return httpx.Response(
                status_code=200,
                json={
                    "count": 4,
                    "results": [frame.to_dict() for frame in multiple_frames],
                    "next": None,
                    "previous": None,
                },
            )

        if req.url.path == "/api/v3/organisations/123":
            return httpx.Response(status_code=200, json=_ORG_DATA_TEMPLATE)

        if req.url.path == "/api/v3/standards/subcategories":
            return httpx.Response(status_code=200, json={"count": 1, "results": [_SUBSTANDARD_DATA_TEMPLATE]})

        return httpx.Response(404)

    api_client = get_mock_client_with_handler(mock_handler)

    input_data = info360_asset_export.Input(
        export_id=UUID("1a1a1a1a-1a1a-1a1a-1a1a-1a1a1a1a1a1a"),
        target_org_id=123,
        payload=InfoAssetExportPayload(
            inspection_ids=[UUID("1a1a1a1a-1a1a-1a1a-1a1a-1a1a1a1a1a1a"), UUID("2b2b2b2b-2b2b-2b2b-2b2b-2b2b2b2b2b2b")],
            format=ExportFormat.CSV,
            info_asset_export_type=InfoAssetExportType.DEFECT,
        ),
    )

    output = info360_asset_export.handler(input_data, settings, api_client, output_fs, max_retry_secs=0)
    with output_fs.open(output.output_file.file_path) as f:
        df = pd.read_csv(f).fillna("")

    assert len(df) == 3, "One row per defect frame"

    insp_1_id = "Sub-model testing 09/08/24-78761"
    insp_1_rows = df[(df["id"] == insp_1_id) | (df["id"] == "")]  # First one has an ID, others are empty
    assert len(insp_1_rows) == 2, "Two defects in first inspection"
    assert insp_1_rows.iloc[0]["code"] == "A"
    assert insp_1_rows.iloc[0]["remarks"] == "This is a remark"

    insp_2_id = "Sub-model testing 09/08/24-78762"
    insp_2_rows = df[df["id"] == insp_2_id]
    assert len(insp_2_rows) == 1, "One defect in second inspection"
    assert insp_2_rows.iloc[0]["code"] == "A"
