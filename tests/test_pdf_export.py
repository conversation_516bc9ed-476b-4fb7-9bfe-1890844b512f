import datetime
import zipfile
from copy import deepcopy
from uuid import UUID

import httpx
import pypdf
import pytest
from vapar.clients import api
from vapar.core.exports import BulkInspectionPDFExportPayload, ExportFormat

from activities import generate_bulk_inspection_pdf, zipped_pdf_export
from tests.conftest import get_mock_client_with_handler

_INSPECTION_MODEL_DATA = {
    "uuid": "4d797e1a-3004-4338-897d-7c7d9fb6f052",
    "legacyId": "78761",
    "status": "Uploaded",
    "structural_grade": 2,
    "service_grade": 4,
    "created_by": None,
    "created_at": "2024-08-09T00:27:02.397568Z",
    "Direction": "Downstream",
    "Date": "2019-08-20",
    "LengthSurveyed": 3983.0,
    "GeneralRemarks": "",
    "IsImperial": None,
    "Standard": "Australia 2020",
    "ReviewedBy": None,
    "Time": None,
    "AssetID": "asset id",
    "Material": "Vitrified Clay",
    "HeightDiameter": 8,
    "LocationStreet": "Stree 8 4725 Brookwood Drive",
    "LocationTown": "Roanoke",
    "UpstreamNode": "",
    "DownstreamNode": "01B-3932.0",
    "UseOfDrainSewer": "SS",
    "repairCompletedDate": None,
    "folder": {
        "id": 4776,
        "path": "Sub-model testing 09/08/24",
        "depth": 2,
        "numchild": 0,
        "primary_org": None,
        "secondary_org": None,
        "job_name": "Sub-model testing 09/08/24",
        "created_date": "2024-08-09T00:07:15.965074Z",
        "pipe_type_sewer": False,
        "standard_key": 6,
    },
    "asset": {
        "uuid": "4e252512-15f6-4563-aadc-e85aa6ef05ac",
        "type": "pipe",
        "organisation": 142,
        "standard": 0,
        "createdAt": "2024-08-09T00:26:50.162221Z",
        "AssetID": "asset id",
        "UpstreamNode": "US Node",
        "DownstreamNode": "01B-3932.0",
        "HeightDiameter": 8,
        "Material": "Vitrified Clay",
        "LocationStreet": "Stree 8 4725 Brookwood Drive",
        "LocationTown": "Roanoke",
    },
    "file": {
        "id": 0,
        "url": "uploadedvideofiles/06_03_2024_22_42_34_899046-01b-3933.001b-3932.0_10018_08_20_2019.mp4",
        "hidden": False,
        "filename": "06_03_2024_22_42_34_899046-01b-3933.001b-3932.0_10018_08_20_2019.mp4",
        "file_size": "108.2MB",
        "file_type": "application/octet-stream",
        "upload_org": 142,
        "target_org": 142,
        "upload_user": "VAPAR Admin",
        "uploaded_by": 766,
        "created_time": "2024-08-09T00:07:40.501596Z",
        "job_id": None,
        "job_tree": 4776,
        "upload_completed_type": "2024-08-09T00:08:45.227556Z",
        "upload_completed": True,
        "upload_org_type": "Asset_Owner",
    },
    "extraFields": {"ExpectedLength": "3983.0"},
    "toBeMatched": False,
    "ExpectedLength": "3983.0",
    "start_node": "",
    "end_node": "01B-3932.0",
    "location": {"LocationStreet": "Stree 8 4725 Brookwood Drive", "LocationTown": "Roanoke", "country": "AU"},
    "name": "Stree 8 4725 Brookwood Drive, Roanoke",
    "filename": "06_03_2024_22_42_34_899046-01b-3933.001b-3932.0_10018_08_20_2019.mp4",
    "setupLocation": "Upstream",
    "chainage_unit": "m",
    "WorkOrder": "ABC",
}

_FRAME_DATA_TEMPLATE = {
    "id": 0,
    "inspectionId": 0,
    "imageLocation": "string",
    "imageUrl": "string",
    "frameId": 1,
    "classLabel": "Defect Description 1",
    "classCertainty": ".9",
    "chainage": "string",
    "chainageNumber": "50",
    "isHidden": False,
    "isAccepted": True,
    "allClassBreakdown": "string",
    "atJoint": False,
    "atClock": 2147483647,
    "toClock": 2147483647,
    "contDefectStart": False,
    "contDefectEnd": "",
    "quantity1Value": 0,
    "quantity1Units": "m",
    "quantity2Value": 0,
    "quantity2Units": "m",
    "remarks": "string",
    "isMatched": True,
    "parentVideo": 0,
    "pipeTypeSewer": True,
    "material": "string",
    "defectId": 0,
    "defectClass": "Defect Description 1",
    "defectCode": "string",
    "defectStrScore": "2",
    "defectSerScore": "3",
    "defectScoreSeverity": "string",
    "defectScoreIsShown": True,
    "timeReference": "string",
}

_DEFECT_DATA_TEMPLATE = {
    "id": 0,
    "substandard": {
        "id": 0,
        "materialType": "Vitrified Clay",
        "pipeTypeSewer": True,
        "comment": "string",
        "standard": {"id": 0, "displayName": "Standard 1", "name": "Standard 1"},
        "region": "UK",
    },
    "defectDescription": "Defect Description 1",
    "defectModelName": "Defect Model Name 1",
    "defectModelId": 0,
    "serviceScore": "2",
    "structuralScore": "3",
    "defectType": "string",
    "quantity1DefaultVal": 0,
    "quantity1Units": "string",
    "quantity2DefaultVal": 0,
    "quantity2Units": "string",
    "defectCode": "string",
}

_ORGANISATION_DATA_TEMPLATE = {
    "id": 142,
    "fullName": "Interflow",
    "country": "US",
    "emailDomain": "interflow.com.au",
    "logoPath": "https://vapardevstorage.blob.core.windows.net/orglogos/IF.jpg?st=2024-08-20T03%3A09%3A26Z&se=2024-08"
    "-20T07%3A10%3A26Z&sp=r&spr=https&sv=2021-08-06&sr=c&sig=KJ8IyG9WqRqRBlW2r%2Bit4d"
    "/bExKN3b8XG1qzyhROi6c%3D",
    "orgType": "Contractor",
    "shortName": "IF",
    "sewerData": "Sewer",
    "standardKey": 1,
    "standardDisplayName": "Australia 2013",
    "linkedOrganisations": [],
}

_SUBSTANDARD_DATA_TEMPLATE = {
    "id": 0,
    "materialType": "string",
    "pipeTypeSewer": True,
    "comment": "string",
    "standard": {"id": 0, "displayName": "UK V5", "name": "MSCC5"},
    "region": "UK",
}


@pytest.fixture
def inspection() -> api.models.InspectionModel:
    return api.models.InspectionModel.from_dict(_INSPECTION_MODEL_DATA)


@pytest.fixture
def inspection_with_missing_node() -> api.models.InspectionModel:
    inspection = api.models.InspectionModel.from_dict(_INSPECTION_MODEL_DATA)
    inspection.asset.upstream_node = None
    return inspection


@pytest.fixture
def multiple_inspections() -> list[api.models.InspectionModel]:
    i1 = api.models.InspectionModel.from_dict(deepcopy(_INSPECTION_MODEL_DATA))

    # Has some missing data
    i2 = api.models.InspectionModel.from_dict(deepcopy(_INSPECTION_MODEL_DATA))
    i2.uuid = "1a1a1a1a-3004-4338-897d-7c7d9fb6f052"
    i2["legacyId"] = "78762"
    i2["structural_grade"] = 5
    i2["service_grade"] = 5
    i2.asset.upstream_node = "USNODE"
    i2.asset.downstream_node = "DSNODE"
    i2.length_surveyed = 500
    i2["HeightDiameter"] = 200
    i2.asset["Material"] = "clay"
    i2.direction = api.models.DirectionEnum.UPSTREAM
    i2.date = datetime.datetime(2024, 1, 2)
    i2.file["id"] = 1

    return [i1, i2]


@pytest.fixture
def frame() -> api.models.FramesList:
    return api.models.FramesList.from_dict(_FRAME_DATA_TEMPLATE)


@pytest.fixture
def frame_with_cont_defect() -> api.models.FramesList:
    f = api.models.FramesList.from_dict(_FRAME_DATA_TEMPLATE)
    f.cont_defect_start = True
    f.cont_defect_end = "100"
    return f


@pytest.fixture
def multiple_frames() -> list[api.models.FramesList]:
    f1 = api.models.FramesList.from_dict(_FRAME_DATA_TEMPLATE)
    f1.id = 0
    f1.frame_id = 1
    f1.defect_class = "Defect Description 1"
    f1.class_label = "Defect Description 1"

    f2 = api.models.FramesList.from_dict(_FRAME_DATA_TEMPLATE)
    f2.id = 1
    f2.frame_id = 2
    f2.defect_class = "Defect Description 2"
    f2.class_label = "Defect Description 2"

    f3 = api.models.FramesList.from_dict(_FRAME_DATA_TEMPLATE)
    f3.id = 2
    f3.frame_id = 3
    f3.defect_class = "Defect Description 1"
    f3.class_label = "Defect Description 1"

    f4 = api.models.FramesList.from_dict(_FRAME_DATA_TEMPLATE)
    f4.id = 3
    f4.frame_id = 4
    f4.parent_video = 1
    f4.defect_class = "Defect Description 1"
    f4.class_label = "Defect Description 1"

    return [f1, f2, f3, f4]


@pytest.fixture
def all_defects() -> list[api.models.AllDefects]:
    d1 = api.models.AllDefects.from_dict(_DEFECT_DATA_TEMPLATE)

    d2 = api.models.AllDefects.from_dict(_DEFECT_DATA_TEMPLATE)
    d2.id = 1
    d2.defect_description = "Defect Description 2"
    d2.defect_model_name = "Defect Model Name 2"
    d2.structural_score = 1
    d2.service_score = 1

    d3 = api.models.AllDefects.from_dict(_DEFECT_DATA_TEMPLATE)
    d3.id = 2
    d3.defect_description = "Defect Description 3"
    d3.defect_model_name = "Defect Model Name 3"
    d3.structural_score = 2
    d3.service_score = 5

    return [d1, d2, d3]


@pytest.fixture
def organisation() -> api.models.Organisation:
    return api.models.Organisation.from_dict(_ORGANISATION_DATA_TEMPLATE)


@pytest.fixture
def single_inspection_input(multiple_inspections) -> generate_bulk_inspection_pdf.Input:
    return generate_bulk_inspection_pdf.Input(
        export_id=UUID("00000000-0000-0000-0000-000000000000"),
        target_org_id=123,
        payload=BulkInspectionPDFExportPayload(
            inspection_ids=[UUID("4d797e1a-3004-4338-897d-7c7d9fb6f052")],
            format=ExportFormat.PDF,
        ),
    )


@pytest.fixture
def multiple_inspections_input() -> generate_bulk_inspection_pdf.Input:
    return generate_bulk_inspection_pdf.Input(
        export_id=UUID("00000000-0000-0000-0000-000000000000"),
        target_org_id=123,
        payload=BulkInspectionPDFExportPayload(
            inspection_ids=[
                UUID("4d797e1a-3004-4338-897d-7c7d9fb6f052"),
                UUID("1a1a1a1a-3004-4338-897d-7c7d9fb6f052"),
            ],
            format=ExportFormat.PDF,
        ),
    )


def test_single_inspection_pdf_export(inspection, frame, all_defects, organisation, single_inspection_input, output_fs):
    def mock_handler(req: httpx.Request):
        if req.url.path == "/api/v3/inspections2":
            return httpx.Response(
                status_code=200, json={"results": [inspection.to_dict()], "count": 1, "next": None, "previous": None}
            )

        if req.url.path == "/api/v3/inspections/frames":
            return httpx.Response(status_code=200, json={"results": [frame.to_dict()], "count": 1, "next": None})

        if req.url.path == "/api/v3/standards/defects":
            return httpx.Response(status_code=200, json=[d.to_dict() for d in all_defects])

        if req.url.path == "/api/v3/organisations/142":
            return httpx.Response(status_code=200, json=organisation.to_dict())

        if req.url.path == "/api/v3/standards/subcategories":
            return httpx.Response(
                status_code=200,
                json={"count": 1, "results": [_SUBSTANDARD_DATA_TEMPLATE], "next": None, "previous": None},
            )

        return httpx.Response(404)

    client = get_mock_client_with_handler(mock_handler)
    response = generate_bulk_inspection_pdf.handler(
        input_data=single_inspection_input,
        api_client=client,
        settings=generate_bulk_inspection_pdf.Settings(),
        defect_cache={},
        substd_cache={},
        output_fs=output_fs,
        max_retry_secs=0,
    )
    assert response.output_file.file_display_name == "asset id_US Node_01B-3932.0_VS_ID_78761.pdf", "All data present"

    with output_fs.open(response.output_file.file_path, "rb") as f:
        reader = pypdf.PdfReader(f)
        page_count = len(reader.pages)
        assert page_count == 2

        # check the correct data is on the page
        page = reader.pages[0]
        inspection_fields_displayed = [
            # "legacy_id", - # TODO: need to address this
            # "date", # TODO: date formatting issue
            "direction",
            # "service_grade",
            # "structural_grade",
            "work_order",
        ]
        for field in inspection_fields_displayed:
            assert str(getattr(inspection, field)) in page.extract_text(), f"Field {field} not found in PDF"

        asset_fields_displayed = [
            "material",
            "height_diameter",
            "location_street",
        ]
        for field in asset_fields_displayed:
            assert str(getattr(inspection.asset, field)) in page.extract_text(), f"Field {field} not found in PDF"

        inspection_additional_props_displayed = [
            "service_grade",
            "structural_grade",
        ]
        for field in inspection_additional_props_displayed:
            assert (
                str(inspection.additional_properties[field]) in page.extract_text()
            ), f"Field {field} not found in PDF"


def test_pdf_export_with_missing_node(
    inspection_with_missing_node, frame, all_defects, organisation, single_inspection_input, output_fs
):
    def mock_handler(req: httpx.Request):
        if req.url.path == "/api/v3/inspections2":
            return httpx.Response(
                status_code=200,
                json={"results": [inspection_with_missing_node.to_dict()], "count": 1, "next": None, "previous": None},
            )

        if req.url.path == "/api/v3/inspections/frames":
            return httpx.Response(status_code=200, json={"results": [frame.to_dict()], "count": 1, "next": None})

        if req.url.path == "/api/v3/standards/defects":
            return httpx.Response(status_code=200, json=[d.to_dict() for d in all_defects])

        if req.url.path == "/api/v3/organisations/142":
            return httpx.Response(status_code=200, json=organisation.to_dict())

        if req.url.path == "/api/v3/standards/subcategories":
            return httpx.Response(
                status_code=200,
                json={"count": 1, "results": [_SUBSTANDARD_DATA_TEMPLATE], "next": None, "previous": None},
            )

        return httpx.Response(404)

    client = get_mock_client_with_handler(mock_handler)
    response = generate_bulk_inspection_pdf.handler(
        input_data=single_inspection_input,
        api_client=client,
        settings=generate_bulk_inspection_pdf.Settings(),
        defect_cache={},
        substd_cache={},
        output_fs=output_fs,
        max_retry_secs=0,
    )
    assert response.output_file.file_display_name == "asset id_01B-3932.0_VS_ID_78761.pdf", "No upstream node"

    with output_fs.open(response.output_file.file_path, "rb") as f:
        reader = pypdf.PdfReader(f)
        page_count = len(reader.pages)
        assert page_count == 2

        # check the correct data is on the page
        page = reader.pages[0]
        inspection_fields_displayed = [
            "direction",
            "work_order",
        ]
        for field in inspection_fields_displayed:
            assert (
                str(getattr(inspection_with_missing_node, field)) in page.extract_text()
            ), f"Field {field} not found in PDF"

        asset_fields_displayed = [
            "material",
            "height_diameter",
            "location_street",
        ]
        for field in asset_fields_displayed:
            assert (
                str(getattr(inspection_with_missing_node.asset, field)) in page.extract_text()
            ), f"Field {field} not found in PDF"

        inspection_additional_props_displayed = [
            "service_grade",
            "structural_grade",
        ]
        for field in inspection_additional_props_displayed:
            assert (
                str(inspection_with_missing_node.additional_properties[field]) in page.extract_text()
            ), f"Field {field} not found in PDF"


def test_pdf_export_with_cont_defect(
    frame_with_cont_defect, all_defects, organisation, single_inspection_input, output_fs
):
    def mock_handler(req: httpx.Request):
        if req.url.path == "/api/v3/inspections2":
            return httpx.Response(
                status_code=200,
                json={"results": [_INSPECTION_MODEL_DATA], "count": 1, "next": None, "previous": None},
            )

        if req.url.path == "/api/v3/inspections/frames":
            return httpx.Response(
                status_code=200, json={"results": [frame_with_cont_defect.to_dict()], "count": 1, "next": None}
            )

        if req.url.path == "/api/v3/standards/defects":
            return httpx.Response(status_code=200, json=[d.to_dict() for d in all_defects])

        if req.url.path == "/api/v3/organisations/142":
            return httpx.Response(status_code=200, json=organisation.to_dict())

        if req.url.path == "/api/v3/standards/subcategories":
            return httpx.Response(
                status_code=200,
                json={"count": 1, "results": [_SUBSTANDARD_DATA_TEMPLATE], "next": None, "previous": None},
            )

        return httpx.Response(404)

    client = get_mock_client_with_handler(mock_handler)
    response = generate_bulk_inspection_pdf.handler(
        input_data=single_inspection_input,
        api_client=client,
        settings=generate_bulk_inspection_pdf.Settings(),
        defect_cache={},
        substd_cache={},
        output_fs=output_fs,
        max_retry_secs=0,
    )
    assert type(response) is generate_bulk_inspection_pdf.Output

    with output_fs.open(response.output_file.file_path, "rb") as f:
        reader = pypdf.PdfReader(f)
        page_count = len(reader.pages)
        assert page_count == 2

        page = reader.pages[1]
        frames_page_text = page.extract_text()

    assert "Defect Description 1 - Start 50.00 - Finish 100.00" in frames_page_text, "Expected description"


def test_multiple_inspections_pdf_export(
    multiple_inspections, multiple_frames, all_defects, organisation, multiple_inspections_input, output_fs
):
    def mock_handler(req: httpx.Request):
        if req.url.path == "/api/v3/inspections2":
            return httpx.Response(
                status_code=200,
                json={
                    "results": [insp.to_dict() for insp in multiple_inspections],
                    "count": 2,
                    "next": None,
                    "previous": None,
                },
            )

        if req.url.path == "/api/v3/inspections/frames":
            return httpx.Response(
                status_code=200,
                json={"results": [f.to_dict() for f in multiple_frames], "count": 3, "next": None, "previous": None},
            )

        if req.url.path == "/api/v3/standards/defects":
            return httpx.Response(status_code=200, json=[d.to_dict() for d in all_defects])

        if req.url.path == "/api/v3/organisations/142":
            return httpx.Response(status_code=200, json=organisation.to_dict())

        if req.url.path == "/api/v3/standards/subcategories":
            return httpx.Response(
                status_code=200,
                json={"count": 1, "results": [_SUBSTANDARD_DATA_TEMPLATE], "next": None, "previous": None},
            )

        return httpx.Response(404)

    client = get_mock_client_with_handler(mock_handler)
    response = generate_bulk_inspection_pdf.handler(
        input_data=multiple_inspections_input,
        api_client=client,
        settings=generate_bulk_inspection_pdf.Settings(),
        defect_cache={},
        substd_cache={},
        output_fs=output_fs,
        max_retry_secs=0,
    )

    with output_fs.open(response.output_file.file_path, "rb") as f:
        reader = pypdf.PdfReader(f)
        page_count = len(reader.pages)
        assert page_count == 4

        # check the correct data is on the page
        insp_1_page_1 = reader.pages[0]
        insp_1_page_2 = reader.pages[1]
        insp_2_page_1 = reader.pages[2]
        insp_2_page_2 = reader.pages[3]

        inspection_fields_displayed = [
            "direction",
            "work_order",
        ]
        for field in inspection_fields_displayed:
            insp_1_value = str(getattr(multiple_inspections[0], field))
            insp_2_value = str(getattr(multiple_inspections[1], field))

            assert insp_1_value in insp_1_page_1.extract_text()
            assert insp_1_value in insp_1_page_2.extract_text()
            assert insp_2_value in insp_2_page_1.extract_text()
            assert insp_2_value in insp_2_page_2.extract_text()

        asset_fields_displayed = [
            "material",
            "height_diameter",
            "location_street",
        ]
        for field in asset_fields_displayed:
            insp_1_value = str(getattr(multiple_inspections[0].asset, field))
            insp_2_value = str(getattr(multiple_inspections[1].asset, field))

            assert insp_1_value in insp_1_page_1.extract_text()
            assert insp_1_value in insp_1_page_2.extract_text()
            assert insp_2_value in insp_2_page_1.extract_text()
            assert insp_2_value in insp_2_page_2.extract_text()

        inspection_additional_props_displayed = [
            "service_grade",
            "structural_grade",
        ]
        for field in inspection_additional_props_displayed:
            assert (
                str(multiple_inspections[0].additional_properties[field]) in insp_1_page_1.extract_text()
            ), f"Field {field} not found in page 1"
            assert (
                str(multiple_inspections[0].additional_properties[field]) in insp_1_page_2.extract_text()
            ), f"Field {field} not found in page 2"
            assert (
                str(multiple_inspections[1].additional_properties[field]) in insp_2_page_1.extract_text()
            ), f"Field {field} not found in page 3"
            assert (
                str(multiple_inspections[1].additional_properties[field]) in insp_2_page_2.extract_text()
            ), f"Field {field} not found in page 4"


def test_footer_data():
    from activities.generate_bulk_inspection_pdf import FooterTemplate, get_footer_data

    frames = [api.models.FramesList.from_dict(_FRAME_DATA_TEMPLATE)]
    defects = [api.models.AllDefects.from_dict(_DEFECT_DATA_TEMPLATE)]
    chainage = 100

    footer_data = get_footer_data(frames=frames, chainage=chainage, defects=defects)

    assert isinstance(footer_data, FooterTemplate)


def test_zipped_pdf_export(
    multiple_inspections, multiple_frames, all_defects, organisation, multiple_inspections_input, output_fs
):
    def mock_handler(req: httpx.Request):
        if req.url.path == "/api/v3/inspections2":
            return httpx.Response(
                status_code=200,
                json={
                    "results": [insp.to_dict() for insp in multiple_inspections],
                    "count": 2,
                    "next": None,
                    "previous": None,
                },
            )

        if req.url.path == "/api/v3/inspections/frames":
            return httpx.Response(
                status_code=200,
                json={"results": [f.to_dict() for f in multiple_frames], "count": 3, "next": None, "previous": None},
            )

        if req.url.path == "/api/v3/standards/defects":
            return httpx.Response(status_code=200, json=[d.to_dict() for d in all_defects])

        if req.url.path == "/api/v3/organisations/142":
            return httpx.Response(status_code=200, json=organisation.to_dict())

        if req.url.path == "/api/v3/standards/subcategories":
            return httpx.Response(
                status_code=200,
                json={"count": 1, "results": [_SUBSTANDARD_DATA_TEMPLATE], "next": None, "previous": None},
            )

        return httpx.Response(404)

    client = get_mock_client_with_handler(mock_handler)
    response = zipped_pdf_export.handler(
        input_data=multiple_inspections_input,
        api_client=client,
        settings=zipped_pdf_export.Settings(),
        defect_cache={},
        substd_cache={},
        output_fs=output_fs,
        max_retry_secs=0,
    )
    assert type(response) is zipped_pdf_export.Output

    with output_fs.open(response.output_file.file_path, "rb") as z, zipfile.ZipFile(z) as zf:
        pdf = zf.namelist()[0]
        with zf.open(pdf) as f:
            reader = pypdf.PdfReader(f)
            page_count = len(reader.pages)
            assert page_count == 2

            # check the correct data is on the page
            insp_1_page_1 = reader.pages[0]
            insp_1_page_2 = reader.pages[1]

            inspection_fields_displayed = [
                "direction",
                "work_order",
            ]
            for field in inspection_fields_displayed:
                insp_1_value = str(getattr(multiple_inspections[0], field))

                assert insp_1_value in insp_1_page_1.extract_text()
                assert insp_1_value in insp_1_page_2.extract_text()

            asset_fields_displayed = [
                "material",
                "height_diameter",
                "location_street",
            ]
            for field in asset_fields_displayed:
                insp_1_value = str(getattr(multiple_inspections[0].asset, field))

                assert insp_1_value in insp_1_page_1.extract_text()
                assert insp_1_value in insp_1_page_2.extract_text()

            inspection_additional_props_displayed = [
                "service_grade",
                "structural_grade",
            ]
            for field in inspection_additional_props_displayed:
                assert (
                    str(multiple_inspections[0].additional_properties[field]) in insp_1_page_1.extract_text()
                ), f"Field {field} not found in page 1"
                assert (
                    str(multiple_inspections[0].additional_properties[field]) in insp_1_page_2.extract_text()
                ), f"Field {field} not found in page 2"
