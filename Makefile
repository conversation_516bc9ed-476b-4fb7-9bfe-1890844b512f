
.PHONY: lint fix test get-reqs deploy-dev deploy-staging deploy-prod

lint:
	poetry run ruff check
	poetry run ruff format --check

fix:
	poetry run ruff check --fix
	poetry run ruff format

test:
	poetry run pytest

get-reqs:
	poetry export -f requirements.txt --without-hashes | grep -ve 'https://bitbucket.org/teamvapar/vapar.git' > requirements.txt && \
	VAPARLIB_VERSION=$$(grep https://bitbucket.org/teamvapar/vapar.git pyproject.toml | sed -n 's/.*tag = "\([^"]*\)".*/\1/p') && \
	echo "vapar @ git+https://x-token-auth:$${BITBUCKET_VAPAR_REPO_TOKEN}@bitbucket.org/teamvapar/vapar.git@$${VAPARLIB_VERSION}" >> requirements.txt

install-az:
	curl -sL https://aka.ms/InstallAzureCLIDeb | bash
	apt-get update && apt-get install gpg wget  -y
	wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor | tee /usr/share/keyrings/microsoft-prod.gpg
	wget -q https://packages.microsoft.com/config/debian/12/prod.list
	mv prod.list /etc/apt/sources.list.d/microsoft-prod.list
	chown root:root /usr/share/keyrings/microsoft-prod.gpg
	chown root:root /etc/apt/sources.list.d/microsoft-prod.list
	apt-get update && apt-get install azure-functions-core-tools-4 libicu-dev  -y

deploy-dev:
	make get-reqs
	func azure functionapp publish backend-tasks-app-dev --verbose --slot staging --python

deploy-staging:
	make get-reqs
	func azure functionapp publish backend-tasks-app-uat --verbose --python

deploy-prod-au:
	make get-reqs
	func azure functionapp publish backend-tasks-app-prod --verbose --slot staging --python

deploy-prod-uk:
	make get-reqs
	func azure functionapp publish uk-backend-tasks-app-prod-01 --verbose --slot staging --python

auth-vapar:
	poetry config repositories.vapar https://bitbucket.org/teamvapar/vapar.git
	poetry config http-basic.vapar x-token-auth ${BITBUCKET_VAPAR_REPO_TOKEN}