FROM python:3.10-slim-buster
COPY --from=openjdk:11-jre-slim /usr/local/openjdk-11 /usr/local/openjdk-11

ENV JAVA_HOME /usr/local/openjdk-11

RUN update-alternatives --install /usr/bin/java java /usr/local/openjdk-11/bin/java 1

WORKDIR /app

# Pyppeteer/chromium dependencies
RUN apt-get update \
    && apt-get install -y wget gnupg ffmpeg \
    && wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list' \
    && apt-get update \
    && apt-get install -y google-chrome-stable fonts-ipafont-gothic fonts-wqy-zenhei fonts-thai-tlwg fonts-kacst fonts-freefont-ttf libxss1 \
      --no-install-recommends \
    && rm -rf /var/lib/apt/lists/*

### For running the container locally without CI:
#ARG BITBUCKET_VAPAR_REPO_TOKEN
#COPY . .
#
#RUN apt-get update && apt-get install -y curl make \
#	&& curl -sSL https://install.python-poetry.org | python3
#
#RUN export PATH=$PATH:$HOME/.local/bin && make auth-vapar && poetry install --no-root --with=dev
