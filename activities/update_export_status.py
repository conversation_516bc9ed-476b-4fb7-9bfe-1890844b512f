"""
Function Activity that updates the status of an export request.
"""

import logging
from uuid import UUID

from pydantic import BaseModel
from vapar.clients import api
from vapar.constants.exports import ExportStatus, ExportStatusReason

log = logging.getLogger(__name__)


class Input(BaseModel):
    export_id: UUID
    target_org_id: int
    status: ExportStatus
    status_reason: ExportStatusReason | None = None
    instance_id: str = ""


def _execute_update_status(
    export_id: UUID, status: ExportStatus, status_reason: ExportStatusReason, client: api.AuthenticatedClient
):
    body = api.models.PatchedExportUpdateRequest(
        status=status,
        status_reason=status_reason,
    )
    res = api.endpoints.exports_partial_update.sync_detailed(uuid=str(export_id), body=body, client=client)
    api.raise_on_status(res)


def handler(input_dto: Input, api_client: api.AuthenticatedClient, max_retry_secs: int = 60) -> None:
    log.info(
        "Starting UpdateExportStatus activity",
        extra={
            "export_id": input_dto.export_id,
            "status": input_dto.status,
            "status_reason": input_dto.status_reason,
            "target_org_id": input_dto.target_org_id,
            "instance_id": input_dto.instance_id,
        },
    )
    api_client = api_client.with_headers({api.TARGET_ORG_ID_HEADER: str(input_dto.target_org_id)})
    api.with_retries(max_retry_secs)(_execute_update_status)(
        input_dto.export_id, input_dto.status, input_dto.status_reason, api_client
    )
