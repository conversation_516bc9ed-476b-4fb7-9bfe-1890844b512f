"""
Function Activity that fetches the full payload for an export request.
"""

import logging
from uuid import UUID

from pydantic import BaseModel
from vapar.clients import api
from vapar.core.exports import AnyExportPayload

log = logging.getLogger(__name__)


class Input(BaseModel):
    export_id: UUID
    target_org_id: int
    instance_id: str = ""


class Output(BaseModel):
    full_payload: AnyExportPayload


def _fetch_export_payload(
    input_data: Input, api_client: api.AuthenticatedClient, max_retry_secs: int = 60
) -> AnyExportPayload:
    res = api.with_retries(max_retry_secs)(api.endpoints.exports_payload_retrieve.sync_detailed)(
        uuid=str(input_data.export_id), client=api_client
    )
    api.raise_on_status(res)
    return AnyExportPayload.model_validate(res.parsed.to_dict())


def handler(input_data: Input, api_client: api.AuthenticatedClient, max_retry_secs: int = 60) -> Output:
    log.info(
        "Starting FetchExportPayload activity",
        extra={
            "export_id": input_data.export_id,
            "target_org_id": input_data.target_org_id,
            "instance_id": input_data.instance_id,
        },
    )
    api_client = api_client.with_headers({api.TARGET_ORG_ID_HEADER: str(input_data.target_org_id)})
    payload = _fetch_export_payload(input_data, api_client, max_retry_secs)

    return Output(full_payload=payload)
