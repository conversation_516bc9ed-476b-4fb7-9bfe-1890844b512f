"""
Function activity that generates a .zip of inspection PDFs (one PDF per inspection)
"""

import logging
import tempfile
import uuid
import zipfile
from pathlib import Path

from fsspec import AbstractFileSystem
from fsspec.implementations.dirfs import DirFileSystem
from fsspec.implementations.local import LocalFileSystem
from pydantic import BaseModel
from pydantic_settings import BaseSettings
from vapar.clients import api
from vapar.constants.exports import ExportFormat, ExportStatusReason
from vapar.core.exports import BulkInspectionPDFExportPayload

from activities import generate_bulk_inspection_pdf
from activities.generate_bulk_inspection_pdf import build_single_inspection_display_name
from common.defects import SubStandardDefectCache
from common.models import ExportOutputFile, ExportStatusException
from common.reports import build_timestamped_display_name
from common.standards import SubStandardCache

log = logging.getLogger(__name__)


class Input(BaseModel):
    export_id: uuid.UUID
    target_org_id: int
    payload: BulkInspectionPDFExportPayload


class Output(BaseModel):
    output_file: ExportOutputFile | ExportStatusReason


class Settings(BaseSettings):
    export_outputs_folder_name: str = "export-outputs"
    vapar_api_base_url: str = "https://www.vapar.solutions/"
    pyppeteer_home: str = "chromium"

    image_secret_key_b64encoded: str = ""
    salt_b64encoded: str = ""


def _zip_and_delete_pdfs(
    folder: str | None,
    working_fs: AbstractFileSystem,
    output_fs: AbstractFileSystem,
    pdfs: list[ExportOutputFile],
    file_display_name: str | None = None,
) -> ExportOutputFile:
    """
    Given a list of file data, create a .zip, and delete the original pdfs

    :param folder: The folder to save the zip file under
    :param working_fs: The filesystem that the individual pdfs are stored on
    :param output_fs: The filesystem to save the zip file on
    :param pdfs: The list of pdfs to zip
    :param file_display_name: The display name to return for the zip file
    :return: The file data for the zipped file
    """
    zip_filename = f"{uuid.uuid4()}.zip"
    zip_file_path = str(Path(folder, zip_filename)) if folder else zip_filename

    with output_fs.open(zip_file_path, "wb") as z, zipfile.ZipFile(z, "w") as zipf:
        for pdf in pdfs:
            with working_fs.open(pdf.file_path, "rb") as f:
                zipf.writestr(pdf.file_display_name or pdf.file_path, f.read())
            working_fs.delete(pdf.file_path)

    return ExportOutputFile(
        file_path=zip_file_path,
        size=output_fs.size(zip_file_path),
        mime_type="application/zip",
        extension=".zip",
        file_display_name=file_display_name,
    )


def handler(
    input_data: Input,
    api_client: api.AuthenticatedClient,
    settings: Settings,
    output_fs: AbstractFileSystem,
    defect_cache: SubStandardDefectCache | None = None,
    substd_cache: SubStandardCache | None = None,
    max_retry_secs: int = 60,
) -> Output:
    log.info(
        "Starting ZippedPdfExport activity",
        extra={"inspection_ids": input_data.payload.inspection_ids},
    )

    api_client = api_client.with_headers({api.TARGET_ORG_ID_HEADER: str(input_data.target_org_id)})
    try:
        if len(input_data.payload.inspection_ids) == 0:
            raise ExportStatusException(ExportStatusReason.INVALID_INSPECTION_ID)

        inspections = generate_bulk_inspection_pdf.collect_inspections_data(
            input_data.payload.inspection_ids, api_client, settings, defect_cache, substd_cache, max_retry_secs
        )

        http_client = api_client.get_httpx_client()

        pdfs = []
        with tempfile.TemporaryDirectory() as temp_dir:
            working_fs = DirFileSystem(path=temp_dir, fs=LocalFileSystem())
            for inspection in inspections:
                html_str = generate_bulk_inspection_pdf.render_pdf([inspection], http_client)
                display_name = build_single_inspection_display_name(
                    api.models.InspectionModel.from_dict(inspection.inspection)
                )
                single_inspection_pdf = generate_bulk_inspection_pdf.write_pdf_report(
                    html=html_str,
                    output_fs=working_fs,
                    filename=f"{uuid.uuid4()}.pdf",
                    pyppeteer_home=settings.pyppeteer_home,
                    file_display_name=display_name,
                )
                pdfs.append(single_inspection_pdf)
                log.info(f"Successfully generated PDF for inspection {inspection.inspection['uuid']}")

            display_name = build_timestamped_display_name("VS_Reports", ExportFormat.ZIP)
            log.info("Zipping generated PDFs...")
            zipped_pdfs = _zip_and_delete_pdfs(
                folder=settings.export_outputs_folder_name,
                working_fs=working_fs,
                output_fs=output_fs,
                pdfs=pdfs,
                file_display_name=display_name,
            )
            log.info("Successfully zipped generated PDFs")

    except ExportStatusException as e:
        return Output(output_file=e.status_reason)

    return Output(output_file=zipped_pdfs)
