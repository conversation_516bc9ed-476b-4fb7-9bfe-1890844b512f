"""
Function Activity that fetches the basic details of an import
"""

import json
import logging
from uuid import UUID

from pydantic import BaseModel
from vapar.clients import api

from common.models import ImportDetails

log = logging.getLogger(__name__)


class Input(BaseModel):
    import_id: UUID
    target_org_id: int
    instance_id: str = ""


class Output(BaseModel):
    details: ImportDetails | None


async def _fetch_import_details(
    input_data: Input, api_client: api.AuthenticatedClient, max_retry_secs: int = 60
) -> ImportDetails | None:
    res = await api.endpoints.imports_retrieve.asyncio_detailed(id=str(input_data.import_id), client=api_client)
    if res.status_code == 404:
        log.info("Import not found", extra={"import_id": input_data.import_id})
        return None
    api.raise_on_status(res)

    # Needed because the code-gen'd types don't handle discriminating between the union of payload types properly,
    # leading to a blank AssetImportPayload() object being returned
    as_dict = json.loads(res.content)
    payload = as_dict.get("payload", {})

    return ImportDetails(
        id=res.parsed.id,
        payload=payload,
        status=res.parsed.status.value,
        status_reason=res.parsed.status_reason.value if res.parsed.status_reason else None,
        type=res.parsed.type.value,
        created_at=res.parsed.created_at,
        updated_at=res.parsed.updated_at,
        completed_at=res.parsed.completed_at or None,
    )


async def handler(input_data: Input, api_client: api.AuthenticatedClient, max_retry_secs: int = 60) -> Output:
    log.info(
        "Starting FetchImportDetails activity",
        extra={
            "import_id": input_data.import_id,
            "target_org_id": input_data.target_org_id,
            "instance_id": input_data.instance_id,
        },
    )
    api_client = api_client.with_headers({api.TARGET_ORG_ID_HEADER: str(input_data.target_org_id)})
    details = await api.with_retries(max_retry_secs)(_fetch_import_details)(input_data, api_client, max_retry_secs)

    return Output(details=details)
