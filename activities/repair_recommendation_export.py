import logging
from datetime import datetime, timezone
from http import HTTPStatus
from uuid import UUID

import pandas as pd
from fsspec import AbstractFileSystem
from pydantic import BaseModel
from pydantic_settings import BaseSettings
from vapar.clients import api
from vapar.constants.exports import ExportStatusReason
from vapar.core.exports import RepairRecommendationExportPayload

from common.models import ExportOutputFile, ExportStatusException
from common.reports import build_timestamped_display_name, write_tabular_report

log = logging.getLogger(__name__)

CSV_HEADERS = (
    "ID",
    "Location(Name)",
    "Upstream Node",
    "Downstream Node",
    "Direction",
    "Structural Grade",
    "Service Grade",
    "Date Captured",
    "Inspected Length",
    "Diameter",
    "Material",
    "Work Order",
    "Video File",
    "Inspection Notes",
    "Workflow Status",
    "Inspection Frequency",
    "Asset ID",
    "Repair Notes",
    "No Actions",
    "Roots",
    "Cleaning",
    "Patching Required",
    "Patches",
    "Dig Up Required",
    "Lining Required",
    "Likelihood Assessment",
    "Likelihood Comment",
    "Consequence Assessment",
    "Consequence Comment",
    "Repair Cost Estimate",
    "Creator",
)


class Settings(BaseSettings):
    export_outputs_folder_name: str = "export-outputs"


class Input(BaseModel):
    export_id: UUID
    target_org_id: int
    payload: RepairRecommendationExportPayload
    instance_id: str = ""


class Output(BaseModel):
    output_file: ExportOutputFile | ExportStatusReason


def _fetch_custom_repair_values(
    inspection_id: int, api_client: api.AuthenticatedClient, max_retry_secs: int = 60
) -> list[api.models.CustomRepairValue]:
    res = api.with_retries(max_retry_secs)(api.endpoints.repairs_custom_value_list.sync_detailed)(
        inspection_id=inspection_id, client=api_client
    )
    if res.status_code == 404:
        return []
    else:
        api.raise_on_status(res)
        return res.parsed


def _fetch_repairrecommendation(
    inspection_id: int, api_client: api.AuthenticatedClient, max_retry_secs: int = 60
) -> api.models.RepairPlan:
    res = api.with_retries(max_retry_secs)(api.endpoints.inspections_repairplan_retrieve.sync_detailed)(
        inspection_id=inspection_id, client=api_client
    )
    if res.status_code == HTTPStatus.NOT_FOUND:
        return None
    api.raise_on_status(res)
    return res.parsed


def _fetch_custom_repair_type(
    inspection_id: int, api_client: api.AuthenticatedClient, max_retry_secs: int = 60
) -> api.models.CustomRepairType:
    res = api.with_retries(max_retry_secs)(api.endpoints.repairs_custom_types_list.sync_detailed)(
        inspection_id=inspection_id, client=api_client
    )
    api.raise_on_status(res)
    return res.parsed


def _fetch_organisation_data(
    org_id: int, api_client: api.AuthenticatedClient, max_retry_secs: int = 60
) -> api.models.Organisation:
    res = api.with_retries(max_retry_secs)(api.endpoints.organisations_retrieve.sync_detailed)(
        id=org_id, client=api_client
    )
    api.raise_on_status(res)
    return res.parsed


def _fetch_inspection(
    inspection_uuid: str, use_header_names: bool, api_client: api.AuthenticatedClient, max_retry_secs: int = 60
) -> api.models.InspectionModel:
    res = api.with_retries(max_retry_secs)(api.endpoints.inspections2_retrieve.sync_detailed)(
        uuid=inspection_uuid,
        client=api_client,
        use_header_names=use_header_names,
    )
    api.raise_on_status(res)
    return res.parsed


def is_uploaded_by_contractor(
    inspection: api.models.InspectionModel,
    target_org_is_asset_owner: bool,
    api_client: api.AuthenticatedClient,
    max_retry_secs: int = 60,
) -> bool:
    upload_org_id = (
        inspection.file.upload_org
        if isinstance(inspection.file, api.models.FileInfo)
        else inspection.file["upload_org"]
    )
    upload_org = api.with_retries(max_retry_secs)(_fetch_organisation_data)(upload_org_id, api_client)
    is_contractor = upload_org.org_type == "Contractor"
    uploaded_by_contractor = is_contractor and target_org_is_asset_owner

    return uploaded_by_contractor


def get_custom_row(inspection_id: int, api_client: api.AuthenticatedClient, max_retry_secs: int = 60) -> dict:
    cdata = []

    custom_dict = {}
    cdict = {}
    odict = {}

    try:
        crvs = api.with_retries(max_retry_secs)(_fetch_custom_repair_values)(inspection_id, api_client)
        for c in crvs:
            cdata.append(c.to_dict())
    except api.APIStatusException:
        crv = {
            "id": 0,
            "custom_Repair_Type": 0,
            "cValueText": None,
            "cValueNumber": None,
            "cValueBool": None,
            "oValueText": None,
            "oValueNumber": None,
            "oValueBool": None,
        }
        cdata.append(crv)

    for row in cdata:
        crt = row.get("custom_Repair_Type", 0)

        for c_key in ["cValueText", "cValueNumber", "cValueBool"]:
            if row[c_key] is not None:
                cdict[crt] = row[c_key]

        for o_key in ["oValueText", "oValueNumber", "oValueBool"]:
            if row[o_key] is not None:
                odict[crt] = row[o_key]

    custom_dict["contractor"] = cdict
    custom_dict["owner"] = odict

    return custom_dict


def get_rr_data(
    mpl_ids: list[int],
    ins_ids: list[UUID],
    is_asset_owner: bool,
    api_client: api.AuthenticatedClient,
    max_retry_secs: int = 60,
) -> list[dict]:
    data = []
    ids_with_repairs = []
    for i, inspection_uuid in enumerate(ins_ids):
        repair = api.with_retries(max_retry_secs)(_fetch_repairrecommendation)(inspection_uuid, api_client)
        if repair is None:
            continue
        ids_with_repairs.append(inspection_uuid)
        repairplan_dict = repair.to_dict()
        repairplan_dict["ins_uuid"] = ins_ids[i]
        repairplan_dict["target"] = mpl_ids[i]
        data.append(repairplan_dict)

    missing_ids = set(ins_ids) - set(ids_with_repairs)

    for inspection_id in missing_ids:
        mpd_id = mpl_ids[ins_ids.index(inspection_id)]
        rr_dict = {
            "id": "",
            "target": mpd_id,
            "ins_uuid": inspection_id,
            "inspectionId": inspection_id,
            "createdAt": datetime.now(timezone.utc),
            "updatedAt": datetime.now(timezone.utc),
            "junctions": 0,
            "frequency": None,
            "likelihood": None,
            "likelihoodComment": None,
            "consequence": None,
            "consequenceComment": None,
            "generalNotes": "",
        }
        rr_dict["vaparPlan"] = {"actionSummary": "", "actor": "vapar", "generalNotes": "", "items": []}
        if is_asset_owner:
            rr_dict["ownerPlan"] = {"actionSummary": None, "actor": "owner", "generalNotes": "", "items": []}
        else:
            rr_dict["contractorPlan"] = {"actionSummary": None, "actor": "contractor", "generalNotes": "", "items": []}

        data.append(rr_dict)

    return data


def _update_actor_repair_item_values(actor_repair_items: dict, actor_repair_item_dict: dict) -> dict:
    """Updates actor repair item dict with values from repair item response."""
    for repair_item in actor_repair_items:
        if repair_item["repairType"] in actor_repair_item_dict:
            actor_repair_item_dict[repair_item["repairType"]] = True
            # Extracts metadata from Patch repair item.
            if repair_item["repairType"] == "Patch":
                patch_count_item = next(filter(lambda x: x["key"] == "Count", repair_item["metadata"]), None)
                patch_count = patch_count_item["value"] if patch_count_item is not None else 0
                actor_repair_item_dict["Patch Count"] = patch_count

    return actor_repair_item_dict


def generate_repair_recommendation_df(
    mpl_ids: list[int],
    ins_ids: list[UUID],
    is_asset_owner: bool,
    header_list: list[str],
    custom_repair_header: list[api.models.CustomRepairType],
    insert_index: int,
    api_client: api.AuthenticatedClient,
    max_retry_secs: int = 60,
) -> pd.DataFrame:
    data = get_rr_data(mpl_ids, ins_ids, is_asset_owner, api_client)
    rows = []
    for row in data:
        customrow = get_custom_row(row["target"], api_client)
        contract_values_list = []
        owner_values_list = []

        for key in custom_repair_header:
            contract_values_list.append(customrow.get("contractor", {}).get(key.id, ""))
            owner_values_list.append(customrow.get("owner", {}).get(key.id, ""))

        inspection = api.with_retries(max_retry_secs)(_fetch_inspection)(row["ins_uuid"], True, api_client)

        if inspection.status in ["Planned", "Uploaded"]:
            continue

        frequency = row.get("frequency", "")

        associated_file = inspection.file
        associated_asset = inspection.asset

        if associated_asset.location_street and associated_asset.location_town:
            name = f"{associated_asset.location_street}, {associated_asset.location_town}"
        else:
            name = ""

        base_values = [
            inspection.legacy_id or "",
            inspection.additional_properties.get("name", name),
            associated_asset.upstream_node or "",
            associated_asset.downstream_node or "",
            inspection.direction or "",
            inspection.structural_grade or "",
            inspection.service_grade or "",
            inspection.date or "",
            inspection.length_surveyed or "",
            associated_asset.height_diameter or "",
            associated_asset.material or "",
            inspection.work_order or "",
            associated_file["filename"] if isinstance(associated_file, dict) else associated_file.filename,
            inspection.general_remarks or "",
            inspection.status or "",
            frequency or "",
            associated_asset.asset_id or "",
        ]

        # Need to create dicts keyed by repair item headers while these are not available in the DB.
        # This should be replaced by a set acquired from the API when available.
        default_repair_item_types = (
            "No Action",
            "Root Removal",
            "Cleaning",
            "Patch",
            "Patch Count",
            "Dig Up",
            "Lining",
        )

        vapar_suggestions = row.get("vaparPlan", {})
        vapar_suggestions_items = dict.fromkeys(default_repair_item_types, "")
        _update_actor_repair_item_values(vapar_suggestions["items"], vapar_suggestions_items)

        valuelist_suggestion = base_values + [
            vapar_suggestions.get("actionSummary", "") or "",
            vapar_suggestions_items.get("No Action", "") or "",
            vapar_suggestions_items.get("Root Removal", "") or "",
            vapar_suggestions_items.get("Cleaning", "") or "",
            vapar_suggestions_items.get("Patch", "") or "",
            vapar_suggestions_items.get("Patch Count", "") or "",
            vapar_suggestions_items.get("Dig Up", "") or "",
            vapar_suggestions_items.get("Lining", "") or "",
            "NA",
            "NA",
            "NA",
            "NA",
            sum([float(item["repairCostEstimate"]) for item in vapar_suggestions["items"]]),
            "AUTOMATED SUGGESTION",
        ]
        valuelist_suggestion[insert_index:insert_index] = ["NA"] * len(custom_repair_header)

        contractor_suggestions = row.get("contractorPlan", {})
        contractor_suggestions_items = dict.fromkeys(default_repair_item_types, "")
        _update_actor_repair_item_values(contractor_suggestions["items"], contractor_suggestions_items)

        valuelist_contractor = base_values + [
            contractor_suggestions.get("actionSummary", "") or "",
            contractor_suggestions_items.get("No Action", "") or "",
            contractor_suggestions_items.get("Root Removal", "") or "",
            contractor_suggestions_items.get("Cleaning", "") or "",
            contractor_suggestions_items.get("Patch", "") or "",
            contractor_suggestions_items.get("Patch Count", "") or "",
            contractor_suggestions_items.get("Dig Up", "") or "",
            contractor_suggestions_items.get("Lining", "") or "",
            row.get("likelihood", "") or "",
            row.get("likelihoodComment", "") or "",
            row.get("consequence", "") or "",
            row.get("consequenceComment", "") or "",
            sum([float(item["repairCostEstimate"]) for item in contractor_suggestions["items"]]),
            "CONTRACTOR RECOMMENDATION",
        ]
        valuelist_contractor[insert_index:insert_index] = contract_values_list

        owner_suggestions = row.get("ownerPlan", {})
        owner_suggestions_items = dict.fromkeys(default_repair_item_types, "")
        _update_actor_repair_item_values(owner_suggestions["items"], owner_suggestions_items)

        valuelist_owner = base_values + [
            owner_suggestions.get("actionSummary", "") or "",
            owner_suggestions_items.get("No Action", "") or "",
            owner_suggestions_items.get("Root Removal", "") or "",
            owner_suggestions_items.get("Cleaning", "") or "",
            owner_suggestions_items.get("Patch", "") or "",
            owner_suggestions_items.get("Patch Count", "") or "",
            owner_suggestions_items.get("Dig Up", "") or "",
            owner_suggestions_items.get("Lining", "") or "",
            row.get("likelihood", "") or "",
            row.get("likelihoodComment", "") or "",
            row.get("consequence", "") or "",
            row.get("consequenceComment", "") or "",
            sum([float(item["repairCostEstimate"]) for item in owner_suggestions["items"]]),
            "DECISION",
        ]
        valuelist_owner[insert_index:insert_index] = owner_values_list

        if not is_asset_owner or not is_uploaded_by_contractor(inspection, is_asset_owner, api_client):
            rows.append(dict(zip(header_list, valuelist_suggestion, strict=True)))
            rows.append(dict(zip(header_list, valuelist_owner, strict=True)))
        else:
            rows.append(dict(zip(header_list, valuelist_suggestion, strict=True)))
            rows.append(dict(zip(header_list, valuelist_owner, strict=True)))
            rows.append(dict(zip(header_list, valuelist_contractor, strict=True)))

    return pd.DataFrame(rows, columns=header_list)


def handler(
    input_data: Input,
    settings: Settings,
    api_client: api.AuthenticatedClient,
    output_fs: AbstractFileSystem,
    max_retry_secs: int = 60,
) -> Output:
    log.info(
        "Starting RepairRecommendationExport activity",
        extra={
            "export_id": input_data.export_id,
            "target_org_id": input_data.target_org_id,
            "instance_id": input_data.instance_id,
        },
    )
    api_client = api_client.with_headers({api.TARGET_ORG_ID_HEADER: str(input_data.target_org_id)})
    try:
        if len(input_data.payload.inspection_ids) == 0:
            raise ExportStatusException(ExportStatusReason.INVALID_INSPECTION_ID)

        ins_ids = input_data.payload.inspection_ids
        mpl_ids: list[int] = []
        for inspection_id in input_data.payload.inspection_ids:
            i = api.with_retries(max_retry_secs)(_fetch_inspection)(inspection_id, False, api_client)
            # use_header_names=False in the above call, so use dict of the response
            i_dict = i.to_dict()
            mpl_ids.append(int(i_dict["id"]))

        if not mpl_ids:
            raise ValueError("None of the provided inspection_ids have an associated MPL object.")

        target_org = api.with_retries(max_retry_secs)(_fetch_organisation_data)(input_data.target_org_id, api_client)
        is_asset_owner = target_org.org_type == "Asset_Owner"

        custom_repair_header = []
        for inspection_id in mpl_ids:
            custom_type = api.with_retries(max_retry_secs)(_fetch_custom_repair_type)(inspection_id, api_client)
            for ct in custom_type:
                # Deduplicate custom repair headers
                if ct not in custom_repair_header:
                    custom_repair_header.append(ct)

        header_list = list(CSV_HEADERS)

        # We always want to insert these just before likelihood assessment
        insert_index = header_list.index("Likelihood Assessment")
        header_list[insert_index:insert_index] = [c.name for c in custom_repair_header]

        rr_df = generate_repair_recommendation_df(
            mpl_ids,
            ins_ids,
            is_asset_owner,
            header_list,
            custom_repair_header,
            insert_index,
            api_client,
            max_retry_secs,
        )

        display_name = build_timestamped_display_name("VS_RepairRecommendations", input_data.payload.format)
        output_file = write_tabular_report(
            rr_df, output_fs, input_data.payload.format, settings.export_outputs_folder_name, display_name
        )

    except ExportStatusException as e:
        return Output(output_file=e.status_reason)

    return Output(output_file=output_file)
