"""
Function Activity that updates the status of an import request.
"""

import logging
from uuid import UUID

from pydantic import BaseModel
from vapar.clients import api
from vapar.constants.imports import ImportStatusEnum, ImportStatusReasonEnum

log = logging.getLogger(__name__)


class Input(BaseModel):
    import_id: UUID
    target_org_id: int
    status: ImportStatusEnum
    status_reason: ImportStatusReasonEnum | None = None
    instance_id: str = ""


def _execute_update_status(
    import_id: UUID, status: ImportStatusEnum, status_reason: ImportStatusReasonEnum, client: api.AuthenticatedClient
):
    body = api.models.PatchedImportRequest(
        status=status,
        status_reason=status_reason,
    )
    res = api.endpoints.imports_partial_update.sync_detailed(id=str(import_id), body=body, client=client)
    api.raise_on_status(res)


def handler(input_dto: Input, api_client: api.AuthenticatedClient, max_retry_secs: int = 60) -> None:
    log.info(
        "Starting UpdateImportStatus activity",
        extra={
            "import_id": input_dto.import_id,
            "status": input_dto.status,
            "status_reason": input_dto.status_reason,
            "target_org_id": input_dto.target_org_id,
            "instance_id": input_dto.instance_id,
        },
    )
    api_client = api_client.with_headers({api.TARGET_ORG_ID_HEADER: str(input_dto.target_org_id)})
    api.with_retries(max_retry_secs)(_execute_update_status)(
        input_dto.import_id, input_dto.status, input_dto.status_reason, api_client
    )
