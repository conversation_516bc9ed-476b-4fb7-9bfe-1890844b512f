import logging
from datetime import datetime
from uuid import UUID

import xmltodict
from fsspec import AbstractFileSystem
from pydantic import BaseModel
from vapar.clients import api
from vapar.constants.conversion import DIRECTION_MAP, StandardValueConverter
from vapar.constants.pipes import Direction<PERSON>num, PipeTypeEnum, StandardEnum

from common.models import ImportDetails, ImportInputFile

log = logging.getLogger(__name__)


class Input(BaseModel):
    import_id: UUID
    target_org_id: int
    instance_id: str
    import_details: ImportDetails


class ParsedFile(BaseModel):
    video_image_storage: str  # VideoImageStorage
    video_image_format: str  # VideoImageFormat
    video_image_filename: str  # VideoImageFilename
    video_name: str = ""  # VRR Observation / Remarks


class ParsedAsset(BaseModel):
    asset_id: str  # PipelineLengthRef
    location_street: str  # LocationStreet
    location_town: str  # LocationTown
    upstream_node: str
    downstream_node: str
    manhole_ref: str  # ManholeRef
    use_of_drain_sewer: str  # UseOfDrainSewer
    height_diameter: int  # HeightDiameter
    material: str  # Material
    year_constructed: str  # YearConstructed
    standard: str  # Standard


class ParsedInspection(BaseModel):
    name_of_surveyor: str  # NameOfSurveyor
    clients_job_ref: str  # ClientsJobRef
    date: str  # Date
    time: str  # Time
    general_remarks: str  # GeneralRemarks
    direction: str  # Direction
    expected_length: float  # ExpectedLength
    client_defined_1: str  # ClientDefined1
    client_defined_2: str  # ClientDefined2
    client_defined_3: str  # ClientDefined3
    client_defined_4: str  # ClientDefined4
    client_defined_5: str  # ClientDefined5
    survey_type: str  # SurveyType (populated in parse_dict_to_models)


class ParsedFrame(BaseModel):
    video_ref: str  # VideoRef
    photograph_refs: list[str]  # PhotographRefs
    distance: float  # Distance
    code: str  # Code
    joint: str  # Joint
    remarks: str  # Remarks


class ParsedDefect(BaseModel):
    continuous_defect: str  # ContinuousDefect
    clock_ref_at_from: int  # ClockRefAtFrom
    clock_ref_to: int  # ClockRefTo
    percentage: float | None = None  # Percentage
    dimension1: float | None = None  # Dimension1
    dimension2: float | None = None  # Dimension2
    band: str | None = None  # Band


class ParsedSurvey(BaseModel):
    file: ParsedFile
    asset: ParsedAsset
    inspection: ParsedInspection
    frames: list[ParsedFrame]
    defects: list[ParsedDefect]


def _get_generic_direction(standard_display_name: str, standard_specific_direction: str) -> DirectionEnum:
    """
    Map the standard-specific direction value to the generic DirectionEnum
    """

    direction_mapping = DIRECTION_MAP.get(StandardEnum(standard_display_name), {})
    for generic, standard_specific in direction_mapping.items():
        if standard_specific == standard_specific_direction:
            return generic
    return DirectionEnum.UNKNOWN


def _fetch_all_defects(
    api_client: api.AuthenticatedClient,
    standard_key_id: int | None = None,
    sub_standard_pipe_type_sewer: bool | None = None,
) -> list[api.models.AllDefects]:
    res = api.endpoints.standards_defects_list.sync_detailed(
        client=api_client,
        standard_key_id=standard_key_id,
        sub_standard_pipe_type_sewer=sub_standard_pipe_type_sewer,
    )
    api.raise_on_status(res)
    return res.parsed


def _create_or_match_asset(
    api_client: api.AuthenticatedClient, request: api.models.AssetMatchOrCreateRequest
) -> api.models.AssetMatchOrCreateResponse:
    res = api.endpoints.assets_match_or_create_create.sync_detailed(client=api_client, body=request)
    api.raise_on_status(res)
    return res.parsed


def make_asset_create_or_match_request(
    standard_id: int, org_id: int, asset: ParsedAsset
) -> api.models.AssetMatchOrCreateRequest:
    request = api.models.AssetMatchOrCreateRequest(
        standard_id=standard_id,
        org_id=org_id,
        asset_id=asset.asset_id,
        upstream_node=asset.upstream_node,
        downstream_node=asset.downstream_node,
        height_diameter=asset.height_diameter,
        material=asset.material,
        location_street=asset.location_street,
        location_town=asset.location_town,
        use_of_drain_sewer=asset.use_of_drain_sewer,
    )
    return request


def _fetch_standards_list(api_client: api.AuthenticatedClient):
    res = api.endpoints.standards_list.sync_detailed(client=api_client)
    api.raise_on_status(res)
    return res.parsed


def get_standard_by_name(api_client: api.AuthenticatedClient, display_name: str) -> api.models.Standard | None:
    standards_list = _fetch_standards_list(api_client).results
    for standard in standards_list:
        if standard.additional_properties.get("display_name") == display_name:
            return standard
    return None


def parse_defects(observation_data: list[dict]) -> list[ParsedDefect]:
    defects = []
    for observation in observation_data:
        # Manhole observations add the node IDs - so these need to be ignored.
        try:
            dimension1 = float(observation.get("Dimension1"))
        except (TypeError, ValueError):
            dimension1 = None
        try:
            dimension2 = float(observation.get("Dimension2"))
        except (TypeError, ValueError):
            dimension2 = None

        defect = ParsedDefect(
            continuous_defect=observation.get("ContinuousDefect") or "",
            clock_ref_at_from=observation.get("ClockRefAtFrom") or 0,
            clock_ref_to=observation.get("ClockRefTo") or 0,
            percentage=observation.get("Percentage"),
            dimension1=dimension1,
            dimension2=dimension2,
            band=observation.get("Band"),
        )
        defects.append(defect)
    return defects


def parse_frames(observation_data: list[dict]) -> list[ParsedFrame]:
    frames = []
    for observation in observation_data:
        photograph_refs = [""]
        if "PhotographRefs" in observation:
            photograph_refs = observation.get("PhotographRefs").get("PhotographRef") or [""]

        frame = ParsedFrame(
            video_ref=observation.get("VideoRef"),
            photograph_refs=photograph_refs,
            distance=observation.get("Distance"),
            code=observation.get("Code"),
            joint=observation.get("Joint") or "",
            remarks=observation.get("Remarks") or "",
        )
        frames.append(frame)
    return frames


def parse_inspection(header_data: dict) -> ParsedInspection:
    inspection = ParsedInspection(
        name_of_surveyor=header_data.get("NameOfSurveyor") or "",
        clients_job_ref=header_data.get("ClientsJobRef") or "",
        date=header_data.get("Date"),
        time=header_data.get("Time") or "",
        general_remarks=header_data.get("GeneralRemarks") or "",
        direction=header_data.get("Direction") or "",
        expected_length=header_data.get("ExpectedLength") or 0.0,
        client_defined_1=header_data.get("ClientDefined1") or "",
        client_defined_2=header_data.get("ClientDefined2") or "",
        client_defined_3=header_data.get("ClientDefined3") or "",
        client_defined_4=header_data.get("ClientDefined4") or "",
        client_defined_5=header_data.get("ClientDefined5") or "",
        survey_type=header_data.get("SurveyType") or "",
    )
    return inspection


def parse_asset(header_data: dict) -> ParsedAsset:
    upstream_node = ""
    downstream_node = ""
    # Hardcoded for now as header_data["Standard"] is the standard code, not the display name - we will need a mapping
    # in vaparlib
    standard_display_name = "UK V5"
    if header_data.get("survey_type") == "Pipe" or not header_data.get("survey_type"):
        direction = _get_generic_direction(standard_display_name, header_data.get("Direction"))
        if direction == DirectionEnum.DOWNSTREAM:
            upstream_node = header_data.get("StartNodeRef") or ""
            downstream_node = header_data.get("FinishNodeRef") or ""
        else:
            upstream_node = header_data.get("FinishNodeRef") or ""
            downstream_node = header_data.get("StartNodeRef") or ""

    # Convert UseOfDrainSewer from standard values to VAPAR values
    use_of_drain_sewer = header_data.get("UseOfDrainSewer") or ""
    converter = StandardValueConverter(header="UseOfDrainSewer", standard=standard_display_name)
    use_of_drain_sewer: PipeTypeEnum | str = converter.get_common_value(use_of_drain_sewer)

    # Get the enum value if converted, otherwise set default.
    try:
        use_of_drain_sewer = use_of_drain_sewer.value
    except AttributeError:
        use_of_drain_sewer = PipeTypeEnum.SEWER.value

    asset = ParsedAsset(
        asset_id=header_data.get("PipelineLengthRef", ""),
        location_street=header_data.get("LocationStreet", ""),
        location_town=header_data.get("LocationTown", ""),
        # start_node_ref=header_data.get("StartNodeRef") if header_data.get("survey_type") == "Pipe" else "",
        # finish_node_ref=header_data.get("FinishNodeRef") if header_data.get("survey_type") == "Pipe" else "",
        upstream_node=upstream_node,
        downstream_node=downstream_node,
        manhole_ref=header_data.get("ManholeRef") if header_data.get("survey_type") == "Manhole" else "",
        use_of_drain_sewer=use_of_drain_sewer,
        height_diameter=header_data.get("HeightDiameter") or 0,
        material=header_data.get("Material") or "",
        year_constructed=header_data.get("YearConstructed") or "",
        standard=standard_display_name,
    )
    return asset


def parse_file(header_data: dict, vrr_observation_data: dict) -> ParsedFile:
    video_name = vrr_observation_data.get("Remarks", "") if vrr_observation_data.get("Code") == "VVR" else ""
    file = ParsedFile(
        video_name=video_name,
        video_image_storage=header_data.get("VideoImageStorage") or "",
        video_image_format=header_data.get("VideoImageFormat") or "",
        video_image_filename=header_data.get("VideoImageFilename") or "",
    )
    return file


def parse_dict_to_models(data: dict) -> list[ParsedSurvey]:
    parsed_data = []
    combined_surveys = []
    if "Survey" in data["SurveyGroup"]:
        for survey in data["SurveyGroup"]["Survey"]:
            survey["Header"]["SurveyType"] = "Pipe"
            combined_surveys.append(survey)

    for survey in combined_surveys:
        header_data = survey["Header"]
        observation_data = survey["Observations"]["Observation"]
        print(observation_data[0])
        file = parse_file(header_data, observation_data[0])
        asset = parse_asset(header_data)
        inspection = parse_inspection(header_data)
        frames = parse_frames(observation_data)
        defects = parse_defects(observation_data)

        parsed_survey = ParsedSurvey(
            file=file,
            asset=asset,
            inspection=inspection,
            frames=frames,
            defects=defects,
        )
        parsed_data.append(parsed_survey)

    return parsed_data


def _fetch_import_file(import_id: UUID, api_client: api.AuthenticatedClient) -> ImportInputFile | None:
    res = api.endpoints.imports_files_list.sync_detailed(import_id=str(import_id), client=api_client)
    api.raise_on_status(res)
    if not res.parsed.results:
        return None
    input_file = ImportInputFile(
        file_path=res.parsed.results[0].blob_url,
        mime_type=res.parsed.results[0].mime_type,
        extension=res.parsed.results[0].extension,
        size=res.parsed.results[0].file_size,
    )
    return input_file


def _create_video_file_record(video_name: str, folder_id: int, client: api.AuthenticatedClient) -> int:
    res = api.endpoints.files_create.sync_detailed(
        client=client,
        body=api.models.FileCreateRequest(
            job_tree=folder_id,
            filename=video_name,
        ),
    )
    api.raise_on_status(res)
    return res.parsed.id


def _create_inspection(
    asset_uuid: str,
    standard_display_name: str,
    file_id: int,
    folder_id: int,
    inspection: ParsedInspection,
    client: api.AuthenticatedClient,
) -> UUID:
    date_surveyed = datetime.strptime(inspection.date, "%Y-%m-%d").date()
    direction = _get_generic_direction(standard_display_name, inspection.direction)
    res = api.endpoints.inspections2_create.sync_detailed(
        asset_uuid=str(asset_uuid),
        folder_id=folder_id,
        file_id=file_id,
        body=api.models.InspectionCreateModel(
            direction=api.models.DirectionEnum(direction.value),
            length_surveyed=inspection.expected_length,
            date=date_surveyed,
            general_remarks=inspection.general_remarks,
        ),
        client=client,
    )
    api.raise_on_status(res)
    return UUID(res.parsed.uuid)


def _match_defect(
    defect: ParsedDefect,
    substandard_defects: list[api.models.AllDefects],
    quantity_1_value: float | None,
    code: str,
    joint: str,
) -> tuple[int, int, str | None, str | None] | None:
    """
    Return a defect model id, defect score id, and the quantity units for a matched defect if found
    """
    defect_filters = [
        (code, "defect_code"),
        (joint, "at_joint_required"),
        (defect.percentage is not None, "percentage_required"),
    ]

    defects_matched = substandard_defects
    for value, key in defect_filters:
        new_matches = []
        for d in defects_matched:
            # at_joint_required is actually treated as at_joint_allowed in the backend
            if key == "at_joint_required":
                if d.at_joint_required is False and joint:
                    continue
                new_matches.append(d)
            # We always want to match the defect code
            elif value == code:
                if hasattr(d, key) and getattr(d, key) == value:
                    new_matches.append(d)
            # But if we're missing percentage data, we might still be able to match
            else:
                if not hasattr(d, key) or getattr(d, key) == value:
                    new_matches.append(d)
        defects_matched = new_matches

        if len(defects_matched) == 1:
            return (
                defects_matched[0].defect_model_id,
                defects_matched[0].id,
                defects_matched[0].quantity_1_units,
                defects_matched[0].quantity_2_units,
            )
        elif len(defects_matched) == 0:
            return (120, 2020, None, None)  # General photograph

    # We don't currently have an endpoint that returns the start and end val for DefectScore quantities
    # So instead we look for which has the closest default percentage
    if quantity_1_value:
        quantity_1_value = int(quantity_1_value)
        defects_with_default_vals = [d for d in defects_matched if d.quantity_1_default_val is not None]
        closest = min(
            defects_with_default_vals,
            key=lambda d: (
                abs(int(d.quantity_1_default_val) - quantity_1_value),
                -int(d.quantity_1_default_val),  # For tie-breaker (higher value wins)
            ),
        )
        return (closest.defect_model_id, closest.id, closest.quantity_1_units, closest.quantity_2_units)
    else:
        # We still have >1 potential matches, so we check for duplicates
        dedup_by_fields = [
            "defect_type",
            "quantity_1_default_val",
            "quantity_1_units",
            "quantity_2_default_val",
            "quantity_2_units",
            "material_applied",
            "characterisation_1",
            "characterisation_2",
            "clock_position_required",
            "clock_spread_possible",
            "start_survey",
            "end_survey",
            "repair_category",
            "fastpass_code",
            "continuous_score",
        ]

        defects_matched = list(
            {
                tuple(getattr(d, field, None) for field in dedup_by_fields): d
                for d in sorted(defects_matched, key=lambda d: d.id)
            }.values()
        )

        if len(defects_matched) == 1:
            return (
                defects_matched[0].defect_model_id,
                defects_matched[0].id,
                defects_matched[0].quantity_1_units,
                defects_matched[0].quantity_2_units,
            )

    return (120, 2020, None, None)  # General photograph


def _bulk_create_frames_for_video(
    file_id: int,
    frames: list[ParsedFrame],
    defects: list[ParsedDefect],
    substandard_defects: list[api.models.AllDefects],
    client: api.AuthenticatedClient,
):
    frame_payloads = []
    for i, (frame, defect) in enumerate(zip(frames, defects, strict=False)):
        # Sets quantity values based on dimension and percentage data.
        quantity_1_value = defect.dimension1
        quantity_2_value = defect.dimension2

        if quantity_1_value is None and defect.percentage:
            quantity_1_value = defect.percentage
        elif quantity_2_value is None and defect.percentage:
            quantity_2_value = defect.percentage

        matched_details = _match_defect(defect, substandard_defects, quantity_1_value, frame.code, frame.joint)
        defect_model_id, defect_score_id, defect_quantity_1_units, defect_quantity_2_units = (
            matched_details if matched_details else (None, None, None, None)
        )

        payload = api.models.VideoFrameCreateRequest(
            parent_video=file_id,
            frame_id=i + 1,
            chainage=str(frame.distance),
            chainage_number=str(frame.distance),
            defect_model=defect_model_id,
            defect_scores=defect_score_id,
            image_location="",  # Not yet uploaded
            class_certainty=str(100),
            time_reference=frame.video_ref,
            quantity_1_value=quantity_1_value,
            quantity_1_units=defect_quantity_1_units,
            quantity_2_value=quantity_2_value,
            quantity_2_units=defect_quantity_2_units,
            remarks=frame.remarks,
            # TODO: Handle continuous defects
        )
        frame_payloads.append(payload)

    res = api.endpoints.inspections_videos_frames_create.sync_detailed(
        video_id=file_id, body=frame_payloads, client=client
    )
    api.raise_on_status(res)


def _link_inspections_to_import(inspection_ids: list[UUID], import_id: UUID, client: api.AuthenticatedClient):
    body = api.models.ImportedInspectionCreateRequest(
        inspection_uuids=[str(insp) for insp in inspection_ids],
    )
    res = api.endpoints.imports_inspections_create.sync_detailed(
        import_id=str(import_id),
        body=body,
        client=client,
    )
    api.raise_on_status(res)


def _set_total_frame_count(file_id: int, total_frames: int, client: api.AuthenticatedClient):
    res = api.endpoints.files_partial_update.sync_detailed(
        id=file_id,
        client=client,
        body=api.models.PatchedInspectionFilePatchRequest(
            total_frames=total_frames,
        ),
    )
    api.raise_on_status(res)


def handler(
    input_data: Input,
    api_client: api.AuthenticatedClient,
    input_fs: AbstractFileSystem,
    max_retry_secs: int = 60,
):
    log.info(
        "Starting AssetImport activity",
        extra={
            "import_id": input_data.import_id,
            "target_org_id": input_data.target_org_id,
            "instance_id": input_data.instance_id,
        },
    )
    api_client = api_client.with_headers({api.TARGET_ORG_ID_HEADER: str(input_data.target_org_id)})

    payload = input_data.import_details.payload.root.root
    folder_id = payload.destination_folder_id

    input_file: ImportInputFile | None = api.with_retries(max_retry_secs)(_fetch_import_file)(
        input_data.import_id, api_client
    )
    if not input_file:
        raise ValueError("No input file found for import")  # TODO: Better error

    log.info(
        "Processing input file",
        extra={
            "file_path": input_file.file_path,
            "size": input_file.size,
            "mime_type": input_file.mime_type,
            "extension": input_file.extension,
        },
    )

    with input_fs.open(input_file.file_path, "r") as f:
        data = xmltodict.parse(f.read(), force_list={"Survey", "NodeSurvey", "Observation", "PhotographRef"})

    parsed_data = parse_dict_to_models(data)
    inspection_ids = []
    file_record_ids = []
    asset_ids = []
    for survey in parsed_data:
        standard = get_standard_by_name(api_client, survey.asset.standard)
        request = make_asset_create_or_match_request(
            standard_id=standard.id, org_id=input_data.target_org_id, asset=survey.asset
        )
        asset_result = api.with_retries(max_retry_secs)(_create_or_match_asset)(api_client, request)
        asset = asset_result.asset
        asset_ids.append(asset.uuid)

        file_record_id = api.with_retries(max_retry_secs)(_create_video_file_record)(
            survey.file.video_name, folder_id, api_client
        )
        file_record_ids.append(file_record_id)

        inspection_uuid = api.with_retries(max_retry_secs)(_create_inspection)(
            asset_uuid=asset.uuid,
            standard_display_name=survey.asset.standard,
            file_id=file_record_id,
            folder_id=folder_id,
            inspection=survey.inspection,
            client=api_client,
        )

        substandard_defects = api.with_retries(max_retry_secs)(_fetch_all_defects)(
            api_client=api_client,
            standard_key_id=standard.id,
            sub_standard_pipe_type_sewer=asset.use_of_drain_sewer == "SS",
        )

        api.with_retries(max_retry_secs)(_bulk_create_frames_for_video)(
            file_id=file_record_id,
            frames=survey.frames,
            defects=survey.defects,
            substandard_defects=substandard_defects,
            client=api_client,
        )

        n_frames = len(survey.frames)
        api.with_retries(max_retry_secs)(_set_total_frame_count)(
            file_id=file_record_id, total_frames=n_frames, client=api_client
        )

        log.info("Created inspection: %s", inspection_uuid)
        inspection_ids.append(inspection_uuid)

    api.with_retries(max_retry_secs)(_link_inspections_to_import)(
        inspection_ids=inspection_ids, import_id=input_data.import_id, client=api_client
    )
