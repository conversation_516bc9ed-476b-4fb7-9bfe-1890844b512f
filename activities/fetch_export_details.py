"""
Function Activity that fetches the basic details of an export
"""

import logging
from uuid import UUID

from pydantic import BaseModel
from vapar.clients import api

from common.models import ExportDetails

log = logging.getLogger(__name__)


class Input(BaseModel):
    export_id: UUID
    target_org_id: int
    instance_id: str = ""


class Output(BaseModel):
    details: ExportDetails | None


async def _fetch_export_details(
    input_data: Input, api_client: api.AuthenticatedClient, max_retry_secs: int = 60
) -> ExportDetails | None:
    res = await api.endpoints.exports_retrieve.asyncio_detailed(uuid=str(input_data.export_id), client=api_client)
    if res.status_code == 404:
        log.info("Export not found", extra={"export_id": input_data.export_id})
        return None
    api.raise_on_status(res)

    return ExportDetails(
        status=res.parsed.status.value,
        status_reason=res.parsed.status_reason.value if res.parsed.status_reason else None,
        type=res.parsed.type.value,
        format=res.parsed.format_.value,
        created_at=res.parsed.created_at,
        updated_at=res.parsed.updated_at,
        completed_at=res.parsed.completed_at or None,
    )


async def handler(input_data: Input, api_client: api.AuthenticatedClient, max_retry_secs: int = 60) -> Output:
    log.info(
        "Starting FetchExportDetails activity",
        extra={
            "export_id": input_data.export_id,
            "target_org_id": input_data.target_org_id,
            "instance_id": input_data.instance_id,
        },
    )
    api_client = api_client.with_headers({api.TARGET_ORG_ID_HEADER: str(input_data.target_org_id)})
    details = await api.with_retries(max_retry_secs)(_fetch_export_details)(input_data, api_client, max_retry_secs)

    return Output(details=details)
