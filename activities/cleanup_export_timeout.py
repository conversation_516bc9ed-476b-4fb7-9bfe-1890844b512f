"""
A function that moves exports to FAILED state if they have been in PENDING or PROCESSING state over a certain duration.
"""

import logging
from datetime import datetime, timedelta, timezone

from pydantic_settings import BaseSettings
from vapar.clients import api
from vapar.constants.exports import ExportStatus, ExportStatusReason

log = logging.getLogger(__name__)


class Settings(BaseSettings):
    # Timeouts since the entity was last updated
    export_pending_timeout_minutes: int = 20
    export_processing_timeout_minutes: int = 20


async def handler(api_client: api.AuthenticatedClient, settings: Settings):
    log.info(
        "Starting scheduled export_cleanup_timeout_timer_trigger",
        extra={
            "pending_timeout_mins": settings.export_pending_timeout_minutes,
            "processing_timeout_mins": settings.export_processing_timeout_minutes,
        },
    )

    now = datetime.now(tz=timezone.utc)

    request_body = api.models.PatchedExportBulkUpdateStatusRequest(
        status=api.models.ExportStatusEnum(ExportStatus.FAILED.value),
        status_reason=api.models.ExportStatusReasonEnum(ExportStatusReason.TIMEOUT.value),
    )

    pending_expiration_cutoff = now - timedelta(minutes=settings.export_pending_timeout_minutes)
    log.info("Updating exports in PENDING state", extra={"cutoff": pending_expiration_cutoff})
    res = await api.endpoints.exports_status_partial_update.asyncio_detailed(
        client=api_client,
        updated_at_lte=pending_expiration_cutoff,
        status=ExportStatus.PENDING.value,
        body=request_body,
    )
    api.raise_on_status(res)

    processing_expiration_cutoff = now - timedelta(minutes=settings.export_processing_timeout_minutes)
    log.info("Updating exports in PROCESSING state", extra={"cutoff": processing_expiration_cutoff})
    res = await api.endpoints.exports_status_partial_update.asyncio_detailed(
        client=api_client,
        updated_at_lte=processing_expiration_cutoff,
        status=ExportStatus.PROCESSING.value,
        body=request_body,
    )
    api.raise_on_status(res)
