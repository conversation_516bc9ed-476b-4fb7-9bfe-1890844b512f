"""
Function Activity that sends the details of created export output files to the API.
"""

import logging
from uuid import UUID

from pydantic import BaseModel
from vapar.clients import api

from common.models import ExportOutputFile

log = logging.getLogger(__name__)


class Input(BaseModel):
    export_id: UUID
    target_org_id: int
    files: list[ExportOutputFile]
    instance_id: str = ""


def _execute_create_export_output(export_id: UUID, file: ExportOutputFile, client: api.AuthenticatedClient):
    filename = file.file_path.rsplit("/", 1)[-1]
    body = api.models.ExportOutputRequest(
        filename=filename,
        blob_url=file.file_path,
        file_size=file.size,
        mime_type=file.mime_type,
        extension=file.extension,
        export=str(export_id),
        file_display_name=file.file_display_name,
    )
    res = api.endpoints.exports_outputs_create.sync_detailed(uuid=str(export_id), body=body, client=client)
    api.raise_on_status(res)


def handler(input_dto: Input, api_client: api.AuthenticatedClient, max_retry_secs: int = 60) -> None:
    log.info(
        "Starting SendOutputDetails activity ",
        extra={
            "export_id": input_dto.export_id,
            "target_org_id": input_dto.target_org_id,
            "n_files": len(input_dto.files),
            "instance_id": input_dto.instance_id,
        },
    )

    api_client = api_client.with_headers({api.TARGET_ORG_ID_HEADER: str(input_dto.target_org_id)})
    for file_details in input_dto.files:
        api.with_retries(max_retry_secs)(_execute_create_export_output)(input_dto.export_id, file_details, api_client)
