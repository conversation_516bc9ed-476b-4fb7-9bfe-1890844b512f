import asyncio
import logging
from io import BytesIO
from uuid import UUID

import numpy as np
import pandas as pd
from fsspec import AbstractFileSystem
from pydantic import BaseModel
from vapar.clients import api
from vapar.constants.conversion import USE_OF_DRAIN_SEWER_MAP
from vapar.constants.pipes import PipeTypeEnum, StandardEnum

from common.models import ImportInputFile
from common.standards import APIStandardProvider, StandardCache

log = logging.getLogger(__name__)

API_REQUEST_LIMIT = 20


class Input(BaseModel):
    import_id: UUID
    target_org_id: int
    instance_id: str = ""


async def _create_asset(api_client: api.AuthenticatedClient, row: dict):
    body = api.models.AssetMatchOrCreateRequest(
        asset_id=row["AssetID"],
        standard_id=row["standardID"],
        use_of_drain_sewer=row["UseOfDrainSewer"],
        upstream_node=row["UpstreamNode"],
        downstream_node=row["DownstreamNode"],
        height_diameter=row["HeightDiameter"],
        material=row["Material"],
        location_street=row["LocationStreet"],
        location_town=row["LocationTown"],
        pipe_unit_length=row["PipeUnitLength"],
        org_id=row["orgID"],
    )

    # Perform a PUT request on Asset
    res = await api.endpoints.assets_update.asyncio_detailed(client=api_client, body=body)
    api.raise_on_status(res)
    return res.parsed


async def _get_generic_pipe_type(standard: api.models.Standard, standard_specific_pipe_type: str | None):
    """
    Map the standard-specific pipe type value to the generic PipeTypeEnum
    """
    pipe_type_mapping = USE_OF_DRAIN_SEWER_MAP.get(StandardEnum(standard.additional_properties["display_name"]), {})
    generic_pipe_type = next(
        (
            generic
            for generic, standard_specific in pipe_type_mapping.items()
            if standard_specific == standard_specific_pipe_type
        ),
        PipeTypeEnum.SEWER,
    )
    return generic_pipe_type


async def prepare_dict(row: dict, standard: api.models.Standard, org_id: int):
    standard_specific_pipe_type = row.get("UseOfDrainSewer")
    generic_pipe_type = await _get_generic_pipe_type(standard, standard_specific_pipe_type)

    asset_dict = {
        "AssetID": row["AssetID"],
        "standardID": standard.id,
        "UseOfDrainSewer": generic_pipe_type.value,
        "UpstreamNode": row.get("UpstreamNode") or None,
        "DownstreamNode": row.get("DownstreamNode") or None,
        "HeightDiameter": row.get("HeightDiameter") or None,
        "Material": row.get("Material") or None,
        "LocationStreet": row.get("LocationStreet") or None,
        "LocationTown": row.get("LocationTown") or None,
        "PipeUnitLength": row.get("PipeUnitLength") or None,
        "orgID": org_id,
    }
    return asset_dict


async def _fetch_import_file(import_id: UUID, api_client: api.AuthenticatedClient) -> ImportInputFile | None:
    res = await api.endpoints.imports_files_list.asyncio_detailed(import_id=str(import_id), client=api_client)
    api.raise_on_status(res)

    if not res.parsed.results:
        return None

    input_file = ImportInputFile(
        file_path=res.parsed.results[0].blob_url,
        mime_type=res.parsed.results[0].mime_type,
        extension=res.parsed.results[0].extension,
        size=res.parsed.results[0].file_size,
    )
    return input_file


async def _fetch_org_details_by_id(org_id: int, api_client: api.AuthenticatedClient) -> api.models.EditOrganisation:
    res = await api.endpoints.organisations_retrieve.asyncio_detailed(id=org_id, client=api_client)
    api.raise_on_status(res)
    return res.parsed


async def process_row(
    row: pd.Series,
    api_client: api.AuthenticatedClient,
    index: int,
    std_provider: APIStandardProvider,
    fallback_standard_name: str,
    target_org_id: int,
):
    row_dict = row.to_dict()
    standard_display_name = row.get("StandardName") or fallback_standard_name

    # APIStandardProvider.get_by_display_name is synchronous, run it in a thread pool
    loop = asyncio.get_running_loop()
    standard = await loop.run_in_executor(None, std_provider.get_by_display_name, standard_display_name)

    prepared_dict = await prepare_dict(row_dict, standard, target_org_id)
    created_asset = await _create_asset(api_client, prepared_dict)

    log.info("Successfully created asset from CSV row %i", index + 1)
    return created_asset


async def process_row_with_semaphore(
    sem: asyncio.Semaphore,
    row: pd.Series,
    api_client: api.AuthenticatedClient,
    index: int,
    std_provider: APIStandardProvider,
    fallback_standard_name: str,
    target_org_id: int,
):
    """Limits concurrent execution to the semaphore value which avoids overwhelming API with requests."""
    async with sem:
        return await process_row(row, api_client, index, std_provider, fallback_standard_name, target_org_id)


async def process_dataframe(
    df: pd.DataFrame,
    api_client: api.AuthenticatedClient,
    std_provider: APIStandardProvider,
    fallback_standard_name: str,
    target_org_id: int,
):
    sem = asyncio.Semaphore(API_REQUEST_LIMIT)
    # Create tasks for each row
    tasks = [
        process_row_with_semaphore(sem, row, api_client, i, std_provider, fallback_standard_name, target_org_id)
        for i, row in df.iterrows()
    ]

    # Run create asset tasks concurrently
    created_assets = await asyncio.gather(*tasks, return_exceptions=True)

    # Handle any exceptions
    for i, result in enumerate(created_assets):
        if isinstance(result, Exception):
            log.error("Error processing row %i: %s", i + 1, str(result))

    return created_assets


async def handler(
    input_data: Input,
    api_client: api.AuthenticatedClient,
    input_fs: AbstractFileSystem,
    std_cache: StandardCache | None = None,
    max_retry_secs: int = 60,
):
    log.info(
        "Starting AssetImport activity",
        extra={
            "import_id": input_data.import_id,
            "target_org_id": input_data.target_org_id,
            "instance_id": input_data.instance_id,
        },
    )
    api_client = api_client.with_headers({api.TARGET_ORG_ID_HEADER: str(input_data.target_org_id)})

    org_details = await _fetch_org_details_by_id(input_data.target_org_id, api_client)
    fallback_standard_name = org_details.standard_display_name

    input_file = await api.with_retries(max_retry_secs)(_fetch_import_file)(input_data.import_id, api_client)
    if not input_file:
        raise ValueError("No input file found for import")  # TODO: Better error handling

    log.info(
        "Processing input file",
        extra={
            "file_path": input_file.file_path,
            "size": input_file.size,
            "mime_type": input_file.mime_type,
            "extension": input_file.extension,
        },
    )

    # Directly read file content and avoids any sync calls.
    # NOTE: input_fs.open() calls .info() internally which is sync
    # which will cause the operation to fail. input_fs.cat() also does the same thing.
    blob_data = await input_fs._cat_file(input_file.file_path, start=0, end=None)
    df = pd.read_csv(BytesIO(blob_data))

    std_provider = APIStandardProvider(api_client, cache=std_cache)
    df = df.fillna(np.nan).replace([np.nan], [None])

    created_assets = await process_dataframe(
        df, api_client, std_provider, fallback_standard_name, target_org_id=input_data.target_org_id
    )
    return created_assets
