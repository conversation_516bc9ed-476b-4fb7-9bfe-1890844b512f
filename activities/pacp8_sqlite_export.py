import datetime
import re
import shutil
import sqlite3
import sys
import tempfile
import uuid
from collections import defaultdict
from collections.abc import Sequence
from pathlib import Path
from typing import NamedTuple

import pandas as pd
from fsspec import AbstractFileSystem
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings
from vapar.clients import api, nassco_validator
from vapar.clients.nassco_validator import (
    NASSCOValidationErrorItem,
    NASSCOValidatorClient,
    NASSCOValidatorPayload,
    PACPRating,
)
from vapar.constants.exports import ExportStatusReason
from vapar.core.exports import PACP8SQLiteExportPayload

from common.constants import STATIC_RESOURCES_DIR
from common.inspections import fetch_inspections_with_frames
from common.models import ExportOutputFile, ExportStatusException
from common.reports import build_org_name_timestamped_display_name
from static.data.pacp_constants import FALLBACK_MATERIAL_CODE, MATERIAL_NAME_TO_CODE

_DB_TEMPLATE_PATH = STATIC_RESOURCES_DIR / "SQLite" / "pacp8_export_template.sqlite3"

_COPIED_DB_FILENAME = "pacp8_export.sqlite3"


def _get_validator_path() -> str:
    bin_dir = STATIC_RESOURCES_DIR / "bin"
    match sys.platform:
        case "darwin":
            return str(bin_dir / "NasscoConsoleValidator-1.8.1-osx-x64")
        case "linux":
            return str(bin_dir / "NasscoConsoleValidator-1.8.1-linux-x64")
        case _:
            raise ValueError(f"Unsupported platform: {sys.platform}")


class Input(BaseModel):
    export_id: uuid.UUID
    target_org_id: int
    payload: PACP8SQLiteExportPayload
    instance_id: str = ""


class Output(BaseModel):
    output_file: ExportOutputFile | ExportStatusReason


class Settings(BaseSettings):
    export_outputs_folder_name: str = "export-outputs"
    nassco_cli_path: str = Field(default_factory=_get_validator_path)


class InspectionData(NamedTuple):
    inspection: api.models.InspectionModel
    frames: list[api.models.FramesList]
    upload_org_name: str | None = None
    target_org_name: str | None = None


def _fetch_org_by_id(id: int, api_client: api.AuthenticatedClient) -> api.models.EditOrganisation:
    res = api.endpoints.organisations_retrieve.sync_detailed(id=id, client=api_client)
    api.raise_on_status(res)
    return res.parsed


def _collect_inspection_data(
    org_details: api.models.EditOrganisation,
    inspection_ids: Sequence[uuid.UUID],
    api_client: api.AuthenticatedClient,
    max_retry_secs: int,
) -> list[InspectionData]:
    orgs_by_id: dict[int, api.models.BaseOrganisation] = {org_details.id: org_details}
    orgs_by_id.update({linked.id: linked for linked in org_details.linked_organisations})

    inspections_with_frames = fetch_inspections_with_frames(
        api_client,
        inspection_ids,
        max_retry_secs=max_retry_secs,
    )

    inspections_data = []
    for pair in inspections_with_frames:
        upload_org = orgs_by_id.get(pair.inspection.file.get("upload_org") or None)
        owner_org = orgs_by_id.get(pair.inspection.file.get("target_org") or None)

        data = InspectionData(
            inspection=pair.inspection,
            frames=pair.frames,
            upload_org_name=upload_org.full_name if upload_org else None,
            target_org_name=owner_org.full_name if owner_org else None,
        )
        inspections_data.append(data)

    return inspections_data


def _format_date(val: str | datetime.datetime | None) -> str:
    """Format as YYYYMMDD"""
    if val is None or val == "":
        return "20000101"
    if isinstance(val, datetime.datetime):
        return val.strftime("%Y%m%d")
    return val


def _format_time(val: str | None) -> str:
    """Format as HHMM 24 hour"""
    if val is None:
        return "0000"
    return val.replace(":", "")  # In case it was formatted as HH:MM


def _format_direction(val) -> str:
    if val == "Downstream":
        return "D"
    return "U"


def _format_uuid(val: str) -> str:
    """Brace-enclosed UUID format used by NASSCO"""
    return "{" + val + "}"


def _new_uuid() -> str:
    return "{" + str(uuid.uuid4()) + "}"


def _get_method_of_inspection(field_value: str | None) -> str:
    if not field_value:
        return "CC"  # Default to CCTV
    if field_value in ("CC", "LA", "SO", "SS", "ZM", "ZZ"):
        return field_value
    return "ZZ"  # Other


def _format_chainage(val: str | None, is_imperial: bool) -> str:
    """Feet are expected to 1dp, metres to 2dp"""
    if not val:
        val = "0"
    try:
        as_float = float(val)
    except ValueError:
        as_float = 0.0

    if is_imperial:
        return f"{as_float:.1f}"
    return f"{as_float:.2f}"


def _get_continuous_start_end(frame: api.models.FramesList, frame_idx: int) -> tuple[str, str]:
    if not frame.cont_defect_start:
        return "", ""

    start_continuous_defect_str = "S" + str(frame_idx + 1).zfill(2)
    end_continuous_defect_str = "F" + str(frame_idx + 1).zfill(2)

    return start_continuous_defect_str, end_continuous_defect_str


def _time_ref_to_seconds(time_ref: str) -> float | None:
    """Parse a time reference string like HH:MM:SS to seconds, or return None if unparseable"""
    if not time_ref:
        return None
    try:
        parts = time_ref.split(":")
        secs = 0.0
        for i, part in enumerate(reversed(parts)):
            secs += float(part) * 60**i
    except ValueError:
        return None

    return secs


def _time_ref_to_formatted_seconds(time_ref: str) -> str | None:
    """Format to 3dp"""
    secs = _time_ref_to_seconds(time_ref)
    if secs is None:
        return None
    return f"{secs:.3f}"


def _time_ref_to_vcr_time(time_ref: str) -> str | None:
    """Format as HHMMSS"""
    secs = _time_ref_to_seconds(time_ref)

    if secs is None:
        return None

    total_secs = int(secs)
    hours = total_secs // 3600
    minutes = (total_secs % 3600) // 60
    seconds = total_secs % 60

    return f"{hours:02d}{minutes:02d}{seconds:02d}"


def _get_material_code(material: str) -> str:
    return MATERIAL_NAME_TO_CODE.get(material.lower().replace(" ", ""), FALLBACK_MATERIAL_CODE)


def _get_frame_values(frame: api.models.FramesList) -> tuple[str, str, str]:
    value_1st = frame.quantity_1_value if frame.quantity_1_value else "0"
    value_percent = "0"
    if frame.quantity_1_units == "%":
        value_1st, value_percent = value_percent, value_1st
    value_2nd = frame.quantity_2_value if frame.quantity_2_value else "0"

    return value_1st, value_2nd, value_percent


def map_inspection_to_validator_input(
    inspections: list[InspectionData],
) -> NASSCOValidatorPayload:
    """
    Construct a minimal payload for running the NASSCO Validator CLI on the given inspections.
    """

    pacp_inspections = [nassco_validator.PACPInspection(inspection_id=insp.inspection.uuid) for insp in inspections]

    pacp_ratings = [
        nassco_validator.PACPRating(
            inspection_id=str(insp.inspection.uuid),
            rating_id=str(uuid.uuid4()),  # The validator expects an uuid for each rating
        )
        for insp in inspections
    ]

    pacp_conditions = []
    for insp in inspections:
        frame_conditions = []
        frames_to_add = (f for f in insp.frames if f.defect_code)
        for frame_idx, frame in enumerate(frames_to_add):
            value_1st, value_2nd, value_percent = _get_frame_values(frame)
            continuous_start, continuous_end = _get_continuous_start_end(frame, frame_idx)

            cond = nassco_validator.PACPCondition(
                inspection_id=insp.inspection.uuid,
                condition_id=str(uuid.uuid4()),
                distance=frame.chainage_number or "0",
                pacp_code=frame.defect_code,
                value_1st_dimension=value_1st,
                value_2nd_dimension=value_2nd,
                value_percent=value_percent,
                clock_at_from=frame.at_clock or None,
                clock_to=frame.to_clock or None,
                joint=int(bool(frame.at_joint)),
                remarks=frame.remarks or None,
                continuous=continuous_start,
            )
            frame_conditions.append(cond)

            if continuous_end:
                cont_end_condition = cond.model_copy()
                cont_end_condition.condition_id = str(uuid.uuid4())
                cont_end_condition.continuous = continuous_end
                cont_end_condition.distance = frame.cont_defect_end or "0"

                frame_conditions.append(cont_end_condition)

        pacp_conditions.extend(frame_conditions)

    payload = NASSCOValidatorPayload(
        pacp_inspections=pacp_inspections,
        pacp_conditions=pacp_conditions,
        pacp_ratings=pacp_ratings,
    )
    return payload


def _extract_value_from_validation_message(message: str) -> str:
    matched = re.match(r"Expected calculated value to be '(?P<val>.*?)'\.$", message)
    if not matched:
        raise ValueError(f"Could not extract value from message: {message}")

    return matched.group("val")


def extract_calculated_ratings(payload: NASSCOValidatorPayload, client: NASSCOValidatorClient):
    """
    Run the NASSCO Validator on the set of inspections and extract the calculated ratings from the validation errors
    returned.

    :returns: A mapping of inspection ID to the corrected PACP ratings
    """

    res = client.validate(payload)

    ratings_to_inspection_id = {r.rating_id: r.inspection_id for r in payload.pacp_ratings}

    rating_corrections = [
        err
        for err in res.validation_errors
        if err.code == NASSCOValidationErrorItem.ErrorCodes.CALCULATION_ERROR
        and err.entity == NASSCOValidationErrorItem.Entities.PACP_RATING
    ]

    corrections_by_inspection_id = defaultdict(dict)
    for correction in rating_corrections:
        rating_id = correction.id
        inspection_id = ratings_to_inspection_id[rating_id]

        value = _extract_value_from_validation_message(correction.message)
        corrections_by_inspection_id[inspection_id][correction.field] = value

    updated_ratings = {
        rating.inspection_id: PACPRating(
            **{
                **rating.model_dump(by_alias=True),
                **corrections_by_inspection_id.get(rating.inspection_id, {}),
            }
        )
        for rating in payload.pacp_ratings
    }

    return updated_ratings


def _build_inspection_rows(inspections_data: list[InspectionData]) -> pd.DataFrame:
    column_types = {
        "InspectionID": str,
        "Surveyed_By": str,
        "Certificate_Number": str,
        "Reviewed_By": str,
        "Reviewer_Certificate_Number": str,
        "Owner": str,
        "Customer": str,
        "PO_Number": str,
        "Work_Order_Number": str,
        "Media_Label": str,
        "Project": str,
        "Inspection_Date": str,
        "Inspection_Time": str,
        "Sheet_Number": "Int64",
        "Weather": str,
        "PreCleaning": str,
        "Date_Cleaned": str,
        "Flow_Control": str,
        "Purpose_of_Survey": str,
        "Direction": str,
        "Inspection_Technology_Used_CCTV": int,
        "Inspection_Technology_Used_Laser": int,
        "Inspection_Technology_Used_Sonar": int,
        "Inspection_Technology_Used_Sidewall": int,
        "Inspection_Technology_Used_Zoom": int,
        "Inspection_Technology_Used_Other": int,
        "Inspection_Status": str,
        "Consequence_Of_Failure": str,
        "Pressure_Value": str,
        "Drainage_Area": str,
        "Pipe_Segment_Reference": str,
        "Street": str,
        "City": str,
        "Location_Code": str,
        "Location_Details": str,
        "Pipe_Use": str,
        "Height": "Int64",
        "Width": "Int64",
        "Shape": str,
        "Material": str,
        "Lining_Method": str,
        "Coating_Method": str,
        "Pipe_Joint_Length": str,
        "Total_Length": str,
        "Length_Surveyed": str,
        "Year_Constructed": "Int64",
        "Year_Renewed": "Int64",
        "Upstream_MH_Number": str,
        "Up_Rim_to_Invert": str,
        "Up_Rim_to_Grade": str,
        "Up_Grade_to_Invert": str,
        "Up_Northing": str,
        "Up_Easting": str,
        "Up_Elevation": str,
        "Downstream_MH_Number": str,
        "Down_Rim_to_Invert": str,
        "Down_Rim_to_Grade": str,
        "Down_Grade_to_Invert": str,
        "Down_Northing": str,
        "Down_Easting": str,
        "Down_Elevation": str,
        "MH_Coordinate_System": str,
        "Vertical_Datum": str,
        "GPS_Accuracy": str,
        "Additional_Info": str,
        "Reverse_setup": str,
        "IsImperial": int,
    }

    rows = []

    for insp in inspections_data:
        inspection = insp.inspection
        asset = inspection.asset
        extra_fields = inspection.extra_fields.additional_properties
        asset_extra_fields = inspection.asset.extra_fields.additional_properties

        tech_used = _get_method_of_inspection(extra_fields.get("MethodOfInspection"))

        row = {
            "InspectionID": _format_uuid(inspection.uuid),
            "Surveyed_By": extra_fields.get("NameOfSurveyor", ""),
            "Certificate_Number": extra_fields.get("CertificateNumber", ""),
            "Reviewed_By": extra_fields.get("NameOfReviewer", ""),
            "Reviewer_Certificate_Number": extra_fields.get("ReviewerCertificateNumber", ""),
            "Owner": insp.upload_org_name or "",
            "Customer": insp.target_org_name or "",
            "PO_Number": extra_fields.get("PONumber", ""),
            "Work_Order_Number": extra_fields.get("WorkOrder", ""),
            "Media_Label": extra_fields.get("MediaLabel", ""),
            "Project": extra_fields.get("Project", ""),
            "Inspection_Date": _format_date(inspection.date),
            "Inspection_Time": _format_time(inspection.time),
            "Sheet_Number": int(extra_fields.get("SheetNumber")) if extra_fields.get("SheetNumber") else None,
            "Weather": extra_fields.get("Weather", ""),
            "PreCleaning": extra_fields.get("Precleaned", "X"),  # X means "Not Known"
            "Date_Cleaned": _format_date(extra_fields.get("DateCleaned")),
            "Flow_Control": extra_fields.get("FlowControlMeasures", ""),
            "Purpose_of_Survey": extra_fields.get("PurposeOfInspection", ""),
            "Direction": _format_direction(inspection.direction or "Downstream"),
            "Inspection_Technology_Used_CCTV": int(tech_used == "CC"),
            "Inspection_Technology_Used_Laser": int(tech_used == "LA"),
            "Inspection_Technology_Used_Sonar": int(tech_used == "SO"),
            "Inspection_Technology_Used_Sidewall": int(tech_used == "SS"),
            "Inspection_Technology_Used_Zoom": int(tech_used == "ZM"),
            "Inspection_Technology_Used_Other": int(tech_used == "ZZ"),
            "Inspection_Status": extra_fields.get("InspectionStage", "CI"),
            "Consequence_Of_Failure": asset_extra_fields.get("ConsequenceOfFailure", ""),
            "Pressure_Value": extra_fields.get("PressureValue", ""),
            "Drainage_Area": asset_extra_fields.get("DrainageArea", ""),
            "Pipe_Segment_Reference": asset_extra_fields.get("AssetID", ""),
            "Street": inspection.asset.location_street or "",
            "City": inspection.asset.location_town or "",
            "Location_Code": asset_extra_fields.get("LocationCode", ""),
            "Location_Details": asset_extra_fields.get("LocationDetails", ""),
            "Pipe_Use": asset_extra_fields.get("UseOfDrainSewer", "XX"),
            "Height": int(asset.height_diameter) if asset.height_diameter else 0,  # Not nullable
            "Width": int(asset_extra_fields.get("Width")) if asset_extra_fields.get("Width") else None,
            "Shape": asset_extra_fields.get("Shape", "C"),  # Default to circular
            "Material": _get_material_code(asset.material or ""),
            "Lining_Method": asset_extra_fields.get("LiningMaterial", ""),
            "Coating_Method": asset_extra_fields.get("LiningType", ""),
            "Pipe_Joint_Length": extra_fields.get("PipeJointLength", ""),
            "Total_Length": asset_extra_fields.get("ExpectedLength", ""),
            "Length_Surveyed": _format_chainage(inspection.length_surveyed, bool(inspection.is_imperial)),
            "Year_Constructed": (
                int(asset_extra_fields.get("YearConstructed")) if asset_extra_fields.get("YearConstructed") else None
            ),
            "Year_Renewed": (
                int(asset_extra_fields.get("YearRenewed")) if asset_extra_fields.get("YearRenewed") else None
            ),
            "Upstream_MH_Number": asset.upstream_node or "",
            "Up_Rim_to_Invert": asset_extra_fields.get("UpRimToInvert", ""),
            "Up_Rim_to_Grade": asset_extra_fields.get("UpRimToGrade", ""),
            "Up_Grade_to_Invert": asset_extra_fields.get("UpGradeToInvert", ""),
            "Up_Northing": asset_extra_fields.get("UpNorthing", ""),
            "Up_Easting": asset_extra_fields.get("UpEasting", ""),
            "Up_Elevation": asset_extra_fields.get("UpElevation", ""),
            "Downstream_MH_Number": asset.downstream_node or "",
            "Down_Rim_to_Invert": asset_extra_fields.get("DownRimToInvert", ""),
            "Down_Rim_to_Grade": asset_extra_fields.get("DownRimToGrade", ""),
            "Down_Grade_to_Invert": asset_extra_fields.get("DownGradeToInvert", ""),
            "Down_Northing": asset_extra_fields.get("DownNorthing", ""),
            "Down_Easting": asset_extra_fields.get("DownEasting", ""),
            "Down_Elevation": asset_extra_fields.get("DownElevation", ""),
            "MH_Coordinate_System": asset_extra_fields.get("MHCoordinateSys", ""),
            "Vertical_Datum": asset_extra_fields.get("MHVerticalDatum", ""),
            "GPS_Accuracy": extra_fields.get("GPSAccuracy", ""),
            "Additional_Info": extra_fields.get("GeneralRemarks", ""),
            "Reverse_setup": extra_fields.get("ReverseSetup", ""),
            "IsImperial": int(bool(inspection.is_imperial)),
        }

        rows.append(row)

    df = pd.DataFrame(rows, columns=list(column_types))
    df = df.astype(column_types).mask(df.isnull(), None)

    return df


def _build_ratings_rows(ratings: dict[str, PACPRating]) -> pd.DataFrame:
    rows = [
        {
            "RatingID": rating.rating_id,
            "InspectionID": rating.inspection_id,
            "STGradeScore1": rating.st_grade_score1,
            "STGradeScore2": rating.st_grade_score2,
            "STGradeScore3": rating.st_grade_score3,
            "STGradeScore4": rating.st_grade_score4,
            "STGradeScore5": rating.st_grade_score5,
            "OMGradeScore1": rating.om_grade_score1,
            "OMGradeScore2": rating.om_grade_score2,
            "OMGradeScore3": rating.om_grade_score3,
            "OMGradeScore4": rating.om_grade_score4,
            "OMGradeScore5": rating.om_grade_score5,
            "STPipeRating": rating.st_pipe_rating,
            "OMPipeRating": rating.om_pipe_rating,
            "OverallPipeRating": rating.overall_pipe_rating,
            "STQuickRating": rating.st_quick_rating,
            "OMQuickRating": rating.om_quick_rating,
            "PACPQuickRating": rating.pacp_quick_rating,
            "STPipeRatingsIndex": rating.st_pipe_ratings_index,
            "OMPipeRatingsIndex": rating.om_pipe_ratings_index,
            "LoFPACP": rating.lof_pacp,
        }
        for rating in ratings.values()
    ]

    df = pd.DataFrame(
        rows,
        # We specify the columns explicitly in case no ratings are passed
        columns=[
            "RatingID",
            "InspectionID",
            "STGradeScore1",
            "STGradeScore2",
            "STGradeScore3",
            "STGradeScore4",
            "STGradeScore5",
            "OMGradeScore1",
            "OMGradeScore2",
            "OMGradeScore3",
            "OMGradeScore4",
            "OMGradeScore5",
            "STPipeRating",
            "OMPipeRating",
            "OverallPipeRating",
            "STQuickRating",
            "OMQuickRating",
            "PACPQuickRating",
            "STPipeRatingsIndex",
            "OMPipeRatingsIndex",
            "LoFPACP",
        ],
    )
    # All columns are strings, but we want to retain null values
    df = df.astype(str).mask(df.isnull(), None)

    return df


def _build_conditions_rows_for_inspection(inspection_data: InspectionData) -> tuple[pd.DataFrame, pd.DataFrame]:
    """
    Build the conditions and media conditions rows for a single inspection
    """

    conditions_column_types = {
        "ConditionID": str,
        "InspectionID": str,
        "Distance": str,
        "Counter": str,
        "PACP_Code": str,
        "Continuous": str,
        "Value_1st_Dimension": str,
        "Value_2nd_Dimension": str,
        "Value_Percent": str,
        "Joint": int,
        "Clock_At_From": str,
        "Clock_To": str,
        "Remarks": str,
        "VCR_Time": str,
        "Grade": str,
    }

    condition_rows = []
    media_rows = []

    is_imperial = bool(inspection_data.inspection.is_imperial)

    frames_to_add = (f for f in inspection_data.frames if f.defect_code)
    for frame_idx, frame in enumerate(frames_to_add):
        continuous_start, continuous_end = _get_continuous_start_end(frame, frame_idx)
        value_1st, value_2nd, value_percent = _get_frame_values(frame)

        condition_id = _new_uuid()
        condition_row = {
            "ConditionID": condition_id,
            "InspectionID": _format_uuid(inspection_data.inspection.uuid),
            "Distance": _format_chainage(frame.chainage_number, is_imperial),
            "Counter": _time_ref_to_formatted_seconds(frame.time_reference) or "",
            "PACP_Code": frame.defect_code,
            "Continuous": continuous_start,
            "Value_1st_Dimension": value_1st,
            "Value_2nd_Dimension": value_2nd,
            "Value_Percent": value_percent,
            "Joint": int(bool(frame.at_joint)),
            "Clock_At_From": frame.at_clock or None,
            "Clock_To": frame.to_clock or None,
            "Remarks": frame.remarks,
            "VCR_Time": _time_ref_to_vcr_time(frame.time_reference) or "",
            "Grade": None,
        }
        condition_rows.append(condition_row)

        if continuous_end:  # Add another row for the end of the continuous defect
            cont_end_condition_row = {
                **condition_row,
                "ConditionID": _new_uuid(),
                "Continuous": continuous_end,
                "Distance": _format_chainage(frame.cont_defect_end, is_imperial),
            }
            condition_rows.append(cont_end_condition_row)

        file = inspection_data.inspection.file or {}
        filename = file.get("filename", "")
        location = ("./images/" + Path(filename).parts[-1]) if filename else ""
        media_row = {
            "MediaCondID": _new_uuid(),
            "ConditionID": condition_id,
            "File_Name": filename,
            "File_Path": location,
        }
        media_rows.append(media_row)

    conditions_df = pd.DataFrame(condition_rows, columns=list(conditions_column_types))
    conditions_df = conditions_df.astype(conditions_column_types).mask(conditions_df.isnull(), None)

    media_conditions_df = pd.DataFrame(
        media_rows,
        columns=["MediaCondID", "ConditionID", "File_Name", "File_Path"],
    )
    media_conditions_df = media_conditions_df.astype(str).mask(media_conditions_df.isnull(), None)

    return conditions_df, media_conditions_df


def _build_conditions_rows(inspection_data: list[InspectionData]) -> tuple[pd.DataFrame, pd.DataFrame]:
    """
    Build a dataframe of conditions rows (defects), and a dataframe of media conditions rows (which represent the
    images for the defect frames)
    """
    df_pairs = [_build_conditions_rows_for_inspection(insp) for insp in inspection_data]
    conditions_dfs, media_conditions_dfs = zip(*df_pairs, strict=False)

    stacked_conditions_df = pd.concat(conditions_dfs, ignore_index=True)
    stacked_media_conditions_df = pd.concat(media_conditions_dfs, ignore_index=True)
    return stacked_conditions_df, stacked_media_conditions_df


def _build_client_defined_rows(inspections_data: list[InspectionData]) -> pd.DataFrame:
    rows = [
        {
            "CustomID": _new_uuid(),
            "InspectionID": _format_uuid(insp.inspection.uuid),
            "Custom_Field_One": insp.inspection.extra_fields.additional_properties.get("ClientDefined1", ""),
            "Custom_Field_Two": insp.inspection.extra_fields.additional_properties.get("ClientDefined2", ""),
            "Custom_Field_Three": insp.inspection.extra_fields.additional_properties.get("ClientDefined3", ""),
            "Custom_Field_Four": insp.inspection.extra_fields.additional_properties.get("ClientDefined4", ""),
            "Custom_Field_Five": insp.inspection.extra_fields.additional_properties.get("ClientDefined5", ""),
            "Custom_Field_Six": insp.inspection.extra_fields.additional_properties.get("ClientDefined6", ""),
            "Custom_Field_Seven": insp.inspection.extra_fields.additional_properties.get("ClientDefined7", ""),
            "Custom_Field_Eight": insp.inspection.extra_fields.additional_properties.get("ClientDefined8", ""),
            "Custom_Field_Nine": insp.inspection.extra_fields.additional_properties.get("ClientDefined9", ""),
            "Custom_Field_Ten": insp.inspection.extra_fields.additional_properties.get("ClientDefined10", ""),
        }
        for insp in inspections_data
    ]

    df = pd.DataFrame(
        rows,
        columns=[
            "CustomID",
            "InspectionID",
            "Custom_Field_One",
            "Custom_Field_Two",
            "Custom_Field_Three",
            "Custom_Field_Four",
            "Custom_Field_Five",
            "Custom_Field_Six",
            "Custom_Field_Seven",
            "Custom_Field_Eight",
            "Custom_Field_Nine",
            "Custom_Field_Ten",
        ],
    )
    df = df.astype(str).mask(df.isnull(), None)

    return df


def _build_media_inspections_rows(inspection_data: list[InspectionData]) -> pd.DataFrame:
    """Represents the inspection video"""
    rows = []
    for insp in inspection_data:
        file = insp.inspection.file or {}
        filename = file.get("filename", "")
        location = ("./videos/" + Path(filename).parts[-1]) if filename else ""

        row = {
            "MediaID": _new_uuid(),
            "InspectionID": _format_uuid(insp.inspection.uuid),
            "File_Name": filename,
            "File_Location": location,
        }
        rows.append(row)

    df = pd.DataFrame(rows, columns=["MediaID", "InspectionID", "File_Name", "File_Location"])
    df = df.astype(str).mask(df.isnull(), None)

    return df


def _generate_sqlite_report(
    conn: sqlite3.Connection, inspections_data: list[InspectionData], validator: NASSCOValidatorClient
):
    payload = map_inspection_to_validator_input(inspections_data)
    updated_ratings = extract_calculated_ratings(payload, validator)

    inspections_df = _build_inspection_rows(inspections_data)
    ratings_df = _build_ratings_rows(updated_ratings)
    conditions_df, media_conditions_df = _build_conditions_rows(inspections_data)
    client_defined_df = _build_client_defined_rows(inspections_data)
    media_inspections_df = _build_media_inspections_rows(inspections_data)

    # 'if_exists="append"' stops the table schema from being modified
    inspections_df.to_sql("PACP_Inspections", conn, if_exists="append", index=False)
    ratings_df.to_sql("PACP_Ratings", conn, if_exists="append", index=False)
    conditions_df.to_sql("PACP_Conditions", conn, if_exists="append", index=False)
    client_defined_df.to_sql("PACP_Custom_Fields", conn, if_exists="append", index=False)
    media_inspections_df.to_sql("PACP_Media_Inspections", conn, if_exists="append", index=False)
    media_conditions_df.to_sql("PACP_Media_Conditions", conn, if_exists="append", index=False)


def handler(
    input_data: Input,
    output_fs: AbstractFileSystem,
    settings: Settings,
    api_client: api.AuthenticatedClient,
    max_retry_secs: int = 60,
) -> Output:
    api_client = api_client.with_headers({api.TARGET_ORG_ID_HEADER: str(input_data.target_org_id)})

    try:
        org_details = _fetch_org_by_id(input_data.target_org_id, api_client)
        inspections_data = _collect_inspection_data(
            org_details,
            input_data.payload.inspection_ids,
            api_client,
            max_retry_secs,
        )

        validator = NASSCOValidatorClient(settings.nassco_cli_path)

        with tempfile.TemporaryDirectory() as temp_dir:
            working_db_filepath = Path(temp_dir) / _COPIED_DB_FILENAME
            shutil.copy(_DB_TEMPLATE_PATH, working_db_filepath)

            with sqlite3.connect(working_db_filepath) as conn:
                _generate_sqlite_report(conn, inspections_data, validator)

            output_uuid = str(uuid.uuid4())
            output_db_filepath = (Path(settings.export_outputs_folder_name) / output_uuid).with_suffix(".sqlite3")
            output_fs.put(str(working_db_filepath), str(output_db_filepath))
            display_name = build_org_name_timestamped_display_name(org_details.short_name, input_data.payload.format)
            output_file = ExportOutputFile(
                file_path=str(output_db_filepath),
                size=working_db_filepath.stat().st_size,
                extension=".sqlite3",
                mime_type="application/vnd.sqlite3",
                file_display_name=display_name,
            )

    except ExportStatusException as e:
        return Output(output_file=e.status_reason)

    return Output(output_file=output_file)
