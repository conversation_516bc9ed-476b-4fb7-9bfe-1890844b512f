import re
import shutil
import subprocess
import urllib.parse
from collections.abc import Iterable
from datetime import datetime, timedelta
from itertools import islice
from pathlib import Path
from typing import TypeVar

import backoff
from fsspec import AbstractFileSystem
from pydantic import BaseModel
from pydantic_settings import BaseSettings
from vapar.clients import api

from common.models import ImportDetails

T = TypeVar("T")


def _batched(iterable: Iterable[T], n: int) -> Iterable[list[T]]:
    """
    Yield n-sized chunks from sequence
    """
    it = iter(iterable)
    while chunk := list(islice(it, n)):
        yield chunk


class Settings(BaseSettings):
    ffmpeg_path: str = "ffmpeg"
    ffprobe_path: str = "ffprobe"
    local_work_dir_root: str = "/app-data/inspection-media-imports"
    videos_container_path: str = "uploadedvideofiles/"
    frames_container_path: str = "videoframefiles/"


class Input(BaseModel):
    import_details: ImportDetails
    target_org_id: int
    instance_id: str = ""


def extract_video_duration(video_path: str | Path, *, ffprobe_path: str | Path = "ffprobe") -> float:
    """
    Extract the duration of a video file using ffprobe
    :param video_path: Path to the video file
    :param ffprobe_path: Path to the ffprobe executable
    :return: The duration of the video in seconds
    :raises: subprocess.CalledProcessError if an ffprobe call fails
    """

    cmd = [
        str(ffprobe_path),
        "-v",
        "error",
        "-show_entries",
        "format=duration",
        "-of",
        "default=noprint_wrappers=1:nokey=1",
        str(video_path),
    ]
    res_text = subprocess.run(cmd, check=True, capture_output=True, text=True).stdout.strip()
    duration = float(res_text)
    return duration


def extract_video_codec(video_path: str | Path, *, ffprobe_path: str | Path = "ffprobe") -> str:
    """
    Extract the codec of a video file - This acts as a check to ensure the file is actually a video

    :param video_path: Path to the video file
    :param ffprobe_path: Path to the ffprobe executable
    :return: The codec of the video as reported by ffprobe
    :raises: subprocess.CalledProcessError if an ffprobe call fails, for example if the codec cannot be determined
    """
    cmd = [
        str(ffprobe_path),
        "-v",
        "error",
        "-select_streams",
        "v:0",
        "-show_entries",
        "stream=codec_name",
        "-of",
        "default=noprint_wrappers=1:nokey=1",
        str(video_path),
    ]
    res_text = subprocess.run(cmd, check=True, capture_output=True, text=True).stdout.strip()
    codec = res_text.split("\n", maxsplit=1)[0]
    return codec


def extract_frames_at_timestamps(
    *,
    video_path: str | Path,
    times_and_filenames: list[tuple[str, str]],
    output_dir: str | Path,
    ffmpeg_path: str | Path = "ffmpeg",
    command_batch_size: int = 100,
):
    """
    Extract a list of frames from a video at specified timestamps
    :param video_path: Path to the video file
    :param times_and_filenames: A list of tuples of the form (timestamp, output_filename), where timestamps are of
        the form HH:MM:SS
    :param output_dir: Directory the extracted frames will be saved to
    :param ffmpeg_path: Path to the ffmpeg executable
    :param command_batch_size: Number of commands to batch together in a single ffmpeg call - needed to balance the
        command line length limit and the speed of the process

    :raises: subprocess.CalledProcessError if an ffmpeg call fails
    """
    if not times_and_filenames:
        return  # ffmpeg will error if there's nothing to do

    for batch in _batched(times_and_filenames, command_batch_size):
        cmd = [str(ffmpeg_path), "-y", "-loglevel", "warning", "-stats", "-i", str(video_path)]
        for time, output_file in batch:
            out_path = Path(output_dir) / output_file
            cmd.extend(["-ss", time, "-vframes", "1", str(out_path)])
        subprocess.run(cmd, check=True, capture_output=False)


def _fetch_video_file_details(file_id: int, client: api.AuthenticatedClient) -> api.models.File:
    res = api.endpoints.files_retrieve.sync_detailed(id=file_id, client=client)
    api.raise_on_status(res)
    return res.parsed


def _fetch_frames_by_vid_id(file_id: int, client: api.AuthenticatedClient) -> list[api.models.FramesList]:
    res = api.endpoints.files_frames_list.sync_detailed(id=file_id, client=client)
    api.raise_on_status(res)
    return res.parsed


@backoff.on_exception(backoff.expo, OSError, max_time=30)
def _copy_vid_to_local_storage(vid_source_fs: AbstractFileSystem, vid_source_path: str, vid_local_dest_path: Path):
    vid_source_fs.get(vid_source_path, str(vid_local_dest_path))


@backoff.on_exception(backoff.expo, (OSError, FileNotFoundError), max_time=30)
def _cleanup_workdir(scratch_dir: Path):
    shutil.rmtree(scratch_dir)


@backoff.on_exception(backoff.expo, OSError, max_time=60)
def _copy_frames_to_blob_storage(frames_local_dir: Path, video_dest_fs: AbstractFileSystem, dest_path: str):
    video_dest_fs.put(str(frames_local_dir / "*"), dest_path, recursive=True)


def _update_frame_urls(file_id, patch_data: list[api.models.FrameExtendedEditRequest], client: api.AuthenticatedClient):
    res = api.endpoints.inspections_videos_frames_partial_update.sync_detailed(file_id, client=client, body=patch_data)
    api.raise_on_status(res)


def _set_file_upload_completed(file_id: int, content_type: str, client: api.AuthenticatedClient):
    res = api.endpoints.files_partial_update.sync_detailed(
        id=file_id,
        client=client,
        body=api.models.PatchedInspectionFilePatchRequest(
            upload_completed=True,
            upload_completed_time=datetime.now(),
            file_type=content_type,
        ),
    )
    api.raise_on_status(res)


def _run_import_process(
    import_details: ImportDetails,
    api_client: api.AuthenticatedClient,
    settings: Settings,
    scratch_dir: Path,
    video_source_fs: AbstractFileSystem,
    destination_fs: AbstractFileSystem,
    max_retry_secs: int,
) -> None:
    file_id = import_details.payload.root.file_id

    video_file_obj: api.models.File = api.with_retries(max_retry_secs)(_fetch_video_file_details)(file_id, api_client)
    vid_url = urllib.parse.unquote(video_file_obj.url)  # "container/filename" path
    vid_url_name = Path(vid_url).name

    frames: list[api.models.FramesList] = api.with_retries(max_retry_secs)(_fetch_frames_by_vid_id)(file_id, api_client)

    video_local_path = (scratch_dir / "video").with_suffix(Path(vid_url).suffix)
    _copy_vid_to_local_storage(video_source_fs, vid_url, video_local_path)

    # Extract video details
    _vid_codec = extract_video_codec(video_local_path, ffprobe_path=settings.ffprobe_path)
    vid_duration_secs = extract_video_duration(video_local_path, ffprobe_path=settings.ffprobe_path)

    file_metadata = video_source_fs.info(vid_url).get("content_settings", {})
    content_type = file_metadata.get("content_type", "application/octet-stream")

    frames_local_dir = scratch_dir / "frames"
    frames_local_dir.mkdir(exist_ok=True)

    frames.sort(key=lambda f: f.frame_id)
    timestamp_rgx = re.compile(r"\d{1,2}:\d{2}:\d{1,2}(\.\d+)?$")
    frames_with_time_ref = [f for f in frames if timestamp_rgx.match(f.time_reference or "")]
    valid_frames = []
    for frame in frames_with_time_ref:
        hours, mins, secs = frame.time_reference.split(":")
        duration = timedelta(hours=int(hours), minutes=int(mins), seconds=float(secs))
        if duration.total_seconds() <= vid_duration_secs:
            valid_frames.append(frame)  # Filter out frames that are outside the video duration

    frames_and_filenames = [(f, f"{vid_url_name}_{n:04d}.jpg") for n, f in enumerate(valid_frames, start=1)]
    times_and_filenames = [(frame.time_reference, name) for frame, name in frames_and_filenames]
    extract_frames_at_timestamps(
        video_path=video_local_path,
        times_and_filenames=times_and_filenames,
        output_dir=frames_local_dir,
        ffmpeg_path=settings.ffmpeg_path,
    )

    # Copy frames back to blob storage
    if frames_and_filenames:
        _copy_frames_to_blob_storage(frames_local_dir, destination_fs, settings.frames_container_path)

    api.with_retries(max_retry_secs)(_set_file_upload_completed)(file_id, content_type, api_client)

    frames_update_data = [
        api.models.FrameExtendedEditRequest(
            id=f.id,
            parent_video=f.parent_video,
            chainage_number=f.chainage_number,  # Required property for some reason
            image_location=str(Path(settings.frames_container_path, Path(filename).name)),
        )
        for f, filename in frames_and_filenames
    ]
    api.with_retries(max_retry_secs)(_update_frame_urls)(file_id, frames_update_data, api_client)


def handler(
    input_dto: Input,
    settings: Settings,
    api_client: api.AuthenticatedClient,
    video_source_fs: AbstractFileSystem,
    destination_fs: AbstractFileSystem,
    max_retry_secs: int = 30,
) -> None:
    """
    Handle importing a video file for an inspection - this involves copying the video file to a scratch directory in a
    mounted azure file share, extracting video details and the frames at defect timestamps, and then copying the video
    and frames back to blob storage.

    :param input_dto: Import details
    :param settings: Settings for executables and paths
    :param api_client: Authenticated API client
    :param video_source_fs: Filesystem object for the source blob storage
    :param destination_fs: Filesystem object for the destination blob storage
    :param max_retry_secs: Maximum number of seconds to retry on api call failures
    """

    scratch_dir = Path(settings.local_work_dir_root) / str(input_dto.import_details.id)
    scratch_dir.mkdir(parents=True, exist_ok=True)

    api_client = api_client.with_headers({api.TARGET_ORG_ID_HEADER: str(input_dto.target_org_id)})
    try:
        _run_import_process(
            input_dto.import_details,
            api_client,
            settings,
            scratch_dir,
            video_source_fs,
            destination_fs,
            max_retry_secs,
        )

    finally:
        # Clean up temp storage
        shutil.rmtree(scratch_dir)
