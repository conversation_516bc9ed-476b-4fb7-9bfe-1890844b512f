"""
Function activity that generates a PDF export for a list of inspections.
"""

import asyncio
import base64
import hashlib
import logging
import math
import os
import uuid
from datetime import datetime
from html import unescape
from pathlib import Path
from urllib.parse import urljoin
from uuid import UUID

import httpx
from fsspec import AbstractFileSystem
from jinja2 import Environment, FileSystemLoader, select_autoescape
from pydantic import BaseModel, ConfigDict, field_validator
from pydantic_settings import BaseSettings
from pyppeteer import launch
from vapar.clients import api
from vapar.constants.exports import ExportStatusReason
from vapar.core.exports import BulkInspectionPDFExportPayload

from common import encrypt_url
from common.constants import STATIC_RESOURCES_DIR
from common.defects import APISubStandardAllDefectsProvider, SubStandardDefectCache
from common.inspections import fetch_inspections_with_frames
from common.models import ExportOutputFile, ExportStatusException
from common.reports import build_timestamped_display_name
from common.standards import APISubstandardProvider, SubStandardCache
from static.data.legacy_scoring import legacy_scoring

DISPLAY_NAME_MAX_LENGTH = 255
TEMPLATES_SEARCH_PATH = STATIC_RESOURCES_DIR / "templates" / "pdf_export"
IMAGES_SEARCH_PATH = STATIC_RESOURCES_DIR / "images"
INSPECTION_PDF_TEMPLATE_NAME = "skeleton-jinja.html"

log = logging.getLogger(__name__)


class Input(BaseModel):
    export_id: uuid.UUID
    target_org_id: int
    payload: BulkInspectionPDFExportPayload


class Output(BaseModel):
    output_file: ExportOutputFile | ExportStatusReason


class Settings(BaseSettings):
    export_outputs_folder_name: str = "export-outputs"
    vapar_api_base_url: str = "https://www.vapar.solutions/"
    pyppeteer_home: str = "chromium"

    image_secret_key_b64encoded: str = ""
    salt_b64encoded: str = ""


class HeaderTemplate(BaseModel):
    client: str
    uploaded_by_initials: str
    uploaded_time: str
    video_file_url: str
    file_name: str
    play_url: str
    organisation_logo: str | None
    organisation_name: str


class FooterTemplate(BaseModel):
    str_no_def: int
    str_peak: int
    str_mean: float | str
    str_total: int
    ser_no_def: int
    ser_peak: int
    ser_mean: float | str
    ser_total: int
    generation_date: str


class Frame(BaseModel):
    model_config = ConfigDict(arbitrary_types_allowed=True)

    id: int
    chainage: float
    comment: str
    code: str
    qty_str: str
    remarks: str | None
    image_url: str
    frame_id: int
    relative_position: float
    upstream_node: str | None
    downstream_node: str | None
    cont_defect_start: bool
    cont_defect_end: str | None
    class_label: str


class DefectRow(BaseModel):
    chainage: str
    code: str
    description: str
    details: str
    remarks: str | None


class DefectLine(BaseModel):
    start: str
    length: str
    angle: str


class DefectPage(BaseModel):
    content: str
    rows: list[DefectRow] = []
    lines: list[DefectLine] = []
    pipe: str | None

    @field_validator("rows", "lines", mode="before")
    def validate_fields(cls, value):
        return value or []


class FrameCell(BaseModel):
    image_src: str
    chainage: float
    comment: str
    remarks: str | None


class FramePage(BaseModel):
    content: str
    frames: list[FrameCell] = []

    @field_validator("frames", mode="before")
    def validate_fields(cls, value):
        return value or []


class HeaderUrls(BaseModel):
    video_url: str
    play_url: str


class InspectionData(BaseModel):
    model_config = ConfigDict(arbitrary_types_allowed=True)

    inspection: dict
    pages: list[DefectPage | FramePage]
    header_data: HeaderTemplate
    footer_data: FooterTemplate


def write_pdf_report(
    html: str,
    output_fs: AbstractFileSystem,
    filename: str,
    pyppeteer_home: str,
    folder: str | None = None,
    file_display_name: str | None = None,
) -> ExportOutputFile:
    """
    Convert HTML content to PDF file.
    :param html: The HTML content to convert.
    :param filename: The name of the file to save the PDF content to.
    :param folder: The folder to save the PDF content to.
    :param output_fs: The file system to save the PDF content to.
    :param pyppeteer_home: The path to the pyppeteer home directory.
    :param file_display_name: The display name of the file.
    :return: ExportOutputFile
    """

    async def create_pdf():
        browser = await launch(
            headless=True,
            args=[
                "--allow-file-access-from-files",
                "--no-sandbox",
                "--headless",
                "--disable-gpu"
                "--disable-web-security",
                "--allow-running-insecure-content",
            ],
            env={"PYPPETEER_HOME": pyppeteer_home},
            handleSIGINT=False,
            handleSIGTERM=False,
            handleSIGHUP=False,
        )
        page = await browser.newPage()
        await page.setContent(html)
        pdf_buffer = await page.pdf(printBackground=True, displayHeaderFooter=False, width=595, height=843)
        with output_fs.open(file_path, "wb") as output_file:
            output_file.write(pdf_buffer)
        await browser.close()
    with open("/hdd/test.html", "w+") as f:
        f.write(html)
    file_path = str(Path(folder, filename)) if folder else filename

    new_loop = asyncio.new_event_loop()
    asyncio.set_event_loop(new_loop)

    try:
        new_loop.run_until_complete(create_pdf())
    finally:
        new_loop.close()

    file_size = output_fs.size(file_path)
    file_details = ExportOutputFile(
        file_path=file_path,
        size=file_size,
        mime_type="application/pdf",
        extension=".pdf",
        file_display_name=file_display_name,
    )
    return file_details


def _fetch_inspection_by_id(
    inspection_id: uuid.UUID, api_client: api.AuthenticatedClient
) -> api.models.InspectionModel:
    res = api.endpoints.inspections2_retrieve.sync_detailed(
        uuid=str(inspection_id), client=api_client, use_header_names=True
    )
    api.raise_on_status(res)
    return res.parsed


def _fetch_frame_data(
    inspection_id: uuid.UUID,
    api_client: api.AuthenticatedClient,
    reported_frames: bool = True,
    max_retry_secs: int = 60,
) -> list[api.models.FramesList]:
    """
    Fetch frame data for an inspection from the client API.
    :param inspection_id: inspection uuid
    :param api_client: authenticated client
    :param max_retry_secs: max retry seconds
    :return: list of frames
    """
    res = api.with_retries(max_retry_secs)(api.endpoints.inspections2_frames_list.sync_detailed)(
        uuid=inspection_id, reported_frames=reported_frames, client=api_client
    )
    api.raise_on_status(res)
    return res.parsed


def _fetch_defects_data(
    standard_id: int, api_client: api.AuthenticatedClient, max_retry_secs: int = 60
) -> list[api.models.AllDefects]:
    """
    Fetch defects list by standard from the client API.
    :param standard_id: standard id
    :param api_client: authenticated client
    :param max_retry_secs: max retry seconds
    :return: list of defects
    """

    res = api.with_retries(max_retry_secs)(api.endpoints.standards_defects_list.sync_detailed)(
        standard_key_id=standard_id, client=api_client
    )
    api.raise_on_status(res)
    return res.parsed


def _fetch_organisation_data(
    org_id: int, api_client: api.AuthenticatedClient, max_retry_secs: int = 60
) -> api.models.Organisation:
    """
    Fetch organisation data from the client API.
    :param org_id: organisation id
    :param api_client: authenticated client
    :param max_retry_secs: max retry seconds
    :return: an organisation
    """
    res = api.with_retries(max_retry_secs)(api.endpoints.organisations_retrieve.sync_detailed)(
        id=org_id, client=api_client
    )
    api.raise_on_status(res)
    return res.parsed


def _build_urls(inspection: api.models.InspectionModel, settings: Settings) -> HeaderUrls:
    """
    Get the video URL and play URL for the inspection.
    :param inspection: The inspection to get the URLs for.
    :return: The video URL and play URL.
    """

    encrypted_vid_id = encrypt_url.encrypt(
        inspection.file["id"],
        inspection.file["target_org"],
        settings.image_secret_key_b64encoded,
        settings.salt_b64encoded,
    )

    hashed_insp_id = hashlib.sha256(str(inspection.uuid).encode()).hexdigest()

    play_url = urljoin(settings.vapar_api_base_url, f"api/playurl/{inspection.uuid}?q={hashed_insp_id}")
    vid_download_url = urljoin(settings.vapar_api_base_url, f"api/vids/{encrypted_vid_id}")

    return HeaderUrls(video_url=vid_download_url, play_url=play_url)


def get_header_data(
    inspection: api.models.InspectionModel,
    target_organisation: api.models.Organisation,
    upload_organisation: api.models.Organisation,
    settings: Settings,
) -> HeaderTemplate:
    """
    Get header data for the PDF template.
    :param inspection: The inspection to generate the header data from.
    :param target_organisation: The organisation the inspection belongs to.
    :param upload_organisation: The organisation that uploaded the inspection.
    :param settings: The settings to use.
    :return: The header data.
    """
    file = inspection.file
    if not isinstance(file, dict):
        file = file.to_dict()

    uploaded_by_initials = "".join([x[0].upper() for x in filter(None, file["upload_user"].split(" "))])
    image_source = (
        upload_organisation.logo_path if upload_organisation.org_type == "Contractor" else target_organisation.logo_path
    )
    urls = _build_urls(inspection, settings)

    header_data = HeaderTemplate(
        client=target_organisation.full_name if target_organisation.id != file["upload_org"] else "",
        uploaded_by_initials=uploaded_by_initials,
        uploaded_time=str(datetime.strptime(file["created_time"], "%Y-%m-%dT%H:%M:%S.%fZ").strftime("%Y-%m-%d"))[:10],
        video_file_url=urls.video_url,
        file_name=file["filename"],
        play_url=urls.play_url,
        organisation_logo=image_source,
        organisation_name=upload_organisation.full_name,
    )
    return header_data


def get_footer_data(frames: list[api.models.FramesList], chainage: int | float, defects: list) -> FooterTemplate:
    """
    Get footer data for the PDF template.
    :param frames: The frames to generate the footer data from.
    :param chainage: The total length of the inspection.
    :param defects: List of defects by folder standard
    :return: The footer data.
    """
    structural_scores = []
    service_scores = []

    for frame in frames:
        frame_defect = next((defect for defect in defects if defect.id == frame.defect_id), None)
        if frame_defect:
            if int(frame_defect.structural_score) > 0 or frame_defect.defect_type == "str":
                structural_scores.append(int(frame_defect.structural_score))
            if int(frame_defect.service_score) > 0 or frame_defect.defect_type == "ser":
                service_scores.append(int(frame_defect.service_score))
        elif frame["classLabel"] in legacy_scoring["str"]:
            structural_scores.append(int(legacy_scoring["str"][frame["classLabel"]]))
        elif frame["classLabel"] in legacy_scoring["ser"]:
            service_scores.append(int(legacy_scoring["ser"][frame["classLabel"]]))

    footer_data = {
        "str_no_def": len(structural_scores),
        "str_peak": max(structural_scores) if structural_scores else 0,
        "str_total": sum(structural_scores),
        "str_mean": f"{float(sum(structural_scores)) / float(chainage):.2f}" if chainage > 0.0 else "N/A",
        "ser_no_def": len(service_scores),
        "ser_peak": max(service_scores) if service_scores else 0,
        "ser_total": sum(service_scores),
        "ser_mean": f"{float(sum(service_scores)) / float(chainage):.2f}" if chainage > 0.0 else "N/A",
        "generation_date": "UTC " + f"{datetime.now().strftime('%Y-%m-%d, %H:%M:%S')}",
    }

    return FooterTemplate.model_validate(footer_data)


def get_quantity_string(frame: api.models.FramesList) -> str:
    """
    Get the quantity string for the frame defect.
    :param frame: The frame to get the quantity string from.
    :return: The quantity string.
    """
    qty_str = ""

    if (
        frame.quantity_1_value is not None
        and frame.quantity_1_units is not None
        and (float(frame.quantity_1_value) >= 0.0 or frame.class_label == "Water Level")
    ):
        qty_str += str(frame.quantity_1_value) + frame.quantity_1_units

    try:
        q2v = float(frame.quantity_2_value)
    except TypeError:
        pass
    else:
        if q2v and q2v > 0.0 and frame.quantity_2_units is not None:
            if qty_str:
                qty_str += ", "
            qty_str += str(q2v) + frame.quantity_2_units

    if frame.at_clock and frame.at_clock > 0:
        if qty_str:
            qty_str += ", "
        qty_str += "At " + str(frame.at_clock)
        if frame.to_clock and frame.at_clock > 0:
            qty_str += " to " + str(frame.to_clock)
        qty_str += " o clock"
    if frame.at_joint:
        if qty_str:
            qty_str += ", "
        qty_str += "J"

    return qty_str


def get_frame_template_data(
    inspection: api.models.InspectionModel,
    frames: list[api.models.FramesList],
    defects: list[api.models.AllDefects],
    api_client: api.AuthenticatedClient,
) -> list[Frame]:
    """
    Get frame template data for the PDF export.
    :param inspection: The inspection to generate the frame template data from.
    :param frames: The frames to generate the frame template data from.
    :param defects: List of defects to get the defect description from.
    :param api_client: The API client to use for fetching the max chainage from frames if needed.
    :return: The frame template data.
    """
    frame_list = []

    # Sometimes frame IDs can be bad (ie. not in order of chainage)
    # We re-sort the frames, first by chainage, then by their old ID, to determine an "ordering ID"
    # We then use this to determine relative position, which we order the frame list by later
    def _parse_chainage(frame):
        try:
            return round(float(frame.chainage_number), 2)
        except (TypeError, ValueError):
            return float("inf")

    frames = sorted(frames, key=lambda f: (_parse_chainage(f), f.frame_id))

    if inspection.length_surveyed:
        max_chainage = inspection.length_surveyed
    else:
        all_frames = _fetch_frame_data(
            inspection_id=UUID(inspection.uuid), api_client=api_client, reported_frames=False
        )
        max_chainage = max(all_frames, key=lambda f: f.chainage_number).chainage_number

    try:
        max_chainage = float(max_chainage)
    except (TypeError, ValueError):
        max_chainage = 0.0
    use_frame_positions = max_chainage <= 0.0

    sorted_frames = sorted(enumerate(frames), key=lambda item: (_parse_chainage(item[1]), item[1].frame_id))

    ordering_ids = [None] * len(frames)
    for ordering_id, (original_index, _) in enumerate(sorted_frames):
        ordering_ids[original_index] = ordering_id

    for i in range(len(frames)):
        frame_defect = next((defect for defect in defects if defect.id == frames[i].defect_id), None)
        chain_num = float(frames[i].chainage_number) if frames[i].chainage_number else None

        description = frame_defect.defect_description or frame_defect.defect_model_name or ""

        qty_str = get_quantity_string(frame=frames[i])

        relative_position = (ordering_ids[i] / len(frames)) * max_chainage
        if use_frame_positions or chain_num is None or chain_num > max_chainage:
            chain_num = relative_position

        if frames[i].cont_defect_start and frames[i].cont_defect_end:
            try:
                end_chain_num = float(frames[i].cont_defect_end)
            except ValueError:
                end_chain_num = "N/A"
            comment = f"{description} - Start {chain_num:.2f} - Finish {end_chain_num:.2f}"
        else:
            comment = f"{description} - {chain_num:.2f}"

        frame_list.append(
            Frame(
                id=frames[i].frame_id,
                chainage=chain_num,
                comment=comment,
                code=frames[i].defect_code,
                qty_str=qty_str,
                remarks=frames[i].remarks,
                image_url=frames[i].image_url,
                frame_id=frames[i].frame_id,
                relative_position=relative_position,
                upstream_node=inspection.asset.upstream_node,
                downstream_node=inspection.asset.downstream_node,
                cont_defect_start=frames[i].cont_defect_start,
                cont_defect_end=frames[i].cont_defect_end,
                class_label=frames[i].class_label,
            )
        )

    frame_list = sorted(frame_list, key=lambda i: i.chainage)

    return frame_list


def _calculate_defect_line(chainage: float, start: float, end: float, row: int) -> str:
    """
    Find the position, length, and angle of a line connecting a defect to its position on the pipe graphic
    :param chainage: The defect chainage.
    :param start: The page's start chainage.
    :param end: The page's end chainage.
    :row: The row number (1-20) housing the defect.
    """
    pipe_graphic_scale_factor = 493.0
    pipe_graphic_y_offset = 48.0
    row_height = 28.0
    row_y_offset = 12.5
    x_statics_squared = 1225.0
    x_statics_diff = 35.0

    # Where we draw the line from (x value is static)
    if end - start == 0:
        pipe_loc_y = pipe_graphic_y_offset
    else:
        pipe_loc_y = (((chainage - start) / (end - start)) * pipe_graphic_scale_factor) + pipe_graphic_y_offset

    # Where we draw the line to (x value is static)
    table_loc_y = (float(row) * row_height) + row_y_offset

    # Find the length of the required line using pythag
    square = x_statics_squared + ((table_loc_y - pipe_loc_y) ** 2.0)
    if square >= 0.0:
        length = math.sqrt(square)
    else:
        square = square * -1.0
        length = math.sqrt(square)
        length = length * -1.0

    # Find the angle of the line using trig
    angle_radians = math.atan((table_loc_y - pipe_loc_y) / x_statics_diff)
    angle = angle_radians * (180 / math.pi)

    line = dict(start=str(pipe_loc_y), length=str(length), angle=str(angle))

    return line


def build_defect_pages(inspection: api.models.InspectionModel, frame_list: list[Frame]) -> list[DefectPage]:
    """
    Build a list of pages to display inspection defects.
    :param inspection: The inspection to build the pages from.
    :param frame_list: The frame list to build the pages from.
    :return: The defect page list.
    """
    # This should probably be refactored into simpler functions in the future.
    pages = []
    abandoned = list(filter(lambda item: "abandoned" in item.class_label.lower(), frame_list))

    # First, we have to determine the run of 20 defects with the lowest difference
    # in chainage between first and last. This will be what we divide the total
    # chainage by to determine how many defect pages we have.
    chainages = [f.chainage for f in frame_list]
    if len(chainages) < 20:
        chainage_per_page = chainages[-1]
    else:
        chainage_per_page = min(chainages[i + 19] - chainages[i] for i in range(len(chainages) - 19))

    # Now we determine how many pages we have, and which defects they will house
    total_chainage = frame_list[-1].chainage
    amount_of_pages = math.ceil(total_chainage / chainage_per_page) if chainage_per_page > 0 else 1
    for page_number in range(amount_of_pages):
        # On the last page, we want the scale to be such that the last defect aligns with the bottom of the pipe
        if page_number < amount_of_pages - 1:
            end_chainage = (page_number + 1) * chainage_per_page
        else:
            end_chainage = frame_list[-1].chainage

        page = dict(
            content="defects",
            start_chainage=page_number * chainage_per_page,
            end_chainage=end_chainage,
            defects=[],
            rows=[],
            lines=[],
            pipe="",
        )
        for defect in frame_list:
            if defect.chainage >= page["start_chainage"]:
                if defect.chainage != frame_list[-1].chainage:
                    if defect.chainage < page["end_chainage"]:
                        page["defects"].append(defect)
                else:
                    if defect.chainage <= page["end_chainage"]:
                        page["defects"].append(defect)

        pages.append(page)

    # Each page will have a 20 row table. When we have less than 20 defects in a page
    # (almost always) we need to device which rows are blank. We do this by looking
    # where the current biggest chainage gap is, and adding a row there, and then
    # re-evaluating until we have 20 rows.
    empty_pagenums = []
    for p in range(len(pages)):
        pages[p]["rows"] = [
            dict(
                chainage=pages[p]["start_chainage"],
                code="",
                description="",
                details="",
                remarks="",
            )
        ]
        for defect in pages[p]["defects"]:
            row = dict(
                chainage=defect.chainage,
                code=defect.code,
                description=defect.comment.replace(f"{defect.code} - ", ""),
                details=defect.qty_str,
                remarks=defect.remarks if defect.remarks else "",
            )
            pages[p]["rows"].append(row)
        pages[p]["rows"].append(
            dict(
                chainage=pages[p]["end_chainage"],
                code="",
                description="",
                details="",
                remarks="",
            )
        )
        # Ensure these are sorted smallest to largest
        pages[p]["rows"] = sorted(pages[p]["rows"], key=lambda x: x["chainage"])

        # 22 items including the start and end chainages for the pipe visible on the page
        while len(pages[p]["rows"]) < 22:
            chainages = [d["chainage"] for d in pages[p]["rows"]]
            # Find the index for the chainage where the current largest gap starts
            index, _ = max(enumerate(range(1, len(chainages))), key=lambda x: chainages[x[1]] - chainages[x[0]])
            # Insert a float (the midpoint of the gap) after the index
            midpoint = (chainages[index] + chainages[index + 1]) / 2
            pages[p]["rows"].insert(
                index + 1,
                dict(
                    chainage=midpoint,
                    code="MIDPOINT TO BE CLEARED",
                    description="",
                    details="",
                    remarks="",
                ),
            )

        # Remove these chainages once everything is in place; we don't want to display them
        for r in range(len(pages[p]["rows"])):
            if pages[p]["rows"][r]["code"] == "MIDPOINT TO BE CLEARED":
                pages[p]["rows"][r]["chainage"] = None
                pages[p]["rows"][r]["code"] = ""

        # Also remove the start and end chainages we used for calculations
        del pages[p]["rows"][0]
        del pages[p]["rows"][-1]

        # If we run into any edge cases with float calculations, sometimes we have empty pages
        if len(pages[p]["defects"]) == 0:
            empty_pagenums.append(p)
            continue

        if pages[p]["defects"][0] == frame_list[0]:
            # The pipe starts on this page
            if pages[p]["defects"][-1] == frame_list[-1]:
                # The pipe starts and ends on this page
                if abandoned:
                    pages[p]["pipe"] = "start-abandoned"
                else:
                    pages[p]["pipe"] = "start-and-end"
            else:
                pages[p]["pipe"] = "start"
        else:
            # The pipe does not start on this page
            if pages[p]["defects"][-1] == frame_list[-1]:
                # The pipe ends on this page but does not start on it
                if abandoned:
                    pages[p]["pipe"] = "abandoned"
                else:
                    pages[p]["pipe"] = "end"
            else:
                pages[p]["pipe"] = "middle"

        for defect in pages[p]["defects"]:
            # Find the row number which houses this defect
            relevant_row = 1
            for r in range(len(pages[p]["rows"])):
                if pages[p]["rows"][r]["chainage"] == defect.chainage and pages[p]["rows"][r][
                    "description"
                ] == defect.comment.replace(f"{defect.code} - ", ""):
                    relevant_row = r + 1

            line = _calculate_defect_line(
                defect.chainage, pages[p]["start_chainage"], pages[p]["end_chainage"], relevant_row
            )
            pages[p]["lines"].append(line)

    # Remove any empty pages we already skipped for being empty
    # Iterate through this in reverse order so that we're not shifting indices as we iterate
    for n in reversed(empty_pagenums):
        del pages[n]

    # Add start and end node identifiers as remarks in the first and last defects
    if inspection.additional_properties["start_node"]:
        pages[0]["rows"][0]["remarks"] = f"Start node: {inspection.additional_properties['start_node']}"
    if not abandoned and inspection.additional_properties["end_node"]:
        pages[-1]["rows"][-1]["remarks"] = f"End node: {inspection.additional_properties['end_node']}"

    for p in range(len(pages)):
        # Change chainage values to str
        for r in range(len(pages[p]["rows"])):
            try:
                pages[p]["rows"][r]["chainage"] = f"{pages[p]['rows'][r]['chainage']:.2f}"
            except (TypeError, ValueError):
                pages[p]["rows"][r]["chainage"] = str(pages[p]["rows"][r]["chainage"])
            if pages[p]["rows"][r]["chainage"] == "None":
                pages[p]["rows"][r]["chainage"] = ""
        # Cleanup values only used for calculations etc.
        del pages[p]["defects"]
        del pages[p]["start_chainage"]
        del pages[p]["end_chainage"]

    return pages


def build_frame_pages(frame_list: list[Frame]) -> list:
    """
    Build a list of frame pages for the frame list.
    :param frame_list: The frame list to build the pages from.
    :return: The frame page list.
    """
    frames_on_page = 0
    frame_pages = []
    frame_set = dict(content="frames", frames=[])

    for frame in frame_list:
        frame_data = {
            "image_src": frame.image_url,
            "chainage": frame.chainage,
            "comment": frame.comment,
            "remarks": frame.remarks,
        }

        frame_set["frames"].append(frame_data)

        frames_on_page += 1
        if frames_on_page == 4:
            frame_pages.append(frame_set)
            frame_set = dict(content="frames", frames=[])
            frames_on_page = 0

    if len(frame_set["frames"]) > 0:
        frame_pages.append(frame_set)

    return frame_pages


def build_single_inspection_display_name(data: api.models.InspectionModel) -> str:
    """
    Build the display name for a single inspection, chopping off parts of the name if it exceeds the maximum length.
    The name has the format `{asset id}_{upstream node}_{downstream node}_VS_ID_{legacy id}.pdf`.
    """
    required_suffix = f"VS_ID_{data.legacy_id or ''}.pdf"
    remaining_len = DISPLAY_NAME_MAX_LENGTH - len(required_suffix)
    available_parts = []
    if data.asset.asset_id:
        available_parts.append(data.asset.asset_id)
    if data.asset.upstream_node:
        available_parts.append(data.asset.upstream_node)
    if data.asset.downstream_node:
        available_parts.append(data.asset.downstream_node)
    chosen_parts = []
    for part in available_parts:
        if len(part) >= remaining_len:
            break
        remaining_len -= len(part) - 1  # -1 for the underscore separator
        chosen_parts.append(part)

    chosen_parts.append(required_suffix)

    display_name = "_".join(chosen_parts)
    display_name = display_name.replace("/", "_")  # slashes can't be present in file names in Unix
    return display_name


def collect_inspections_data(
    inspection_ids: list[uuid.UUID],
    api_client: api.AuthenticatedClient,
    settings: Settings,
    defect_cache: SubStandardDefectCache | None = None,
    substd_cache: SubStandardCache | None = None,
    max_retry_secs: int = 60,
) -> list[InspectionData]:
    """
    Bulk fetch and format data needed for PDF inspection exports.

    :param inspection_ids: List of inspection UUIDs to fetch data for
    :param api_client: API client to use for fetching data
    :param settings: Settings to use for urls, etc.
    :param defect_cache: Cache for loaded defect data
    :param substd_cache: Cache for loaded substandard data
    :param max_retry_secs: Maximum number of seconds to retry API requests
    :return: List of InspectionData objects.
    """

    # Cache these common entities
    defect_provider = APISubStandardAllDefectsProvider(api_client, cache=defect_cache, max_retry_secs=max_retry_secs)
    substd_provider = APISubstandardProvider(api_client, cache=substd_cache, max_retry_secs=max_retry_secs)
    orgs = {}

    log.info("Fetching inspections with frames...")
    inspections_with_frames = fetch_inspections_with_frames(api_client, inspection_ids, max_retry_secs=max_retry_secs)
    log.info(f"Found {len(inspections_with_frames)} inspections")

    inspections_data = []
    for pair in inspections_with_frames:
        inspection = pair.inspection
        frames = pair.frames
        substandard = substd_provider.get_by_standard(inspection.standard, inspection.asset.use_of_drain_sewer)
        defects = defect_provider.get_by_substandard(substandard.id)

        frame_template_data = get_frame_template_data(inspection, frames, defects, api_client)

        defect_pages = build_defect_pages(inspection, frame_template_data)
        log.info(f"Generated {len(defect_pages)} defect pages")
        frame_pages = build_frame_pages(frame_template_data)
        log.info(f"Generated {len(frame_pages)} frame pages")
        pages = defect_pages + frame_pages

        target_org_id = inspection.file["target_org"]
        upload_org_id = inspection.file["upload_org"]
        orgs[target_org_id] = orgs.get(target_org_id) or _fetch_organisation_data(
            target_org_id, api_client, max_retry_secs
        )
        orgs[upload_org_id] = orgs.get(upload_org_id) or _fetch_organisation_data(
            upload_org_id, api_client, max_retry_secs
        )

        header_data = get_header_data(
            inspection=inspection,
            target_organisation=orgs[target_org_id],
            upload_organisation=orgs[upload_org_id],
            settings=settings,
        )
        footer_data = get_footer_data(
            frames=frames,
            chainage=inspection.length_surveyed,
            defects=defects,
        )

        inspection_dict = inspection.to_dict()
        inspection_dict["url"] = urljoin(settings.vapar_api_base_url, f"inspection/{inspection.uuid}")
        inspection_dict["Date"] = str(inspection_dict["Date"])[:10]
        log.info("Successfully set inspection URL and date")

        data = InspectionData(
            inspection=inspection_dict,
            pages=pages,
            header_data=header_data,
            footer_data=footer_data,
        )
        inspections_data.append(data)

    return inspections_data


def render_pdf(
    inspections: list[InspectionData],
    httpx_client: httpx.Client,
) -> str:
    """
    Render the report data as an HTML string.

    :param inspections: List of InspectionData objects. If multiple inspections are provided, they will be included one
        after the other.
    :param httpx_client: HTTPX client to use for fetching and embedding images
    :return: The rendered HTML document
    """

    env = Environment(loader=FileSystemLoader(TEMPLATES_SEARCH_PATH), autoescape=select_autoescape())
    template = env.get_template(INSPECTION_PDF_TEMPLATE_NAME)

    html = template.render(
        inspections=inspections,
        abs_path=os.path.abspath(FileSystemLoader(IMAGES_SEARCH_PATH).searchpath[0]),
    )
    log.info("Successfully rendered jinja template")

    # If we're using pyppeteer to render to PDF, we need to convert all images to base64
    html_lines = html.splitlines()
    for x in range(len(html_lines)):
        if "<img" in html_lines[x] and 'src="' in html_lines[x]:
            index = html_lines[x].find('src="') + 5
            path = html_lines[x][index : html_lines[x].find('"', index)]
            if path and os.path.isfile(path):
                with open(path, "rb") as image:
                    base64_image = base64.b64encode(image.read()).decode("utf-8")
                    html_lines[x] = html_lines[x].replace(path, f"data:image/png;base64,{base64_image}")
            elif path.startswith("http"):
                url = unescape(path)
                response = httpx_client.get(url)
                if response.status_code == 200:
                    base64_image = base64.b64encode(response.content).decode("utf-8")
                    html_lines[x] = html_lines[x].replace(path, f"data:image/png;base64,{base64_image}")

    html = "\n".join(html_lines)
    log.info("Successfully converted all images to base64")

    return html


def handler(
    input_data: Input,
    api_client: api.AuthenticatedClient,
    settings: Settings,
    output_fs: AbstractFileSystem,
    defect_cache: SubStandardDefectCache | None = None,
    substd_cache: SubStandardCache | None = None,
    max_retry_secs: int = 60,
) -> Output:
    log.info(
        "Starting GeneratePdfExport activity",
        extra={"inspection_ids": input_data.payload.inspection_ids},
    )

    api_client = api_client.with_headers({api.TARGET_ORG_ID_HEADER: str(input_data.target_org_id)})

    try:
        if len(input_data.payload.inspection_ids) == 0:
            raise ExportStatusException(ExportStatusReason.INVALID_INSPECTION_ID)
        inspections = collect_inspections_data(
            inspection_ids=input_data.payload.inspection_ids,
            api_client=api_client,
            settings=settings,
            defect_cache=defect_cache,
            substd_cache=substd_cache,
            max_retry_secs=max_retry_secs,
        )

        http_client = api_client.get_httpx_client()
        html = render_pdf(inspections=inspections, httpx_client=http_client)

        filename = uuid.uuid4()

        if len(input_data.payload.inspection_ids) > 1:
            display_name = build_timestamped_display_name("VS_Reports", input_data.payload.format)
        else:
            display_name = build_single_inspection_display_name(
                api.models.InspectionModel.from_dict(inspections[0].inspection)
            )

        file = write_pdf_report(
            html=html,
            filename=f"{filename}.pdf",
            folder=settings.export_outputs_folder_name,
            output_fs=output_fs,
            pyppeteer_home=settings.pyppeteer_home,
            file_display_name=display_name,
        )
        log.info("Wrote PDF to " + str(filename) + ".pdf")

    except ExportStatusException as e:
        return Output(output_file=e.status_reason)

    return Output(output_file=file)
