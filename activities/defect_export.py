import logging
import re
from datetime import datetime
from typing import Any
from uuid import UUID

import pandas as pd
import xmltodict
from fsspec import AbstractFileSystem
from pydantic import BaseModel
from pydantic_settings import BaseSettings
from vapar.clients import api
from vapar.constants.conversion import DIRECTION_MAP
from vapar.constants.exports import ExportFormat, ExportStatusReason
from vapar.constants.pipes import DirectionEnum, StandardEnum
from vapar.core.exports import DefectExportPayload
from yarl import URL

from common.encrypt_url import combine_urls, num_to_sha256
from common.inspections import InspectionWithFrames, fetch_inspections_with_frames
from common.models import ExportOutputFile, ExportStatusException
from common.reports import (
    build_org_name_timestamped_display_name,
    build_timestamped_display_name,
    write_tabular_report,
    write_textual_report,
)

log = logging.getLogger(__name__)

_MATERIAL_NAME_TO_CODE = {
    "ac": "AC",
    "aconcrete": "AC",
    "asbestoscement": "AC",
    "brick": "BR",
    "brickwork": "BR",
    "butifriflekelay": "VC",
    "castiron": "CI",
    "castirontypeunknown": "CI",
    "cc": "CO",
    "chestoscement": "AC",
    "clay": "VC",
    "clayvitrified": "VC",
    "co": "CO",
    "combined": "X",
    "con": "CO",
    "conc": "CO",
    "concete": "CO",
    "concreat": "CO",
    "concreate": "CO",
    "concreete": "CO",
    "concrete": "CO",
    "concretee": "CO",
    "concretepipe": "CO",
    "concretepreca": "CO",
    "concretereinforced": "RC",
    "concretesegments": "CO",
    "concreteunreinforced": "CO",
    "concretevc": "CO",
    "concrte": "CO",
    "cong": "CO",
    "curedinplacepipe": "X",
    "detailmissing": "Z",
    "earthenware": "VC",
    "eartherware": "VC",
    "epoxy": "EP",
    "fibrecement": "FC",
    "fibrereinforcedcement": "FC",
    "fiedclay": "VC",
    "fifiedclay": "VC",
    "gg": "X",
    "go": "X",
    "grilledclay": "VC",
    "hape": "PE",
    "hdpe": "PE",
    "hope": "PE",
    "icc": "X",
    "ifiedclay": "VC",
    "iriblog": "XP",
    "ivc": "X",
    "lined": "X",
    "llined": "X",
    "masonryuncoursedorrough": "MAR",
    "mildsteelcementlined": "CL",
    "": "Z",
    "opvc": "PVC",
    "other": "Z",
    "p": "XP",
    "pitc": "PF",
    "pitchfibre": "PF",
    "plastic": "XP",
    "poinylchlor": "PVC",
    "polyethylene": "PE",
    "polypropylene": "PP",
    "polyvinylchlor": "PVC",
    "polyvinylchlorid": "PVC",
    "polyvinylchloride": "PVC",
    "pvc": "PVC",
    "pvcelasticlsed": "PVC",
    "pvcexliner": "PVC",
    "pvcplasticised": "PVC",
    "pvcplatisicsed": "PVC",
    "pvcunplasticised": "PVC",
    "pyc": "PVC",
    "rc": "RC",
    "reinforceconcrete": "RC",
    "reinforcedconcrete": "RC",
    "reinforcedconcretepipe": "RC",
    "reinforcesd": "RC",
    "renforcedcement": "RC",
    "ribloc": "XP",
    "riblock": "XP",
    "riblocpvc": "XP",
    "riblocvc": "XP",
    "riblog": "XP",
    "riblogpvc": "XP",
    "sbestoscement": "AC",
    "sitriffedclay": "VC",
    "spiralwoundliner": "XP",
    "steel": "ST",
    "strifiedclay": "VC",
    "toifiedclay": "VC",
    "trifiedclay": "VC",
    "unidentifiedtypeofironorsteel": "XI",
    "unidentifiedtypeofplastics": "XP",
    "unidentifiedtypeorironorsteel": "XI",
    "unknown": "X",
    "upvc": "PVC",
    "upvcsplu": "PVC",
    "vc": "VC",
    "vcp": "VC",
    "vg": "VC",
    "vic": "VC",
    "viscalledclay": "VC",
    "vitrifiedclay": "VC",
    "vitrifiedclayiceallclayware": "VC",
    "vitrifiedclayieallclayware": "VC",
    "vitrifiedclayiveallclayware": "VC",
    "vitrifiedclaypipe": "VC",
    "vitrifiedclaypipeea": "VC",
    "vitrifiedclaypipeecircular": "VC",
    "vitrifiedclaypipeiea": "VC",
    "vitrifiedclaypipeieallclayware": "VC",
    "vitrifiedclaypipeiveallclay": "VC",
    "vitrifiedclays": "VC",
    "vitrifiedelay": "VC",
    "vitrifiedlay": "VC",
    "vitrifiericlay": "VC",
    "vitriifiedclay": "VC",
    "zzz": "Z",
}


class Settings(BaseSettings):
    video_play_domain: str = "https://vapar.azure-api.net/assets/playurl/"
    export_outputs_folder_name: str = "export-outputs"


class Input(BaseModel):
    export_id: UUID
    target_org_id: int
    payload: DefectExportPayload
    instance_id: str = ""


class Output(BaseModel):
    output_file: ExportOutputFile | ExportStatusReason


def _fetch_org_details_by_id(org_id: int, api_client: api.AuthenticatedClient) -> api.models.EditOrganisation:
    res = api.endpoints.organisations_retrieve.sync_detailed(id=org_id, client=api_client)
    api.raise_on_status(res)
    return res.parsed


def _build_report_rows(inspections: list[InspectionWithFrames], settings: Settings) -> pd.DataFrame:
    """
    Build the defect report
    :param inspections: List of inspections
    :return: The report as a DataFrame
    """
    rows = []

    continuous_defect_count = 1
    for inspection_data in inspections:
        inspection = inspection_data.inspection
        frames_filtered = [f for f in inspection_data.frames if f.defect_id]
        reportable_frames = [f for f in frames_filtered if f.class_label == "Defect"]
        pacp = inspection.standard == StandardEnum.PACP7

        if isinstance(inspection.date, datetime):
            date_captured = datetime.strftime(inspection.date, "%Y-%m-%d")
        else:
            date_captured = inspection.date

        play_domain = URL(settings.video_play_domain)
        sha_code = num_to_sha256(inspection.legacy_id)
        vid_url_obj = combine_urls(play_domain, URL(str(inspection.legacy_id))).update_query(q=sha_code)
        vid_url = str(vid_url_obj)

        if not inspection.asset.height_diameter:
            diameter = None
        elif (
            inspection.asset.height_diameter
            and isinstance(inspection.asset.height_diameter, str)
            and inspection.asset.height_diameter[-2:] == "mm"
        ):
            diameter = inspection.asset.height_diameter[0:-2]
        else:
            diameter = str(inspection.asset.height_diameter)

        replaced_name = " ".join(
            filter(None, [inspection.asset.location_street, inspection.asset.location_town])
        ).replace(",", " ")

        if inspection.file["created_time"]:
            created_datetime = datetime.strptime(inspection.file["created_time"], "%Y-%m-%dT%H:%M:%S.%fZ")
        else:
            created_datetime = None

        for frame in frames_filtered:
            row = {
                "VAPAR ID": inspection.legacy_id or "",
                "Name": replaced_name or "",
                "Asset No": inspection.asset.asset_id or "",
                "Length": f"{float(inspection.length_surveyed or 0):.2f}",
                "Structural Grade": (inspection.structural_grade or 1) if len(reportable_frames) <= 0 else 1,
                "Service Grade": (inspection.service_grade or 1) if len(reportable_frames) <= 0 else 1,
                "Date Captured": date_captured or "",
                "Diameter": diameter or "",
                "Video File": vid_url or "",
                "Material": inspection.asset.material or "",
                "Upstream Node": inspection.asset.upstream_node or "",
                "Downstream Node": inspection.asset.downstream_node or "",
                "Dir of Travel": inspection.direction or "",
                "Frame ID": "Frame " + str(frame.frame_id) or "",
                "Defect Class": frame.defect_class or "",
                "Defect Length": f"{float(frame.chainage_number):.2f}" if frame.chainage_number else "",
                "Defect Code": frame.defect_code or "",
                "Inspection Notes": inspection.general_remarks or "",
                "Structural Score": frame.defect_str_score,
                "Service Score": frame.defect_ser_score,
                "Job Name": inspection.folder["job_name"],
                "Work Order": inspection.work_order or "",
                "Upload User": (
                    "".join(x[0] for x in inspection.file["upload_user"].split())
                    if inspection.file["upload_user"]
                    else ""
                ),
                "Upload Date": datetime.strftime(created_datetime, "%Y-%m-%d") or "",
                "Remarks": frame.remarks or "",
                "Continuous": "",
            }

            if pacp and frame.cont_defect_start:
                # Add column for continuous defects
                row["Continuous"] = "S" + str(continuous_defect_count).zfill(2)

                # Add duplicate row for continuous defect end
                cont_defect_end_row = row.copy()
                end_row_chainage = f"{float(frame.cont_defect_end or 0.0):.2f}"
                cont_defect_end_row["Continuous"] = "F" + str(continuous_defect_count).zfill(2)
                cont_defect_end_row["Defect Length"] = end_row_chainage

                rows.extend([row, cont_defect_end_row])
                continuous_defect_count += 1
            else:
                rows.append(row)

    df = pd.DataFrame(
        rows,
        columns=[
            "VAPAR ID",
            "Name",
            "Asset No",
            "Length",
            "Structural Grade",
            "Service Grade",
            "Date Captured",
            "Diameter",
            "Video File",
            "Material",
            "Upstream Node",
            "Downstream Node",
            "Dir of Travel",
            "Frame ID",
            "Defect Class",
            "Defect Length",
            "Defect Code",
            "Inspection Notes",
            "Structural Score",
            "Service Score",
            "Job Name",
            "Work Order",
            "Upload User",
            "Upload Date",
            "Remarks",
            "Continuous",
        ],
    )
    df = df.astype(
        {
            "VAPAR ID": str,
            "Name": str,
            "Asset No": str,
            "Length": str,
            "Structural Grade": int,
            "Service Grade": int,
            "Date Captured": str,
            "Diameter": str,
            "Video File": str,
            "Material": str,
            "Upstream Node": str,
            "Downstream Node": str,
            "Dir of Travel": str,
            "Frame ID": str,
            "Defect Class": str,
            "Defect Length": str,
            "Defect Code": str,
            "Inspection Notes": str,
            "Structural Score": str,
            "Service Score": str,
            "Job Name": str,
            "Work Order": str,
            "Upload User": str,
            "Upload Date": str,
            "Remarks": str,
            "Continuous": str,
        }
    )
    return df


def _get_asset_id_representation(inspection: api.models.InspectionModel) -> str:
    if inspection.asset.asset_id:
        return inspection.asset.asset_id
    elif inspection.direction in (DirectionEnum.UPSTREAM, DirectionEnum.UNKNOWN):
        return inspection.additional_properties.get("start_node", "")
    else:
        return inspection.additional_properties.get("end_node", "")


def _build_xml_survey_header_contents(inspection_data: InspectionWithFrames) -> dict[str, Any]:
    if isinstance(inspection_data.inspection.date, datetime):
        survey_date = datetime.strftime(inspection_data.inspection.date, "%Y-%m-%d")
    else:
        survey_date = inspection_data.inspection.date or "0000-00-00"

    start_node = (inspection_data.inspection.additional_properties.get("start_node") or "").upper()
    end_node = (inspection_data.inspection.additional_properties.get("end_node") or "").upper()

    extra_fields = inspection_data.inspection.extra_fields.additional_properties

    direction = inspection_data.inspection.direction
    mscc5_directions = DIRECTION_MAP[StandardEnum.MSCC5]
    standard_direction = mscc5_directions.get(direction, mscc5_directions[DirectionEnum.UNKNOWN]).value

    vapar_id = inspection_data.inspection.legacy_id or ""
    if remarks := inspection_data.inspection.general_remarks:
        note = f"VAPAR ID {vapar_id}, {remarks}"
    else:
        note = f"VAPAR ID {vapar_id}"

    material = inspection_data.inspection.asset.material or ""
    material_cleaned = re.sub(r"[^a-zA-Z]", "", material).lower()
    material_code = _MATERIAL_NAME_TO_CODE.get(material_cleaned, "X")

    header = {
        "ClientsJobRef": extra_fields.get("ClientsJobRef", ""),
        "ContractorsJobRef": extra_fields.get("ContractorsJobRef", ""),
        "PipelineLengthRef": _get_asset_id_representation(inspection_data.inspection),
        "Date": survey_date,
        "LocationStreet": inspection_data.inspection.asset.location_street or "",
        "LocationTown": inspection_data.inspection.asset.location_town or "",
        "StartNodeRef": start_node,
        "Node1Ref": start_node,
        "FinishNodeRef": end_node,
        "Node3Ref": extra_fields.get("Node3Ref", "-"),
        "Direction": standard_direction,
        "HeightDiameter": inspection_data.inspection.asset.height_diameter or "",
        "Material": material_code,
        "UseOfDrainSewer": inspection_data.inspection.asset.use_of_drain_sewer or "C",
        "Shape": extra_fields.get("Shape", "C"),
        "GeneralRemarks": note,
        "ExpectedLength": inspection_data.inspection.length_surveyed or 0.0,
        "LengthSurveyed": inspection_data.inspection.length_surveyed or 0.0,
        "Precleaned": extra_fields.get("Precleaned", "Z"),
        "MethodOfInspection": extra_fields.get("MethodOfInspection", "B"),
        "Standard": "MSCC5",
        "UpstreamNode": inspection_data.inspection.asset.upstream_node or "",
        "DownstreamNode": inspection_data.inspection.asset.downstream_node or "",
    }

    return header


def _build_xml_observation_contents(
    inspection: api.models.InspectionModel, frame: api.models.FramesList
) -> dict[str, Any]:
    defect_code = frame.defect_code or ""

    observation = {
        "VideoRef": frame.time_reference or "0:00:00",
        "Distance": frame.chainage_number,
        "Code": defect_code,
    }

    if remarks := frame.remarks:
        observation["Remarks"] = defect_code + ", " + remarks if defect_code else remarks

    if frame.at_joint:
        observation["Joint"] = "J"

    if frame.at_clock:
        at_clock = str(frame.at_clock or 0).zfill(2)
        observation["ClockRefAtFrom"] = at_clock
    if frame.to_clock:
        to_clock = str(frame.to_clock or 0).zfill(2)
        observation["ClockRefTo"] = to_clock

    if frame.quantity_1_units == "%":
        percentage_value = str(frame.quantity_1_value or "0").zfill(2)
        observation["Percentage"] = percentage_value
    elif frame.quantity_2_units == "%":
        percentage_value = str(frame.quantity_2_value or "0").zfill(2)
        observation["Percentage"] = percentage_value

    if frame.quantity_1_value and frame.quantity_1_units != "%":
        observation["Dimension1"] = frame.quantity_1_value
    if frame.quantity_2_value and frame.quantity_2_units != "%":
        if "Dimension1" in observation:
            observation["Dimension2"] = frame.quantity_2_value
        else:
            observation["Dimension1"] = frame.quantity_2_value

    if defect_code.startswith(("JD", "OJ")) and len(defect_code) > 2:
        observation["Band"] = defect_code[2]  # M or L

    if defect_code != "VVR":
        start_node = inspection.additional_properties.get("start_node", "")
        end_node = inspection.additional_properties.get("end_node", "")
        asset_id = _get_asset_id_representation(inspection)
        prefix = f"{asset_id}_{start_node}_{end_node}"
        image_path = f"Images/{prefix}_{frame.chainage_number}_{frame.frame_id}.jpg"

        observation["PhotographRefs"] = [{"PhotographRef": image_path}]

    return observation


def _build_xml_survey_contents(inspection_data: InspectionWithFrames) -> dict[str, Any]:
    video_reference_observation = {
        "VideoRef": "0:00:00",
        "Distance": 0.0,
        "Code": "VVR",
    }
    observations = [video_reference_observation]
    observations.extend(
        _build_xml_observation_contents(inspection_data.inspection, frame) for frame in inspection_data.frames
    )

    survey = {
        "Header": _build_xml_survey_header_contents(inspection_data),
        "Observations": {"Observation": observations},
    }

    return survey


def _build_xml_report_contents(inspection_data_list: list[InspectionWithFrames]) -> dict[str, Any]:
    """
    Construct a tree representing the defect XML report contents
    """

    surveys = [_build_xml_survey_contents(inspection_data) for inspection_data in inspection_data_list]

    # SurveyGroup is the root tag of the xml document
    # The properties (starting with '@') are used to define the xml namespaces and schema location
    root = {
        "SurveyGroup": {
            "@xsi:schemalocation": "https://www.wrcplc.co.uk/srm/schemas/mscc5_cctv.xsd",
            "@xmlns": "https://www.wrcplc.co.uk/srm",
            "@xmlns:xsi": "https://www.w3.org/2001/XMLSchema-instance",
            "@xmlns:msxsl": "urn:schemas-microsoft-com:xslt",
            "Survey": surveys,
        }
    }
    return root


def build_MSCC5_xml_report(inspection_data_list: list[InspectionWithFrames]) -> str:
    """
    Build an XML report from the given inspection data, returning the XML as a string.
    Note that this report is only intended to be used for MSCC5 inspections.
    """
    version_header = '<?xml version="1.0" encoding="UTF-8"?>'
    stylesheet_header = (
        '<?xml-stylesheet type="text/xsl"href="https://www.wrcplc.co.uk/srm/schemas/mscc5_cctv_stylesheet.xsl"?>'
    )

    root = _build_xml_report_contents(inspection_data_list)
    rendered_root = xmltodict.unparse(root, pretty=True, full_document=False)

    full_doc = f"{version_header}\n{stylesheet_header}\n{rendered_root}"
    return full_doc


def handler(
    input_data: Input,
    settings: Settings,
    api_client: api.AuthenticatedClient,
    output_fs: AbstractFileSystem,
    max_retry_secs: int = 60,
) -> Output:
    log.info(
        "Starting DefectExport activity",
        extra={
            "export_id": input_data.export_id,
            "target_org_id": input_data.target_org_id,
            "instance_id": input_data.instance_id,
        },
    )
    api_client = api_client.with_headers({api.TARGET_ORG_ID_HEADER: str(input_data.target_org_id)})

    try:
        if len(input_data.payload.inspection_ids) == 0:
            raise ExportStatusException(ExportStatusReason.INVALID_INSPECTION_ID)

        inspections = fetch_inspections_with_frames(
            api_client,
            input_data.payload.inspection_ids,
            max_retry_secs=max_retry_secs,
        )

        if input_data.payload.format == ExportFormat.CSV:
            report_df = _build_report_rows(inspections, settings)
            display_name = build_timestamped_display_name("VS_Defects", input_data.payload.format)
            output_file = write_tabular_report(
                report_df,
                output_fs,
                input_data.payload.format,
                folder=settings.export_outputs_folder_name,
                file_display_name=display_name,
            )

        elif input_data.payload.format == ExportFormat.XML:
            report_str = build_MSCC5_xml_report(inspections)
            org_details = api.with_retries(max_retry_secs)(_fetch_org_details_by_id)(
                input_data.target_org_id, api_client
            )
            display_name = build_org_name_timestamped_display_name(org_details.short_name, input_data.payload.format)
            output_file = write_textual_report(
                report_str,
                output_fs,
                input_data.payload.format,
                folder=settings.export_outputs_folder_name,
                file_display_name=display_name,
            )

        else:
            raise ValueError(f"Unexpected export format - {input_data.payload.format}")

    except ExportStatusException as e:
        return Output(output_file=e.status_reason)

    return Output(output_file=output_file)
