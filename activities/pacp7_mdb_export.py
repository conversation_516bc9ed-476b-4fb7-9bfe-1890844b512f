import datetime
import logging
import re
import shutil
import tempfile
import uuid
from collections.abc import Sequence
from pathlib import Path
from typing import NamedTuple

import jay<PERSON><PERSON><PERSON><PERSON>
from fsspec import AbstractFileSystem
from pydantic import BaseModel
from vapar.clients import api
from vapar.constants.conversion import DIRECTION_MAP
from vapar.constants.exports import ExportStatusReason
from vapar.constants.pipes import DirectionEnum, PipeTypeEnum, StandardEnum
from vapar.core.exports import PACP7MDBExportPayload

from common.constants import STATIC_RESOURCES_DIR
from common.defects import APISubStandardAllDefectsProvider
from common.inspections import fetch_inspections_with_frames
from common.models import ExportOutputFile, ExportStatusException
from common.reports import build_org_name_timestamped_display_name
from common.standards import APISubstandardProvider
from static.data.pacp_constants import FALLBACK_MATERIAL_CODE, MATERIAL_NAME_TO_CODE

_JDBC_LIBS_DIR = STATIC_RESOURCES_DIR / "JDBC"
_JDBC_LIBS = ":".join(str(lib) for lib in _JDBC_LIBS_DIR.glob("*.jar"))
_DB_TEMPLATE_PATH = STATIC_RESOURCES_DIR / "MDB" / "pacp7_export_template.mdb"

_COPIED_DB_FILENAME = "pacp7_export.mdb"


log = logging.getLogger(__name__)


class Input(BaseModel):
    export_id: uuid.UUID
    target_org_id: int
    payload: PACP7MDBExportPayload
    instance_id: str = ""


class Output(BaseModel):
    output_file: ExportOutputFile | ExportStatusReason


class Settings(BaseModel):
    export_outputs_folder_name: str = "export-outputs"


class InspectionData(NamedTuple):
    inspection: api.models.InspectionModel
    frames: list[api.models.FramesList]
    defects: dict[int, api.models.AllDefects]
    upload_org_name: str | None = None
    target_org_name: str | None = None


### Row types representing the rows inserted to the Access DB ###


class ConditionRow(NamedTuple):
    inspection_id: int
    distance: float
    pacp_code: str
    value_1st_dimension: float
    value_2nd_dimension: float
    value_percent: float
    joint: bool
    clock_at_from: int
    clock_to: int
    remarks: str
    continuous: str


class MediaConditionRow(NamedTuple):
    condition_id: int
    image_reference: str
    image_path: str
    video_file: str
    video_file_path: str


class InspectionRow(NamedTuple):
    surveyed_by: str
    certificate_number: str
    direction: str
    inspection_status: str
    inspection_date: str
    pre_cleaning: str
    owner: str
    customer: str
    project: str
    height: float
    total_length: float
    length_surveyed: float
    inspection_technology_used_cctv: bool
    pipe_segment_reference: str
    street: str
    city: str
    pipe_use: str
    material: str
    downstream_mh: str
    upstream_mh: str
    additional_info: str


class RatingRow(NamedTuple):
    rating_id: int
    inspection_id: int
    st_grade_score1: float
    st_grade_score2: float
    st_grade_score3: float
    st_grade_score4: float
    st_grade_score5: float
    om_grade_score1: float
    om_grade_score2: float
    om_grade_score3: float
    om_grade_score4: float
    om_grade_score5: float
    st_pipe_rating: float
    om_pipe_rating: float
    overall_pipe_rating: float
    st_quick_rating: float
    om_quick_rating: float
    pacp_quick_rating: float
    st_pipe_ratings_index: float
    om_pipe_ratings_index: float
    overall_pipe_ratings_index: float
    lof_pacp: float
    risk: float


class MediaInspectionRow(NamedTuple):
    inspection_id: int
    media_inspection_id: int
    video_name: str
    video_location: str


def _fetch_org_by_id(id: int, api_client: api.AuthenticatedClient) -> api.models.EditOrganisation:
    res = api.endpoints.organisations_retrieve.sync_detailed(id=id, client=api_client)
    api.raise_on_status(res)
    return res.parsed


def connect_to_access_db(filepath: Path) -> jaydebeapi.Connection:
    """
    Connect to an Access DB using the JDBC UCanAccess driver
    :param filepath: Path to the Access DB file
    :return: Connection object
    """
    conn = jaydebeapi.connect(
        jclassname="net.ucanaccess.jdbc.UcanaccessDriver",
        url=f"jdbc:ucanaccess://{filepath};newDatabaseVersion=V2013",
        driver_args=["", ""],
        jars=_JDBC_LIBS,
    )
    return conn


def _get_video_filename_representation(inspection: api.models.InspectionModel) -> str:
    base_file_name = Path(inspection.file["filename"]).stem
    return r".\Video\\" + base_file_name


def _get_material_code(material: str) -> str:
    material = material.lower()
    material = re.sub(r"\s+", "", material)
    return MATERIAL_NAME_TO_CODE.get(material, FALLBACK_MATERIAL_CODE)


def _insert_frame_data(
    inspection: api.models.InspectionModel,
    frame: api.models.FramesList,
    inspection_row_id: int,
    frame_idx: int,
    defects_by_id: dict[int, api.models.AllDefects],
    crsr: jaydebeapi.Cursor,
    conn: jaydebeapi.Connection,
):
    """
    Insert the frame and media data for a single frame into the Access DB

    :param inspection: The inspection the frame belongs to
    :param frame: The frame data to insert
    :param inspection_row_id: The primary key of the inspection row the frame belongs to
    :param frame_idx: The index of the frame in the inspection
    :param crsr: Cursor object for the open connection
    :param conn: Connection object
    """
    # Defect code is required for inserting frames. It is a foreign key to PACP_Condition_Code
    if frame.defect_code is None:
        raise ExportStatusException(ExportStatusReason.MISSING_DATA)

    frame_chainage_number = float(frame.chainage_number) if frame.chainage_number else 0.0
    value_1st = float(frame.quantity_1_value) if frame.quantity_1_value else 0.0
    value_percent = 0.0
    if frame.quantity_1_units == "%":
        value_1st, value_percent = value_percent, value_1st

    value_2nd = float(frame.quantity_2_value) if frame.quantity_2_value else 0.0

    # Eg: S01, S02, ...
    start_continuous_defect_str = "S" + str(frame_idx + 1).zfill(2) if frame.cont_defect_start else ""

    # Default to allowing at_joint if defect info is not available for some reason
    defect_info = defects_by_id.get(frame.defect_id)
    at_joint_allowed = (defect_info.at_joint_required or False) if defect_info else True

    at_joint = (at_joint_allowed and frame.at_joint) or False

    condition_row = ConditionRow(
        inspection_id=inspection_row_id,
        distance=frame_chainage_number,
        pacp_code=frame.defect_code,
        value_1st_dimension=value_1st,
        value_2nd_dimension=value_2nd,
        value_percent=value_percent,
        joint=at_joint,
        clock_at_from=frame.at_clock or 0,
        clock_to=frame.to_clock or 0,
        remarks=frame.remarks or "",
        continuous=start_continuous_defect_str,
    )

    crsr.execute(
        "INSERT INTO PACP_Conditions ( \
            InspectionID, \
            Distance, \
            PACP_Code, \
            Value_1st_Dimension, \
            Value_2nd_Dimension, \
            Value_Percent, \
            Joint, \
            Clock_At_From, \
            Clock_To, \
            Remarks, \
            Continuous \
        ) VALUES (?,?,?,?,?,?,?,?,?,?,?)",
        condition_row,
    )
    conn.commit()
    crsr.execute("SELECT MAX(ConditionID) FROM PACP_Conditions")
    condition_row_id = crsr.fetchone()[0]

    if frame.cont_defect_start:  # Insert duplicate "finish" row for continuous defects
        chainage = inspection.length_surveyed or 0.0
        end_distance = float(frame.cont_defect_end) if frame.cont_defect_end else chainage
        end_continuous_defect_str = "F" + str(frame_idx + 1).zfill(2)

        continuous_end_condition_row = ConditionRow(
            inspection_id=inspection_row_id,
            distance=end_distance,
            pacp_code=frame.defect_code,
            value_1st_dimension=value_1st,
            value_2nd_dimension=value_2nd,
            value_percent=value_percent,
            joint=at_joint,
            clock_at_from=frame.at_clock or 0,
            clock_to=frame.to_clock or 0,
            remarks=frame.remarks or "",
            continuous=end_continuous_defect_str,
        )
        crsr.execute(
            "INSERT INTO PACP_Conditions ( \
                InspectionID, \
                Distance, \
                PACP_Code, \
                Value_1st_Dimension, \
                Value_2nd_Dimension, \
                Value_Percent, \
                Joint, \
                Clock_At_From, \
                Clock_To, \
                Remarks, \
                Continuous \
            ) VALUES (?,?,?,?,?,?,?,?,?,?,?)",
            continuous_end_condition_row,
        )
        conn.commit()

    filename = inspection.file["filename"]
    video_filename = _get_video_filename_representation(inspection)

    media_condition_row = MediaConditionRow(
        condition_id=condition_row_id,
        image_reference=str(frame.frame_id) if frame.frame_id else "",
        image_path=frame.image_location,
        video_file=filename,
        video_file_path=video_filename,
    )

    crsr.execute(
        "INSERT INTO PACP_Media_Conditions ( \
            ConditionID, \
            Image_Reference, \
            Image_Path, \
            Video_File, \
            Video_File_Path \
        ) VALUES (?,?,?,?,?)",
        media_condition_row,
    )
    conn.commit()


def _insert_inspection_data(
    inspection_data: InspectionData, crsr: jaydebeapi.Cursor, conn: jaydebeapi.Connection
) -> int:
    """
    Insert the inspection, ratings, and media data for a single inspection into the Access DB

    :param inspection_data: Inspection data to insert
    :param crsr: Cursor object for the open connection
    :param conn: Connection object

    :return: InspectionID (primary key) of the inserted inspection row
    """
    inspection = inspection_data.inspection

    project = inspection.folder.additional_properties["job_name"] if inspection.folder else "N/A - No folder"
    inspection_date = inspection.date or datetime.datetime(2000, 1, 1)
    if isinstance(inspection_date, datetime.datetime):
        inspection_date = inspection_date.strftime("%Y-%m-%d")

    client_name = inspection_data.target_org_name or ""
    owner_name = inspection_data.upload_org_name or ""

    restricted_direction = (
        inspection.direction
        if inspection.direction in (DirectionEnum.UPSTREAM, DirectionEnum.DOWNSTREAM)
        else DirectionEnum.DOWNSTREAM
    )
    direction = DIRECTION_MAP[StandardEnum.PACP7][restricted_direction]
    if restricted_direction == DirectionEnum.UPSTREAM:
        up_mh = inspection.additional_properties["end_node"] or "end here"
        down_mh = inspection.additional_properties["start_node"] or "start here"
    else:  # Downstream
        up_mh = inspection.additional_properties["start_node"] or "start here"
        down_mh = inspection.additional_properties["end_node"] or "end here"

    pipe_type = inspection.asset.use_of_drain_sewer or "SS"
    diameter = inspection.asset.height_diameter if inspection.asset.height_diameter else 0

    material = _get_material_code(inspection.asset.material or "")

    st = inspection.asset.location_street or "N/A"
    city = inspection.asset.location_town or "N/A"

    vfile = _get_video_filename_representation(inspection)

    chainage = inspection.length_surveyed or 0.0
    asset_id = inspection.asset.asset_id or ""
    legacy_id = inspection.legacy_id or ""

    filename = inspection.file["filename"]

    inspection_row = InspectionRow(
        surveyed_by="Surveyor Name",
        certificate_number="01",
        direction=direction,
        inspection_status="CI",
        inspection_date=inspection_date,
        pre_cleaning="N",
        owner=owner_name,
        customer=client_name,
        project=project,
        height=diameter,
        total_length=chainage,
        length_surveyed=chainage,
        inspection_technology_used_cctv=True,
        pipe_segment_reference=asset_id,
        street=st,
        city=city,
        pipe_use=pipe_type,
        material=material,
        downstream_mh=down_mh,
        upstream_mh=up_mh,
        additional_info=legacy_id,
    )

    crsr.execute(
        """INSERT INTO PACP_Inspections (
        Surveyed_BY,\
        CERTIFICATE_NUMBER,
        Direction,\
        Inspection_Status,\
        Inspection_Date,\
        PreCleaning,
        Owner,\
        Customer,\
        Project,\
        Height,\
        Total_Length,\
        Length_Surveyed,\
        Inspection_Technology_Used_CCTV,\
        Pipe_Segment_Reference,\
        Street,\
        City,\
        Pipe_Use,\
        Material,\
        Downstream_MH,\
        Upstream_MH,\
        Additional_Info) \
        VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)""",
        inspection_row,
    )
    conn.commit()

    crsr.execute("SELECT MAX(InspectionID) FROM PACP_Inspections")
    inspection_row_id = crsr.fetchone()[0]

    rating_row = RatingRow(
        rating_id=inspection_row_id,
        inspection_id=inspection_row_id,
        st_grade_score1=0,
        st_grade_score2=0,
        st_grade_score3=0,
        st_grade_score4=0,
        st_grade_score5=0,
        om_grade_score1=0,
        om_grade_score2=0,
        om_grade_score3=0,
        om_grade_score4=0,
        om_grade_score5=0,
        st_pipe_rating=0,
        om_pipe_rating=0,
        overall_pipe_rating=0,
        st_quick_rating=0,
        om_quick_rating=0,
        pacp_quick_rating=0,
        st_pipe_ratings_index=0,
        om_pipe_ratings_index=0,
        overall_pipe_ratings_index=0,
        lof_pacp=0,
        risk=0,
    )
    crsr.execute(
        "INSERT INTO PACP_Ratings VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
        rating_row,
    )

    media_inspection_row = MediaInspectionRow(
        inspection_id=inspection_row_id,
        media_inspection_id=inspection_row_id,
        video_name=filename,
        video_location=vfile,
    )
    crsr.execute(
        "INSERT INTO PACP_Media_Inspections VALUES (?, ?, ?, ?)",
        media_inspection_row,
    )

    conn.commit()

    return inspection_row_id


def _generate_mdb_export(inspections: list[InspectionData], conn: jaydebeapi.Connection) -> None:
    with conn.cursor() as crsr:
        crsr.execute("DELETE FROM PACP_Ratings")
        crsr.execute("DELETE FROM PACP_Media_Inspections")
        crsr.execute("DELETE FROM PACP_Media_Conditions")
        crsr.execute("DELETE FROM PACP_Conditions")
        crsr.execute("DELETE FROM PACP_Inspections")

    conn.commit()

    with conn.cursor() as crsr:
        for insp in inspections:
            inspection_row_id = _insert_inspection_data(insp, crsr, conn)

            frames_to_add = (f for f in insp.frames if f.defect_code)  # Ignore frames without defects
            for frame_idx, frame in enumerate(frames_to_add):
                _insert_frame_data(insp.inspection, frame, inspection_row_id, frame_idx, insp.defects, crsr, conn)

    conn.commit()

    with conn.cursor() as crsr:
        crsr.execute("UPDATE PACP_Conditions SET Value_1st_Dimension = null WHERE Value_1st_Dimension = 0")
        crsr.execute("UPDATE PACP_Conditions SET Value_2nd_Dimension = null WHERE Value_2nd_Dimension = 0")
        crsr.execute("UPDATE PACP_Conditions SET Clock_At_From = null WHERE Clock_At_From = 0")
        crsr.execute("UPDATE PACP_Conditions SET Clock_To = null WHERE Clock_To = 0")
        crsr.execute("UPDATE PACP_Conditions SET Value_Percent = null WHERE Value_Percent = 0")

    conn.commit()


def _collect_inspection_data(
    org_details: api.models.EditOrganisation,
    inspection_ids: Sequence[uuid.UUID],
    api_client: api.AuthenticatedClient,
    max_retry_secs: int,
) -> list[InspectionData]:
    orgs_by_id: dict[int, api.models.BaseOrganisation] = {org_details.id: org_details}
    orgs_by_id.update({linked.id: linked for linked in org_details.linked_organisations})

    substd_provider = APISubstandardProvider(api_client, max_retry_secs=max_retry_secs)
    defect_provider = APISubStandardAllDefectsProvider(api_client, max_retry_secs=max_retry_secs)

    inspections_with_frames = fetch_inspections_with_frames(
        api_client,
        inspection_ids,
        max_retry_secs=max_retry_secs,
    )

    inspections_data = []

    for pair in inspections_with_frames:
        upload_org = orgs_by_id.get(pair.inspection.file.get("upload_org") or None)
        owner_org = orgs_by_id.get(pair.inspection.file.get("target_org") or None)

        substd = substd_provider.get_by_standard(
            StandardEnum.PACP7.value, PipeTypeEnum(pair.inspection.asset.use_of_drain_sewer)
        )
        defects_list = defect_provider.get_by_substandard(substd.id)
        defects_by_id = {defect.id: defect for defect in defects_list}

        data = InspectionData(
            inspection=pair.inspection,
            frames=pair.frames,
            upload_org_name=upload_org.full_name if upload_org else None,
            target_org_name=owner_org.full_name if owner_org else None,
            defects=defects_by_id,
        )
        inspections_data.append(data)

    return inspections_data


def handler(
    input_data: Input,
    output_fs: AbstractFileSystem,
    settings: Settings,
    api_client: api.AuthenticatedClient,
    max_retry_secs: int = 60,
) -> Output:
    api_client = api_client.with_headers({api.TARGET_ORG_ID_HEADER: str(input_data.target_org_id)})

    try:
        org_details = _fetch_org_by_id(input_data.target_org_id, api_client)
        inspections_data = _collect_inspection_data(
            org_details,
            input_data.payload.inspection_ids,
            api_client,
            max_retry_secs,
        )

        with tempfile.TemporaryDirectory() as temp_dir:
            working_db_filepath = Path(temp_dir) / _COPIED_DB_FILENAME

            shutil.copy(_DB_TEMPLATE_PATH, working_db_filepath)
            with connect_to_access_db(working_db_filepath) as conn:
                _generate_mdb_export(inspections_data, conn)

            output_uuid = str(uuid.uuid4())

            output_db_filepath = (Path(settings.export_outputs_folder_name) / output_uuid).with_suffix(".mdb")
            output_fs.put(str(working_db_filepath), str(output_db_filepath))

            display_name = build_org_name_timestamped_display_name(org_details.short_name, input_data.payload.format)
            output_file = ExportOutputFile(
                file_display_name=display_name,
                file_path=str(output_db_filepath),
                extension=".mdb",
                mime_type="application/vnd.ms-access",
                size=working_db_filepath.stat().st_size,
            )

    except ExportStatusException as e:
        return Output(output_file=e.status_reason)

    return Output(output_file=output_file)
