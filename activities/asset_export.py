import logging
from datetime import datetime
from uuid import UUID

import pandas as pd
from fsspec import AbstractFileSystem
from pydantic import BaseModel
from pydantic_settings import BaseSettings
from vapar.clients import api
from vapar.constants.exports import ExportStatusReason
from vapar.core.exports import AssetExportPayload
from yarl import URL

from common.encrypt_url import combine_urls, encrypt, num_to_sha256
from common.inspections import fetch_inspections_by_id
from common.models import ExportOutputFile, ExportStatusException
from common.reports import build_timestamped_display_name, write_tabular_report

log = logging.getLogger(__name__)


class Settings(BaseSettings):
    vapar_api_base_url: str = "https://www.vapar.solutions/"
    video_play_domain: str = "https://vapar.azure-api.net/assets/playurl/"
    pdf_domain: str = "https://vapar.azure-api.net/assets/assetpdf/"
    image_secret_key_b64encoded: str = ""
    salt_b64encoded: str = ""
    export_outputs_folder_name: str = "export-outputs"


class Input(BaseModel):
    export_id: UUID
    target_org_id: int
    payload: AssetExportPayload
    instance_id: str = ""


class Output(BaseModel):
    output_file: ExportOutputFile | ExportStatusReason


def _build_report_rows(inspections: list[api.models.InspectionModel], settings: Settings) -> pd.DataFrame:
    """
    Build the asset report
    :param inspections: List of inspections
    :return: The report as a DataFrame
    """
    rows = []

    for inspection in inspections:
        sha_code = num_to_sha256(inspection.legacy_id)
        video_play_url = URL(settings.video_play_domain)

        play_url_obj = combine_urls(video_play_url, URL(str(inspection.legacy_id))).update_query(q=sha_code)
        play_url = str(play_url_obj)

        vapar_base_url = URL(settings.vapar_api_base_url)
        survey_url_obj = combine_urls(vapar_base_url, URL(f"inspection/{inspection.uuid}"))
        survey_url = str(survey_url_obj)

        if isinstance(inspection.date, datetime):
            date = datetime.strftime(inspection.date, "%Y-%m-%d") if inspection.date else None
        else:
            date = inspection.date

        pdf_url, assoc_file = None, None
        if inspection.file:
            pdf_uid = encrypt(
                inspection.legacy_id,
                inspection.file["target_org"],
                settings.image_secret_key_b64encoded,
                settings.salt_b64encoded,
            )
            pdf_domain = URL(settings.pdf_domain)
            pdf_url_obj = combine_urls(pdf_domain, URL(pdf_uid))
            pdf_url = str(pdf_url_obj)
            assoc_file = inspection.file["filename"]

        replaced_name = str(
            ", ".join(filter(None, [inspection.asset.location_street, inspection.asset.location_town]))
        ).replace(",", " ")

        if inspection.file["created_time"]:
            created_datetime = datetime.strptime(inspection.file["created_time"], "%Y-%m-%dT%H:%M:%S.%fZ")
        else:
            created_datetime = None

        row = {
            "VAPAR ID": inspection.legacy_id or "",
            "Name": replaced_name or "",
            "Asset No": inspection.asset.asset_id or "",
            "Length": inspection.length_surveyed or 0.0,
            "Structural Grade": inspection.structural_grade or 1,
            "Service Grade": inspection.service_grade or 1,
            "Date Captured": date or "",
            "Diameter": inspection.asset.height_diameter or "",
            "Video File": assoc_file or "",
            "Material": inspection.asset.material or "",
            "Upstream Node": inspection.asset.upstream_node or "",
            "Downstream Node": inspection.asset.downstream_node or "",
            "Dir of Travel": inspection.direction or "",
            "Job Name": inspection.folder["job_name"] if inspection.folder else "N/A - No folder",
            "Work Order": inspection.work_order or "",
            "Upload User": (
                "".join(x[0] for x in inspection.file["upload_user"].split()) if inspection.file["upload_user"] else ""
            ),
            "Upload Date": datetime.strftime(created_datetime, "%Y-%m-%d") if created_datetime else "",
            "Inspection Notes": inspection.general_remarks or "",
            "Video File Size": inspection.file["file_size"] if inspection.file else None,
            "Folder Path": inspection.folder.additional_properties["path"] if inspection.folder else "N/A - No folder",
            "Workflow Status": inspection.status or "",
            "Video Play Link": play_url or "",
            "PDF Link": pdf_url or "",
            "Survey Link": survey_url or "",
            "Repair Completed Date": inspection.asset.year_renewed or "",
            "Survey Reviewer ID": (
                "".join([x[0].upper() for x in filter(None, inspection.reviewed_by.split(" "))])
                if inspection.reviewed_by
                else ""
            ),
        }
        rows.append(row)

    df = pd.DataFrame(
        rows,
        columns=[
            "VAPAR ID",
            "Name",
            "Asset No",
            "Length",
            "Structural Grade",
            "Service Grade",
            "Date Captured",
            "Diameter",
            "Video File",
            "Material",
            "Upstream Node",
            "Downstream Node",
            "Dir of Travel",
            "Job Name",
            "Work Order",
            "Upload User",
            "Upload Date",
            "Inspection Notes",
            "Video File Size",
            "Folder Path",
            "Workflow Status",
            "Video Play Link",
            "PDF Link",
            "Survey Link",
            "Repair Completed Date",
            "Survey Reviewer ID",
        ],
    )
    df = df.astype(
        {
            "VAPAR ID": str,
            "Name": str,
            "Asset No": str,
            "Length": float,
            "Structural Grade": int,
            "Service Grade": int,
            "Date Captured": str,
            "Diameter": str,
            "Video File": str,
            "Material": str,
            "Upstream Node": str,
            "Downstream Node": str,
            "Dir of Travel": str,
            "Job Name": str,
            "Work Order": str,
            "Upload User": str,
            "Upload Date": str,
            "Inspection Notes": str,
            "Video File Size": str,
            "Folder Path": str,
            "Workflow Status": str,
            "Video Play Link": str,
            "PDF Link": str,
            "Survey Link": str,
            "Repair Completed Date": str,
            "Survey Reviewer ID": str,
        }
    )
    return df


def handler(
    input_data: Input,
    settings: Settings,
    api_client: api.AuthenticatedClient,
    output_fs: AbstractFileSystem,
    max_retry_secs: int = 60,
) -> Output:
    log.info(
        "Starting AssetExport activity",
        extra={
            "export_id": input_data.export_id,
            "target_org_id": input_data.target_org_id,
            "instance_id": input_data.instance_id,
        },
    )
    api_client = api_client.with_headers({api.TARGET_ORG_ID_HEADER: str(input_data.target_org_id)})

    if len(input_data.payload.inspection_ids) == 0:
        raise ValueError("No inspection IDs provided in the export payload")

    try:
        inspections = fetch_inspections_by_id(
            api_client, input_data.payload.inspection_ids, max_retry_secs=max_retry_secs
        )

        report_df = _build_report_rows(inspections, settings)
        display_name = build_timestamped_display_name("VS_Assets", input_data.payload.format)
        output_file = write_tabular_report(
            report_df,
            output_fs,
            input_data.payload.format,
            folder=settings.export_outputs_folder_name,
            file_display_name=display_name,
        )

    except ExportStatusException as e:
        return Output(output_file=e.status_reason)

    return Output(output_file=output_file)
