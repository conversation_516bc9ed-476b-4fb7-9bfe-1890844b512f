import datetime
import logging
from collections.abc import Iterable, Sequence
from typing import <PERSON><PERSON><PERSON><PERSON>
from uuid import UUID

import pandas as pd
from fsspec import AbstractFileSystem
from pydantic import BaseModel
from vapar.clients import api
from vapar.constants.exports import ExportStatusReason
from vapar.core.exports import PredictorExportPayload

from common.defects import APISubStandardAllDefectsProvider
from common.inspections import fetch_inspections_with_frames
from common.models import ExportOutputFile, ExportStatusException
from common.reports import build_timestamped_display_name, write_tabular_report
from common.standards import APISubstandardProvider

log = logging.getLogger(__name__)


class Input(BaseModel):
    export_id: UUID
    target_org_id: int
    payload: PredictorExportPayload
    instance_id: str = ""


class Output(BaseModel):
    output_file: ExportOutputFile | ExportStatusReason


class Settings(BaseModel):
    export_outputs_folder_name: str = "export-outputs"


class InspectionData(NamedTuple):
    inspection: api.models.InspectionModel
    frames: list[api.models.FramesList]
    substandard: api.models.StandardSubcategory
    defects: list[api.models.AllDefects]


def _build_report_row(
    inspection_data: InspectionData,
    all_defects_by_description: dict[str, api.models.AllDefects],
):
    inspection = inspection_data.inspection
    frames = inspection_data.frames

    # Build a summed score per defect type
    score_totals = {defect.defect_description: 0 for defect in all_defects_by_description.values()}
    for frame in frames:
        if frame.defect_class not in all_defects_by_description:
            continue
        str_score = int(frame.defect_str_score or 0)
        ser_score = int(frame.defect_ser_score or 0)
        score_totals[frame.defect_class] += max(str_score, ser_score)

    start_node = inspection.additional_properties["start_node"]
    end_node = inspection.additional_properties["end_node"]

    if isinstance(inspection.date, datetime.datetime):
        date_captured = inspection.date.strftime("%Y-%m-%d")
    elif not inspection.date:  # None or Unset
        date_captured = ""
    else:
        date_captured = str(inspection.date)

    row = {
        "id": inspection.legacy_id,
        "Name": inspection.asset.location_street or "",
        "Asset No": inspection.asset.asset_id or "",
        "Chainage": inspection.length_surveyed or 0.0,
        "Structural Grade": inspection.structural_grade or 1,
        "Service Grade": inspection.service_grade or 1,
        "Date Captured": date_captured or "",
        "Diameter": inspection.asset.height_diameter or "",
        "Video File": inspection.file["filename"],
        "Material": inspection.asset.material or "",
        "Start Node": start_node or "",
        "End Node": end_node or "",
        "Dir of travel": inspection.direction or "",
        **score_totals,
    }
    return row


def _build_report_rows(
    inspections_data: Iterable[InspectionData],
) -> pd.DataFrame:
    """
    Build the defect predictor report
    :param inspections_data: The inspections data to build the report from
    :return: The report as a DataFrame
    """

    all_defects_by_description = {}
    seen_substandard_ids = set()
    for insp in inspections_data:
        if insp.substandard.id not in seen_substandard_ids:
            seen_substandard_ids.add(insp.substandard.id)
            all_defects_by_description.update({d.defect_description: d for d in insp.defects})

    df = pd.DataFrame(
        map(lambda insp: _build_report_row(insp, all_defects_by_description), inspections_data),
        columns=[
            "id",
            "Name",
            "Asset No",
            "Chainage",
            "Structural Grade",
            "Service Grade",
            "Date Captured",
            "Diameter",
            "Video File",
            "Material",
            "Start Node",
            "End Node",
            "Dir of travel",
            *all_defects_by_description.keys(),
        ],
    )
    df = df.astype(
        {
            "id": str,
            "Name": str,
            "Asset No": str,
            "Chainage": float,
            "Structural Grade": int,
            "Service Grade": int,
            "Date Captured": str,
            "Diameter": str,
            "Video File": str,
            "Material": str,
            "Start Node": str,
            "End Node": str,
            "Dir of travel": str,
            **{k: int for k in all_defects_by_description},
        }
    )
    return df


def fetch_inspections_data(
    api_client: api.AuthenticatedClient,
    inspection_ids: Sequence[UUID],
    max_retry_secs: int = 60,
) -> list[InspectionData]:
    inspections_with_frames = fetch_inspections_with_frames(
        api_client,
        inspection_ids,
        reported_frames_only=True,
        max_retry_secs=max_retry_secs,
    )

    substandard_provider = APISubstandardProvider(api_client, max_retry_secs=max_retry_secs)
    defect_provider = APISubStandardAllDefectsProvider(api_client, max_retry_secs=max_retry_secs)

    inspections_data = []
    for pair in inspections_with_frames:
        substd = substandard_provider.get_by_standard(
            pair.inspection.standard, pair.inspection.asset.use_of_drain_sewer
        )
        if not substd:
            raise ValueError(
                f"Could not find substandard for standard {pair.inspection.standard}"
                f" and pipe type {pair.inspection.asset.use_of_drain_sewer}"
            )
        all_defects = defect_provider.get_by_substandard(substd.id)

        data = InspectionData(pair.inspection, pair.frames, substd, all_defects)
        inspections_data.append(data)

    return inspections_data


def handler(
    input_data: Input,
    settings: Settings,
    api_client: api.AuthenticatedClient,
    output_fs: AbstractFileSystem,
    max_retry_secs: int = 60,
) -> Output:
    log.info(
        "Starting DefectPredictorExport activity",
        extra={
            "export_id": input_data.export_id,
            "target_org_id": input_data.target_org_id,
            "instance_id": input_data.instance_id,
        },
    )
    api_client = api_client.with_headers({api.TARGET_ORG_ID_HEADER: str(input_data.target_org_id)})

    try:
        if len(input_data.payload.inspection_ids) == 0:
            raise ExportStatusException(ExportStatusReason.INVALID_INSPECTION_ID)
        inspections_data = fetch_inspections_data(api_client, input_data.payload.inspection_ids, max_retry_secs)

        report_df = _build_report_rows(inspections_data)
        display_name = build_timestamped_display_name("VS_Predictor", input_data.payload.format)
        output_file = write_tabular_report(
            report_df,
            output_fs,
            input_data.payload.format,
            folder=settings.export_outputs_folder_name,
            file_display_name=display_name,
        )

    except ExportStatusException as e:
        return Output(output_file=e.status_reason)

    return Output(output_file=output_file)
