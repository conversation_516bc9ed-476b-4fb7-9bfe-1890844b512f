"""
Handle exports for the Autodesk Info360 Asset platform.
"""

import logging
import re
from collections.abc import Sequence
from typing import NamedTuple
from uuid import UUID

import pandas as pd
from fsspec import AbstractFileSystem
from pydantic import BaseModel
from pydantic_settings import BaseSettings
from vapar.clients import api
from vapar.constants.exports import ExportStatusReason, InfoAssetExportType
from vapar.constants.pipes import StandardEnum
from vapar.core.exports import InfoAssetExportPayload

from common.inspections import fetch_inspections_with_frames
from common.models import ExportOutputFile, ExportStatusException
from common.reports import write_tabular_report
from common.standards import APISubstandardProvider

log = logging.getLogger(__name__)


class Input(BaseModel):
    export_id: UUID
    target_org_id: int
    instance_id: str = ""
    payload: InfoAssetExportPayload


class Output(BaseModel):
    output_file: ExportOutputFile | ExportStatusReason


class Settings(BaseSettings):
    export_outputs_folder_name: str = "export-outputs"


class InspectionData(NamedTuple):
    inspection: api.models.InspectionModel
    frames: list[api.models.FramesList]
    standard_subcategory: api.models.StandardSubcategory
    upload_org_name: str | None = None
    target_org_name: str | None = None


def _fetch_org_by_id(id: int, api_client: api.AuthenticatedClient) -> api.models.EditOrganisation:
    res = api.endpoints.organisations_retrieve.sync_detailed(id=id, client=api_client)
    api.raise_on_status(res)
    return res.parsed


def _collect_inspection_data(
    org_id: int,
    inspection_ids: Sequence[UUID],
    api_client: api.AuthenticatedClient,
    max_retry_secs: int,
) -> list[InspectionData]:
    org = api.with_retries(max_retry_secs)(_fetch_org_by_id)(org_id, api_client)
    orgs_by_id: dict[int, api.models.BaseOrganisation] = {org.id: org}
    orgs_by_id.update({linked.id: linked for linked in org.linked_organisations})

    standard_provider = APISubstandardProvider(api_client)

    inspections_with_frames = fetch_inspections_with_frames(
        api_client,
        inspection_ids,
        max_retry_secs=max_retry_secs,
    )

    inspections_data = []
    for pair in inspections_with_frames:
        standard_subcategory = standard_provider.get_by_standard(
            pair.inspection.standard, pair.inspection.asset.use_of_drain_sewer
        )
        upload_org = orgs_by_id.get(pair.inspection.file.get("upload_org") or None)
        owner_org = orgs_by_id.get(pair.inspection.file.get("target_org") or None)
        data = InspectionData(
            inspection=pair.inspection,
            frames=pair.frames,
            upload_org_name=upload_org.full_name if upload_org else None,
            target_org_name=owner_org.full_name if owner_org else None,
            standard_subcategory=standard_subcategory,
        )
        inspections_data.append(data)

    return inspections_data


def _format_direction(direction: api.models.DirectionEnum) -> str:
    return "D" if direction == api.models.DirectionEnum.DOWNSTREAM else "U"


def _format_material(material: str):
    return re.sub(r"[^a-zA-Z]", "", material)


def _format_diameter(diameter: str) -> str:
    return re.sub(r"[^0-9]", "", diameter)


def _build_infoasset_id(inspection: api.models.InspectionModel) -> str:
    folder_name = inspection.folder.additional_properties.get("job_name", "")
    vapar_id = inspection.legacy_id or ""
    return f"{folder_name}-{vapar_id}"


def _build_asset_report_rows(inspections: list[InspectionData]) -> pd.DataFrame:
    column_types = {
        "id": str,
        "id_flag": str,
        "us_node_id_flag": str,
        "ds_node_id_flag": str,
        "plr_flag": str,
        "when_surveyed_flag": str,
        "surveyed_flag_length": str,
        "material_flag": str,
        "link_suffix": int,
        "direction_flag": str,
        "road_name_flag": str,
        "place_name_flag": str,
        "start_manhole_flag": str,
        "finish_manhole_flag": str,
        "direction": str,
        "us_node_id": str,
        "ds_node_id": str,
        "start_manhole": str,
        "finish_manhole": str,
        "plr": str,
        "when_surveyed": str,
        "road_name": str,
        "place_name": str,
        "surveyed_length": float,
        "anticipated_length_flag": str,
        "material": str,
        "shape": str,
        "shape_flag": str,
        "size_1": str,
        "size_1_flag": str,
        "size_2": str,
        "size_2_flag": str,
        "hard_wired_service_grade": int,
        "hard_wired_service_grade_flag": str,
        "hard_wired_structural_grade": int,
        "hard_wired_structural_grade_flag": str,
        "mean_score": float,
        "mean_score_flag": str,
        "peak_score": int,
        "peak_score_flag": str,
        "total_score": int,
        "total_score_flag": str,
        "service_mean_score": float,
        "service_mean_score_flag": str,
        "service_peak_score": int,
        "service_peak_score_flag": str,
        "service_total_score": int,
        "service_total_score_flag": str,
    }

    rows = []
    for insp in inspections:
        inspection = insp.inspection
        asset = inspection.asset

        total_structural_score = sum(int(frame.defect_str_score) for frame in insp.frames if frame.defect_str_score)
        total_service_score = sum(int(frame.defect_ser_score) for frame in insp.frames if frame.defect_ser_score)
        peak_structural_score = max(
            (int(frame.defect_str_score) for frame in insp.frames if frame.defect_str_score), default=0
        )
        peak_service_score = max(
            (int(frame.defect_ser_score) for frame in insp.frames if frame.defect_ser_score), default=0
        )

        mean_structural_score = (
            (float(total_structural_score) / inspection.length_surveyed) if inspection.length_surveyed else 0.0
        )
        mean_service_score = (
            (float(total_service_score) / inspection.length_surveyed) if inspection.length_surveyed else 0.0
        )

        row = {
            "id": _build_infoasset_id(insp.inspection),
            "id_flag": "SURV",
            "us_node_id_flag": "SURV",
            "ds_node_id_flag": "SURV",
            "plr_flag": "SURV",
            "when_surveyed_flag": "SURV",
            "surveyed_flag_length": "SURV",
            "material_flag": "SURV",
            "link_suffix": 1,
            "direction_flag": "JP",
            "road_name_flag": "JP",
            "place_name_flag": "JP",
            "start_manhole_flag": "JP",
            "finish_manhole_flag": "JP",
            "direction": _format_direction(inspection.direction),
            "us_node_id": asset.upstream_node or "",
            "ds_node_id": asset.downstream_node or "",
            "start_manhole": inspection.additional_properties.get("start_node", ""),
            "finish_manhole": inspection.additional_properties.get("end_node", ""),
            "plr": asset.asset_id or "",
            "when_surveyed": inspection.date or "",
            "road_name": asset.location_street or "",
            "place_name": asset.location_town or "",
            "surveyed_length": inspection.length_surveyed or 0.0,
            "anticipated_length_flag": "#D",
            "material": _format_material(asset.material or ""),
            "shape": "CP",
            "shape_flag": "SURV",
            "size_1": "0",
            "size_1_flag": "JP",
            "size_2": str(asset.height_diameter or ""),
            "size_2_flag": "JP",
            "hard_wired_service_grade": inspection.service_grade or 1,
            "hard_wired_service_grade_flag": "SURV",
            "hard_wired_structural_grade": inspection.structural_grade or 1,
            "hard_wired_structural_grade_flag": "SURV",
            "mean_score": round(mean_structural_score, 2),
            "mean_score_flag": "SURV",
            "peak_score": peak_structural_score,
            "peak_score_flag": "SURV",
            "total_score": total_structural_score,
            "total_score_flag": "SURV",
            "service_mean_score": round(mean_service_score, 2),
            "service_mean_score_flag": "SURV",
            "service_peak_score": peak_service_score,
            "service_peak_score_flag": "SURV",
            "service_total_score": total_service_score,
            "service_total_score_flag": "SURV",
        }
        rows.append(row)

    df = pd.DataFrame(rows, columns=list(column_types.keys()))
    df = df.astype(column_types).mask(df.isnull(), None)

    return df


def _get_defect_size(defect_code: str, standard: StandardEnum) -> str:
    if standard not in (StandardEnum.NZ_PIPE_MANUAL_3, StandardEnum.NZ_PIPE_MANUAL_4):
        return ""

    size = defect_code[-1] if len(defect_code) > 2 else ""
    if size not in ("S", "M", "L"):
        return ""
    return size


def _format_defect_code(defect_code: str, standard: StandardEnum) -> str:
    if standard not in (StandardEnum.NZ_PIPE_MANUAL_3, StandardEnum.NZ_PIPE_MANUAL_4):
        return defect_code
    if defect_code == "CN":
        return "LO"
    return defect_code


def _build_defect_report_rows(inspections: list[InspectionData]) -> pd.DataFrame:
    column_types = {
        "id": str,
        "distance": float,
        "code": str,
        "remarks": str,
        "charecterisation1": str,
    }

    rows = []
    for insp in inspections:
        frames_to_add = (frame for frame in insp.frames if frame.defect_code and frame.defect_code not in ("ST", "LOV"))
        for i, frame in enumerate(frames_to_add):
            standard = StandardEnum(insp.standard_subcategory.standard.display_name)
            is_first_defect = i == 0
            row = {
                "id": _build_infoasset_id(insp.inspection) if is_first_defect else "",
                "distance": float(frame.chainage_number or 0.0),
                "code": _format_defect_code(frame.defect_code, standard),
                "remarks": frame.remarks or "",
                "charecterisation1": _get_defect_size(frame.defect_code, standard),
            }
            rows.append(row)

    df = pd.DataFrame(rows, columns=list(column_types.keys()))
    df = df.astype(column_types).mask(df.isnull(), None)
    return df


def handler(
    input_data: Input,
    settings: Settings,
    api_client: api.AuthenticatedClient,
    output_fs: AbstractFileSystem,
    max_retry_secs: int = 60,
) -> Output:
    log.info(
        "Starting InfoAssetExport activity",
        extra={
            "export_id": input_data.export_id,
            "target_org_id": input_data.target_org_id,
            "instance_id": input_data.instance_id,
        },
    )
    api_client = api_client.with_headers({api.TARGET_ORG_ID_HEADER: str(input_data.target_org_id)})

    try:
        if len(input_data.payload.inspection_ids) == 0:
            raise ExportStatusException(ExportStatusReason.INVALID_INSPECTION_ID)

        inspections = _collect_inspection_data(
            input_data.target_org_id,
            input_data.payload.inspection_ids,
            api_client,
            max_retry_secs,
        )

        if input_data.payload.info_asset_export_type == InfoAssetExportType.ASSET:
            report_df = _build_asset_report_rows(inspections)
            display_name = "InfoAssetManagerCSV_Assets.csv"
        elif input_data.payload.info_asset_export_type == InfoAssetExportType.DEFECT:
            report_df = _build_defect_report_rows(inspections)
            display_name = "InfoAssetManagerCSV_Defects.csv"
        else:
            raise ValueError(f"Unsupported InfoAssetExportType: {input_data.payload.info_asset_export_type}")

        output_file = write_tabular_report(
            report_df,
            output_fs,
            input_data.payload.format,
            folder=settings.export_outputs_folder_name,
            file_display_name=display_name,
        )

    except ExportStatusException as e:
        return Output(output_file=e.status_reason)

    return Output(output_file=output_file)
