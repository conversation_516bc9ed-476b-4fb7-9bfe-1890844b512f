import base64
import hashlib

from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from yarl import URL


def num_to_sha256(num: int) -> str:
    sentence = str(num)
    result = hashlib.sha256(sentence.encode())
    return result.hexdigest()


def encrypt(entity_id: str, organization_pk: int, image_secret_key_b64: str, salt_b64: str) -> str:
    secret = base64.b64decode(image_secret_key_b64)
    salt = base64.b64decode(salt_b64)
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    key = base64.urlsafe_b64encode(kdf.derive(secret))
    f = Fernet(key)
    msg = str(entity_id) + ":" + str(organization_pk)
    msg = bytes(msg, encoding="utf-8")
    token = f.encrypt(msg)
    token = token.decode("utf-8")
    return token


def combine_urls(base_url: URL, extra_component: URL) -> URL:
    """
    Combine a base url with a path fragment.

    Both the base url and the extra component can have query parameters.
    """
    full_url = (base_url / str(extra_component)).with_query({**base_url.query, **extra_component.query})
    return full_url
