"""
Shared data fetching logic for inspections and frames
"""

import logging
from collections import defaultdict
from collections.abc import Iterable, Sequence
from itertools import islice
from typing import NamedTuple, TypeVar
from uuid import UUID

from vapar.clients import api
from vapar.constants.exports import ExportStatusReason

from common.models import ExportStatusException

INSPECTIONS_PAGE_SIZE = 500
FRAMES_PAGE_SIZE = 1000
QUERY_BATCH_SIZE = 200

log = logging.getLogger(__name__)

T = TypeVar("T")


def _batched(iterable: Iterable[T], n: int) -> Iterable[list[T]]:
    """
    Yield n-sized chunks from sequence
    """
    it = iter(iterable)
    while chunk := list(islice(it, n)):
        yield chunk


class InspectionWithFrames(NamedTuple):
    inspection: api.models.InspectionModel
    frames: list[api.models.FramesList]


def fetch_inspections_by_id(
    client: api.AuthenticatedClient,
    inspection_ids: Sequence[UUID],
    error_on_missing: bool = True,
    max_retry_secs: int = 60,
) -> list[api.models.InspectionModel]:
    """
    Fetch inspections in bulk by a sequence of inspection IDs.
    """
    if not inspection_ids:
        return []

    inspections = []
    for id_batch in _batched(inspection_ids, QUERY_BATCH_SIZE):
        ids_param = ",".join(str(insp_id) for insp_id in id_batch)
        page_number = 1
        while page_number is not None:
            res = api.with_retries(max_retry_secs)(api.endpoints.inspections2_list.sync_detailed)(
                client=client,
                page=page_number,
                page_size=INSPECTIONS_PAGE_SIZE,
                uuid_in=ids_param,
                use_header_names=True,
                use_inspection_filters=False,
            )
            api.raise_on_status(res)
            inspections.extend(res.parsed.results)
            page_number = page_number + 1 if res.parsed.next_ else None
            log.info(f"Loaded {len(res.parsed.results)} inspections")

    if error_on_missing and len(inspections) != len(set(inspection_ids)):
        raise ExportStatusException(ExportStatusReason.INVALID_INSPECTION_ID)

    return inspections


def fetch_frames_by_multiple_video_ids(
    client: api.AuthenticatedClient,
    video_ids: Sequence[int],
    reported_frames_only: bool = True,
    max_retry_secs: int = 60,
) -> list[api.models.FramesList]:
    """
    Bulk fetch frames for multiple videos, by video ID.
    """
    if not video_ids:
        return []

    frames = []
    for video_id_batch in _batched(video_ids, QUERY_BATCH_SIZE):
        video_batch_str = ",".join(str(vid) for vid in video_id_batch)
        page_number = 1
        while page_number is not None:
            res = api.with_retries(max_retry_secs)(api.endpoints.inspections_frames_list.sync_detailed)(
                client=client,
                page=page_number,
                page_size=FRAMES_PAGE_SIZE,
                reported_frames=reported_frames_only,
                parent_video_in=video_batch_str,
            )
            api.raise_on_status(res)
            frames.extend(res.parsed.results)
            page_number = page_number + 1 if res.parsed.next_ else None
            log.info(f"Loaded {len(res.parsed.results)} frames")

    return frames


def fetch_inspections_with_frames(
    client: api.AuthenticatedClient,
    inspection_ids: Sequence[UUID],
    reported_frames_only: bool = True,
    error_on_missing: bool = True,
    max_retry_secs: int = 60,
) -> list[InspectionWithFrames]:
    """
    Fetch inspections and their frames in bulk from the API.

    We split frames and inspections into batches to avoid hitting query string length limits
    """

    if not inspection_ids:
        return []

    inspections = fetch_inspections_by_id(client, inspection_ids, error_on_missing, max_retry_secs)
    video_ids = [insp.file["id"] for insp in inspections]
    frames = fetch_frames_by_multiple_video_ids(client, video_ids, reported_frames_only, max_retry_secs)

    vid_id_to_frames = defaultdict(list)
    for frame in frames:
        vid_id_to_frames[frame.parent_video].append(frame)

    combined = [
        InspectionWithFrames(
            inspection=insp,
            frames=vid_id_to_frames[insp.file["id"]],
        )
        for insp in inspections
    ]
    return combined
