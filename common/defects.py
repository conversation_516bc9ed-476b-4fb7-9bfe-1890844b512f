import abc
from collections.abc import MutableMapping

import cachetools
from vapar.clients import api


def _fetch_all_defects_by_substandard(
    substandard_id: int, api_client: api.AuthenticatedClient
) -> list[api.models.AllDefects]:
    res = api.endpoints.standards_defects_list.sync_detailed(
        sub_standard_id=substandard_id, is_shown=True, client=api_client
    )
    api.raise_on_status(res)
    return res.parsed


class SubStandardAllDefectsProvider(abc.ABC):
    """
    An interface for retrieving the full list of defects for a specific standard subcategory
    """

    @abc.abstractmethod
    def get_by_substandard(self, standard_subcategory_id: int) -> list[api.models.AllDefects]:
        """
        Retrieve the full list of defects for a given standard subcategory ID. Returns None if the defects are not
        found.

        :param standard_subcategory_id: The standard subcategory ID to retrieve details for
        :return: A defect's details object or None if not found
        """
        raise NotImplementedError


SubStandardDefectCache = MutableMapping[int, list[api.models.AllDefects]]


class APISubStandardAllDefectsProvider(SubStandardAllDefectsProvider):
    _GLOBAL_DEFECT_CACHE = cachetools.TTLCache(maxsize=64, ttl=10 * 60)

    def __init__(
        self,
        api_client: api.AuthenticatedClient,
        cache: SubStandardDefectCache | None = None,
        max_retry_secs: int = 60,
    ):
        """
        :param api_client: The client to use when interacting with the Vapar API
        :param cache: A map of the form standard_subcategory_id -> list[AllDefects]
        :param max_retry_secs: The maximum number of seconds to retry api calls on failure
        """

        def fetcher_fn(standard_subcategory_id: int) -> list[api.models.AllDefects]:
            return _fetch_all_defects_by_substandard(standard_subcategory_id, api_client)

        self._fetcher_fn = api.with_retries(max_retry_secs)(fetcher_fn)
        self._cache = self._GLOBAL_DEFECT_CACHE if cache is None else cache

    def get_by_substandard(self, standard_subcategory_id: int) -> list[api.models.AllDefects]:
        if (substandard_defects := self._cache.get(standard_subcategory_id)) is not None:
            return substandard_defects
        substandard_defects = self._fetcher_fn(standard_subcategory_id)
        self._cache[standard_subcategory_id] = substandard_defects

        return substandard_defects
