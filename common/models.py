from datetime import datetime
from uuid import UUID

from pydantic import BaseModel
from vapar.constants.exports import ExportFormat, ExportStatus, ExportStatusReason, ExportType
from vapar.constants.imports import ImportStatusEnum, ImportStatusReasonEnum, ImportTypeEnum
from vapar.core.imports import AnyImportPayload


class ExportStatusException(Exception):
    status_reason: ExportStatusReason

    def __init__(self, status_reason: ExportStatusReason):
        self.status_reason = status_reason
        super().__init__(status_reason.name)


class ExportDetails(BaseModel):
    """Basic read-only details about the state of an export"""

    status: ExportStatus
    status_reason: ExportStatusReason | None = None
    type: ExportType
    format: ExportFormat
    created_at: datetime
    updated_at: datetime
    completed_at: datetime | None = None


class ExportOutputFile(BaseModel):
    file_path: str  # Full path, including container name if an Azure Blob
    file_display_name: str | None = None
    size: int
    mime_type: str
    extension: str


class ImportDetails(BaseModel):
    """Basic read-only details about the state of an import"""

    id: UUID
    payload: AnyImportPayload
    status: ImportStatusEnum
    status_reason: ImportStatusReasonEnum | None = None
    type: ImportTypeEnum
    created_at: datetime
    updated_at: datetime
    completed_at: datetime | None = None


class ImportInputFile(BaseModel):
    file_path: str  # Full path, including container name if an Azure Blob
    size: int
    mime_type: str
    extension: str
