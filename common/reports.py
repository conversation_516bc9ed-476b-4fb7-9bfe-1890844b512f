"""
Utilities for outputting export reports.
"""

import logging
import uuid
from datetime import datetime
from pathlib import Path

import pandas as pd
from fsspec import AbstractFileSystem
from vapar.constants.exports import ExportFormat

from common.models import ExportOutputFile

log = logging.getLogger(__name__)

FORMAT_TO_EXTENSION = {
    ExportFormat.CSV: ".csv",
    ExportFormat.XML: ".xml",
    ExportFormat.PDF: ".pdf",
    ExportFormat.ZIP: ".zip",
    ExportFormat.MDB: ".mdb",
    ExportFormat.SQLITE: ".db3",
}

FORMAT_TO_MIMETYPE = {
    ExportFormat.CSV: "text/csv",
    ExportFormat.XML: "text/xml",
    ExportFormat.PDF: "application/pdf",
    ExportFormat.ZIP: "application/zip",
    ExportFormat.MDB: "application/vnd.ms-access",
    ExportFormat.SQLITE: "application/vnd.sqlite3",
}


def build_org_name_timestamped_display_name(
    org_name: str, export_format: ExportFormat, curr_time: datetime | None = None
):
    curr_time = curr_time or datetime.now()
    extension = FORMAT_TO_EXTENSION.get(export_format, "")
    timestamp = curr_time.strftime("%Y-%m-%d_%H-%M-%S")

    display_name = f"{org_name}_{timestamp}{extension}"
    display_name = display_name.replace("/", "_")
    return display_name


def build_timestamped_display_name(
    report_short_name: str, export_format: ExportFormat, curr_time: datetime | None = None
) -> str:
    curr_time = curr_time or datetime.now()
    extension = FORMAT_TO_EXTENSION.get(export_format, "")
    timestamp = curr_time.strftime("%Y-%m-%d_%H-%M-%S")
    return f"{report_short_name} - {timestamp}{extension}"


def write_tabular_report(
    report_df: pd.DataFrame,
    output_fs: AbstractFileSystem,
    output_format: ExportFormat,
    folder: str | None = None,
    file_display_name: str | None = None,
) -> ExportOutputFile:
    extension = FORMAT_TO_EXTENSION.get(output_format, "")
    mime_type = FORMAT_TO_MIMETYPE.get(output_format, "application/octet-stream")
    report_uuid = uuid.uuid4()
    filename = f"{report_uuid}{extension}"
    if folder:
        filename = str(Path(folder) / filename)
    with output_fs.open(filename, "wb") as f:
        if output_format == ExportFormat.CSV:
            report_df.to_csv(f, index=False)
        elif output_format == ExportFormat.XML:
            report_df.to_xml(f, index=False)
        else:
            raise ValueError(f"Unsupported output format: {output_format}")

    size = output_fs.size(filename)

    return ExportOutputFile(
        file_path=filename,
        size=size,
        mime_type=mime_type,
        extension=extension,
        file_display_name=file_display_name,
    )


def write_textual_report(
    report_text: str,
    output_fs: AbstractFileSystem,
    output_format: ExportFormat,
    folder: str | None = None,
    file_display_name: str | None = None,
) -> ExportOutputFile:
    extension = FORMAT_TO_EXTENSION.get(output_format, "")
    mime_type = FORMAT_TO_MIMETYPE.get(output_format, "text/plain")
    report_uuid = uuid.uuid4()
    filename = f"{report_uuid}{extension}"
    if folder:
        filename = str(Path(folder) / filename)
    with output_fs.open(filename, "wb") as f:  # Note: adlfs does not support 'w' mode
        f.write(report_text.encode())

    size = output_fs.size(filename)

    return ExportOutputFile(
        file_path=filename,
        size=size,
        mime_type=mime_type,
        extension=extension,
        file_display_name=file_display_name,
    )
