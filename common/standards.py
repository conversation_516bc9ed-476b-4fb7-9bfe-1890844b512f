import abc
from collections.abc import MutableMapping

import cachetools
from vapar.clients import api
from vapar.constants.pipes import PipeT<PERSON><PERSON>num


def _fetch_subcategory_by_standard(
    standard_display_name: str, pipe_type: PipeTypeEnum, api_client: api.AuthenticatedClient
) -> api.models.StandardSubcategory | None:
    is_sewer = pipe_type == PipeTypeEnum.SEWER
    res = api.endpoints.standards_subcategories_list.sync_detailed(
        standard_key_display_name=standard_display_name,
        pipe_type_sewer=is_sewer,
        material_type="Rigid",
        client=api_client,
    )
    api.raise_on_status(res)
    return res.parsed.results[0] if res.parsed.results else None


def _fetch_standard_by_display_name(
    display_name: str, api_client: api.AuthenticatedClient
) -> api.models.Standard | None:
    res = api.endpoints.standards_list.sync_detailed(client=api_client)
    api.raise_on_status(res)
    standards = res.parsed.results or []
    # Note: display_name is not mapped by the client correctly because the field isn't returned in camelCase as expected
    return next((std for std in standards if std.additional_properties.get("display_name") == display_name), None)


class SubStandardProvider(abc.ABC):
    """
    An interface for retrieving the details of a standard subcategory
    """

    @abc.abstractmethod
    def get_by_standard(
        self, standard_display_name: int, pipe_type: PipeTypeEnum
    ) -> api.models.StandardSubcategory | None:
        """
        Retrieve the details of a standard subcategory by standard name and pipe type.

        :param standard_display_name: The standard name to retrieve details for
        :param pipe_type: The pipe type to retrieve details for
        :return: A subcategory's details object or None if not found
        """
        raise NotImplementedError


SubStandardCache = MutableMapping[tuple[str, PipeTypeEnum], api.models.StandardSubcategory]


class APISubstandardProvider(SubStandardProvider):
    _GLOBAL_SUBSTANDARD_CACHE = cachetools.TTLCache(maxsize=64, ttl=10 * 60)

    def __init__(
        self,
        api_client: api.AuthenticatedClient,
        cache: SubStandardCache | None = None,
        max_retry_secs: int = 60,
    ):
        def fetcher_fn(name: str, pipe_type: PipeTypeEnum) -> api.models.StandardSubcategory | None:
            return _fetch_subcategory_by_standard(name, pipe_type, api_client)

        self._cache = self._GLOBAL_SUBSTANDARD_CACHE if cache is None else cache
        self._fetcher_fn = api.with_retries(max_retry_secs)(fetcher_fn)

    def get_by_standard(
        self, standard_display_name: str, pipe_type: PipeTypeEnum
    ) -> api.models.StandardSubcategory | None:
        key = (standard_display_name, pipe_type)
        if (substandard := self._cache.get(key)) is not None:
            return substandard
        substandard = self._fetcher_fn(standard_display_name, pipe_type)
        self._cache[key] = substandard

        return substandard


class StandardProvider(abc.ABC):
    """
    An interface for retrieving the details of a standard.
    """

    @abc.abstractmethod
    def get_by_display_name(self, display_name: str) -> api.models.Standard | None:
        """
        Retrieve the details of a standard by its display name.

        :param display_name: The display name of the standard to retrieve.
        :return: A standard object or None if not found.
        """
        raise NotImplementedError


StandardCache = MutableMapping[str, api.models.Standard]


class APIStandardProvider(StandardProvider):
    _GLOBAL_STANDARD_CACHE = cachetools.TTLCache(maxsize=64, ttl=10 * 60)

    def __init__(
        self,
        api_client: api.AuthenticatedClient,
        cache: StandardCache | None = None,
        max_retry_secs: int = 60,
    ):
        def fetcher_fn(display_name: str) -> api.models.Standard | None:
            return _fetch_standard_by_display_name(display_name, api_client)

        self._cache = self._GLOBAL_STANDARD_CACHE if cache is None else cache
        self._fetcher_fn = api.with_retries(max_retry_secs)(fetcher_fn)

    def get_by_display_name(self, display_name: str) -> api.models.Standard | None:
        if (standard := self._cache.get(display_name)) is not None:
            return standard
        standard = self._fetcher_fn(display_name)
        self._cache[display_name] = standard
        return standard
