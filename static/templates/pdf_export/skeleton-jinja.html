<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inspection Report</title>
    <style>
        html,
        body {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
        }
        

        body {
            display: block;
            background-color: #f0f0f0;
        }

        body p {
            margin-top: 1.5%;
            font-size: 8pt;
        }

        .page {
            width: 210mm;
            height: 297mm;
            padding: 12.7mm;
            margin: 10mm auto;
            background-color: white;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            page-break-after: always;
        }

        .header {
            display: flex;
            flex-direction: column;
            margin-bottom: 1%;
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-bottom {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .vaparlogo {
            width: auto;
            height: 25px;
        }

        .clientlogo {
            width: auto;
            height: 25px;
            margin-left: auto;
            margin-right: 1.5%
        }

        .header h1 {
            margin: 0 0 0.5em 0;
            font-size: 1.1em;
        }

        .header h2 {
            margin: 0;
            font-size: 1.0em;
        }

        .summary-header {
            text-align: center;
        }

        .summary-value {
            font-size: 8pt;
            font-weight: normal;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1.5%;
        }

        th,
        td {
            border: 1px solid #000;
            padding: 2mm;
            text-align: left;
            font-size: 8pt;
        }

        .inspection-notes {
            padding-left: 2mm;
            border: 1px solid #000;
        }

        .defects {
            margin-top: 1.5%;
            margin-bottom: 1.5%;
            border: 1px solid #000;
            flex: 1;
            display: flex;
            padding: 1.5%;
            box-sizing: border-box;
            overflow: hidden;
            align-items: flex-start;
        }

        .pipe {
            position: relative;
            height: 100%;
            overflow: hidden;
            flex: 0 0 auto;
            display: flex;
            justify-content: flex-start;
            width: 11%;
        }

        .pipe img {
            height: 100%;
            width: auto;
        }

        .flowicon {
            position: absolute;
            margin-top: 80%;
            margin-left: 0%;
            height: 10%;
            width: auto;
        }

        .flowicon img {
            display: block;
        }

        .defects-container {
            position: relative;
        }

        .lines-container {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .line {
            position: absolute;
            height: 1px;
            border-top: 1px solid black;
            display: block;
            transform-origin: center left;
            left: 39px;
        }

        .defects-table {
            position: relative;
        }

        .defects tr {
            height: 28px;
        }

        .defects th {
            border: 1px solid transparent;
            font-size: 8pt;
            padding: 0;
            height: 10%;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .defects td {
            border: 1px solid transparent;
            font-size: 6pt;
            padding: 0;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .frames {
            margin-top: 1.5%;
            margin-bottom: 1.5%;
            border: 1px solid #000;
            flex: 1;
            padding: 1.5%;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 1.5%;
        }

        .image {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: flex-start;
            text-align: left;
            background-color: #ffffff;
        }

        .image img {
            max-width: 100%;
            max-height: 80%;
            width: auto;
            height: auto;
            object-fit: contain;
        }

        .image img:empty {
            min-width: 100%;
            min-height: 80%;
            background-color: #ffffff;
            content: " ";
        }

        .image p {
            margin-top: 1%;
            margin-bottom: 8%;
            padding: 0.5%;
            text-align: left;
            font-size: 6pt;
        }

        .footer {
            margin-top: auto;
            margin-bottom: 2%;
        }

        .footer th, .footer td {
            padding: 0.5mm;
            text-align: center;
        }

        .truncate {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .nowrap {
            white-space: nowrap;
        }

        @media print {
            .page {
                margin-top: 0;
                margin-bottom: 0;
            }
            .td {
                print-color-adjust: exact;
            }
        }

        @media screen and (max-width: 768px) {
            .header-bottom {
                flex-direction: column;
                align-items: center;
            }

            .header h2,
            .header p {
                text-align: center;
            }
        }
    </style>
</head>

<body>
    {% for inspection in inspections %}
        {% for page in inspection["pages"] %}
            {% include "page-jinja.html" %}
        {% endfor %}
    {% endfor %}
</body>

</html>