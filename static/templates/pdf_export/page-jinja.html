<div class="page">
    <div class="header">
        <div class="header-top">
            <img class="vaparlogo" src="{{ abs_path }}/vapicon.png">
            {% if inspection["header_data"]["organisation_logo"] != "" %}
                <img class="clientlogo" src="{{ inspection.header_data["organisation_logo"] }}"/>
            {% else %}
                <h2>{{ inspection.header_data["organisation_name"] }}</h2>
            {% endif %}
        </div>
        <div class="header-bottom">
            <h1>Inspection Report</h1>
            <p><strong>Folder Name: </strong>{{inspection.inspection["folder"]["job_name"]}}</p>
        </div>
    </div>

    <table>
        <tr>
            {% if inspection.header_data.client %}
                <th class="summary-header">Client:<br><span class="summary-value">{{inspection.header_data.client}}</span></th>
            {% else %}
                <th class="summary-header">Client:<br><span class="summary-value">{{inspection.header_data["organisation_name"]}}</span></th>
            {% endif %}
            {% if inspection.inspection["legacyId"] %}
                <th class="summary-header">VAPAR ID:<br><span class="summary-value"><a 
                    href="{{ inspection.inspection["url"] }}">{{inspection.inspection["legacyId"]}}</a></span></th>
            {% else %}
                <th class="summary-header">VAPAR ID:<br><span class="summary-value"><a 
                    href="{{ inspection.inspection["url"] }}">{{inspection.inspection["uuid"][:8] ~ "..."}}</a></span></th>
            {% endif %}
            <th class="summary-header">Date:<br><span class="summary-value">{{inspection.inspection["Date"]}}</span></th>
            <th class="summary-header">Pipe Use:<br><span class="summary-value">{% if inspection.inspection["UseOfDrainSewer"] == "SS" %} Sewer {% else %} Storm Water {% endif %}</span></th>
            <th class="summary-header">Material:<br><span class="summary-value">{{inspection.inspection["Material"]}}</span></th>
            <th class="summary-header">Direction:<br><span class="summary-value">{{inspection.inspection["Direction"]}}</span></th>
            <th class="summary-header">Diameter:<br><span class="summary-value">{{inspection.inspection["HeightDiameter"]}}</span></th>
        </tr>
    </table>

    <table>
        <tr>
            <th class="nowrap">Length:</th>
            <td>{{inspection.inspection["LengthSurveyed"]}}</td>
            <th class="nowrap">Address:</th>
            <td class="truncate">{{inspection.inspection["asset"]["LocationStreet"]}}, {{inspection.inspection["asset"]["LocationTown"]}}</td>
            <th class="nowrap">Upload Date:</th>
            <td class="truncate">UTC {{ inspection.header_data.uploaded_time }}</td>
        </tr>
        <tr>
            <th class="nowrap">Asset No:</th>
            <td>{{inspection.inspection["asset"]["AssetID"]}}</td>
            <th class="nowrap">Video File:</th>
            <td><a href="{{ inspection.header_data.video_file_url }}">Download File</a></td>
            <th class="nowrap">File Size:</th>
            <td class="truncate">{{ inspection.inspection["file"]["file_size"] }}</td>
        </tr>
        <tr>
            <th class="nowrap">US MH:</th>
            <td>{{inspection.inspection["asset"]["UpstreamNode"]}}</td>
            <th class="nowrap">Play URL:</th>
            <td><a href="{{ inspection.header_data.play_url }}">Play Video</a></td>
            <th class="nowrap">Uploaded By:</th>
            <td class="truncate">{{ inspection.header_data.uploaded_by_initials }}</td>
        </tr>
        <tr>
            <th class="nowrap">DS MH:</th>
            <td>{{inspection.inspection["asset"]["DownstreamNode"]}}</td>
            <th class="nowrap">Work Order:</th>
            <td>{{inspection.inspection["WorkOrder"]}}</td>
            <td colspan="2"></td>
        </tr>
    </table>

    <div class="inspection-notes">
        <p class="truncate"><strong>Inspection Notes: </strong>{{inspection.inspection["GeneralRemarks"]}}</p>
    </div>

    {% if page["content"] == "defects" %}
        {% include "defects-jinja.html" %}
    {% elif page["content"] == "frames" %}
        {% include "frames-jinja.html" %}
    {% endif %}

    <div class="footer">
        <table>
            <tr>
                <th>STR no def</th>
                <th>STR peak</th>
                <th>STR mean</th>
                <th>STR total</th>
                <th>STR grade</th>
                <th>SER no def</th>
                <th>SER peak</th>
                <th>SER mean</th>
                <th>SER total</th>
                <th>SER grade</th>
            </tr>
            <tr>
                <td>{{ inspection.footer_data.str_no_def }}</td>
                <td>{{ inspection.footer_data.str_peak }}</td>
                <td>{{ inspection.footer_data.str_mean }}</td>
                <td>{{ inspection.footer_data.str_total }}</td>
                {% if inspection.inspection["structuralGrade"] | string in ["1", "2"] %}
                    <td style="background-color: #6cc067 !important;"><strong>{{ inspection.inspection["structuralGrade"] }}</strong></td>
                {% elif inspection.inspection["structuralGrade"] | string == "3" %}
                    <td style="background-color: #f0ad4e !important;"><strong>{{ inspection.inspection["structuralGrade"] }}</strong></td>
                {% elif inspection.inspection["structuralGrade"] | string in ["4", "5"] %}
                    <td style="background-color: #d9534f !important;"><strong>{{ inspection.inspection["structuralGrade"] }}</strong></td>
                {% else %}
                    <td>{{ inspection.inspection["structuralGrade"] }}</td>
                {% endif %}
                <td>{{ inspection.footer_data.ser_no_def }}</td>
                <td>{{ inspection.footer_data.ser_peak }}</td>
                <td>{{ inspection.footer_data.ser_mean }}</td>
                <td>{{ inspection.footer_data.ser_total }}</td>
                {% if inspection.inspection["serviceGrade"] | string in ["1", "2"] %}
                    <td style="background-color: #6cc067 !important;"><strong>{{ inspection.inspection["serviceGrade"] }}</strong></td>
                {% elif inspection.inspection["serviceGrade"] | string == "3" %}
                    <td style="background-color: #f0ad4e !important;"><strong>{{ inspection.inspection["serviceGrade"] }}</strong></td>
                {% elif inspection.inspection["serviceGrade"] | string in ["4", "5"] %}
                    <td style="background-color: #d9534f !important;"><strong>{{ inspection.inspection["serviceGrade"] }}</strong></td>
                {% else %}
                    <td>{{ inspection.inspection["serviceGrade"] }}</td>
                {% endif %}
            </tr>
        </table>
        <p>This report was dynamically generated at <strong>{{inspection.footer_data.generation_date}}.</strong></p>
        <p><strong>PLEASE NOTE: </strong>Changes may have been made on the VAPAR Solutions platform since this report was generated.</p>
        <p style="text-align: center;">Page {{ (inspection["pages"].index(page) + 1) | string }} of {{ inspection["pages"] | length | string }}</p>
    </div>
</div>