<div class="defects">
    <div class="pipe">
        {% if page["pipe"] == "abandoned" %}
            <img src="{{ abs_path }}/pipe-abandoned.png">
        {% elif page["pipe"] == "end" %}
            <img src="{{ abs_path }}/pipe-end.png">
        {% elif page["pipe"] == "middle" %}
            <img src="{{ abs_path }}/pipe-middle.png">
        {% elif page["pipe"] == "start-abandoned" %}
            <img src="{{ abs_path }}/pipe-start-abandoned.png">
        {% elif page["pipe"] == "start-and-end" %}
            <img src="{{ abs_path }}/pipe-start-and-end.png">
        {% elif page["pipe"] == "start" %}
            <img src="{{ abs_path }}/pipe-start.png">
        {% endif %}
        <div class="flowicon">
            {% if inspection.inspection["Direction"] == "Upstream" %}
                <img src="{{ abs_path }}/flowicon-up.png">
            {% elif inspection.inspection["Direction"] == "Downstream" %}
                <img src="{{ abs_path }}/flowicon-down.png">
            {% endif %}
        </div>
    </div>
    <div class="lines-container">
        {% for line in page["lines"] %}
            <div class="line" style="top: {{ line["start"] }}px; width: {{ line["length"] }}px; transform: rotate({{ line["angle"] }}deg)"></div>
        {% endfor %}
    </div>
    <table class="defects-table">
        <tr>
            <th></th>
            <th>Code</th>
            <th>Description</th>
            <th>Details</th>
            <th>Remarks</th>
        </tr>
        {% for row in page["rows"] %}
            <tr>
                <td>{{ row["chainage"] }}</td>
                <td>{{ row["code"] }}</td>
                <td>{{ row["description"] }}</td>
                <td>{{ row["details"] }}</td>
                <td>{{ row["remarks"] }}</td>
            </tr>
        {% endfor %}
    </table>
</div>