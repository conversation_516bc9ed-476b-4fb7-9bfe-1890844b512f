legacy_scoring = dict(
    none={
        "Change of conduit material": 0,
        "Connection": 0,
        "Finish node": 0,
        "Healthy Liner": 0,
        "Inspection (survey) abandoned": 0,
        "Junction": 0,
        "Line of conduit deviates": 0,
        "Loss of vision": 0,
        "Loss_of_vision": 0,
        "No Defect": 0,
        "No_defect": 0,
        "Other": 0,
        "Start node": 0,
        "Start or Finish node _manhole": 0,
        "Start_or_End_Node": 0,
        "Textbox": 0,
        "Title": 0,
        "Unknown": 0,
        "Vermin": 0,
        "Weird or Other": 0,
    },
    ser={
        "Connection - Blocked": 50,
        "Connection - Damaged": 30,
        "Connection - Intruding connection_Large": 50,
        "Connection - Intruding connection_Small": 30,
        "Connection - No_defect": 0,
        "Connection_or_Junction - Blocked": 50,
        "Connection_or_Junction - Damaged": 30,
        "Connection_or_Junction - Intruding connection_Large": 50,
        "Connection_or_Junction - Intruding connection_Small": 30,
        "Connection_or_Junction - No_defect": 0,
        "Debris": 45,
        "Debris - 10_to_20": 30,
        "Debris - 30_to_50": 40,
        "Debris - 60_to_70": 50,
        "Debris - 80_to_100": 60,
        "Debris - Intruding_objects": 60,
        "Debris or Deposits": 45,
        "Debris or Deposits - 10_to_20": 30,
        "Debris or Deposits - 30_to_50": 40,
        "Debris or Deposits - 60_to_70": 50,
        "Debris or Deposits - 80_to_100": 60,
        "Debris or Deposits - Intruding_objects": 60,
        "Defective connection": 40,
        "Defective junction": 40,
        "Deformation": 20,
        "Deposits on the wall and in the invert": 45,
        "Displaced joint": 3.5,
        "Displaced joint - Offset_large": 15,
        "Displaced joint - Offset_small": 2,
        "Displaced Joint - Open Large": 5,
        "Displaced Joint - Open Medium": 2,
        "Displaced Joint - Radial Medium": 2,
        "Displaced joint - Separated_large": 5,
        "Displaced joint - Separated_small": 2,
        "Infiltration": 12.5,
        "Infiltration - Infiltration_Dripping": 5,
        "Infiltration - Infiltration_Gushing": 30,
        "Infiltration - Infiltration_Running": 20,
        "Infiltration - Infiltration_Seeping": 1,
        "Intruding connection": 40,
        "Joining material (seal) intrustion": 60,
        "Joint_displacement": 3.5,
        "Joint_displacement - Offset_large": 15,
        "Joint_displacement - Offset_small": 2,
        "Joint_displacement - Separated_large": 5,
        "Joint_displacement - Separated_small": 2,
        "Obstruction": 45,
        "Obstruction - 10_to_20": 30,
        "Obstruction - 30_to_50": 40,
        "Obstruction - 60_to_70": 50,
        "Obstruction - 80_to_100": 60,
        "Obstruction - Intruding_objects": 60,
        "Roots": 20,
        "Roots - Fine": 1,
        "Roots - Mass 10 to 20%": 1,
        "Roots - Mass_Large": 60,
        "Roots - Mass_Medium": 40,
        "Roots - Small": 1,
        "Roots - Tap_roots": 10,
    },
    str={
        "Breaking": 50,
        "Breaking - Displaced_Large": 80,
        "Breaking - Displaced_Medium": 50,
        "Breaking - Displaced_Small": 40,
        "Breaking - Missing_Large": 100,
        "Breaking - Missing_Medium": 80,
        "Breaking - Missing_Small": 50,
        "Collapse": 165,
        "Cracking": 20,
        "Cracking - Circ_large": 8,
        "Cracking - Circ_small": 4,
        "Cracking - Large": 40,
        "Cracking - Long - Large": 16,
        "Cracking - Long - Small": 8,
        "Cracking - Long_large": 16,
        "Cracking - Long_small": 8,
        "Cracking - Medium": 15,
        "Cracking - Mult - Large": 40,
        "Cracking - Mult_large": 40,
        "Cracking - Mult_small": 20,
        "Cracking - Small": 2,
        "Deformation - Large": 90,
        "Deformation - Medium": 30,
        "Deformation - Small": 2,
        "Displaced bricks": 30,
        "Lining Defective": 40,
        "Lining Defective - Blistered": 20,
        "Lining Defective - Bulge": 20,
        "Lining Defective - Defective connection": 40,
        "Lining Defective - Displaced joint": 20,
        "Lining Defective - Healthy liner": 0,
        "Lining Defective - Infiltration": 20,
        "Lining Defective - Peeling": 20,
        "Lining Defective - Pinhole": 20,
        "Lining Defective - Roots": 20,
        "Lining Defective - Textbox": 0,
        "Lining Defective - Title": 0,
        "Lining Defective - Unknown": 0,
        "Lining Defective - Wrinkled": 20,
        "Soil visible through defect": 60,
        "Surface Damage": 30,
        "Surface Damage - Spalling": 20,
        "Surface damage other": 30,
        "Surface Damage to Concrete": 30,
        "Surface_damage": 30,
        "Surface_damage - Agg_exposed": 10,
        "Surface_damage - Corrosion": 20,
        "Surface_damage - Reo_exposed": 80,
        "Surface_damage - Spalling": 20,
    },
)
